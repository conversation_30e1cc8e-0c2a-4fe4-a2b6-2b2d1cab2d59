/* eslint-disable react-dom/no-missing-iframe-sandbox */
import { API_LOGIN, API_USER } from '@/api'
import { OAuthApi } from '@/api/OAuthApi'
import { god } from '@/god'
import { useNavi } from '@/hooks'
import { setTokenData } from '@/store/userStore'
import { useCheckWXLogin } from '@/utils/auth'
import { Reg } from '@jl-org/tool'
import { Button } from 'antd'
import clsx from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import { memo, useState } from 'react'
import { emailLogin } from '../Login/useLogin'
import { ZhBg } from '../ZhBg'
import { AgreementCheckbox } from './AgreementCheckbox'
import { PhoneInput } from './PhoneInput'
import { VerificationCodeInput } from './VerificationCodeInput'

/**
 * 登录页面组件
 */
export const LoginPageZh = memo(() => {
  const [loginType, setLoginType] = useState<'phone' | 'email' | 'wechat'>('phone')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [countryCode, setCountryCode] = useState('+86')
  const [verificationCode, setVerificationCode] = useState('')

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isAgreementChecked, setIsAgreementChecked] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const [wxQRURL, setWXQRURL] = useState<string>('')

  const to = useNavi()
  useCheckWXLogin(() => to('/p/trend'))

  useEffect(
    () => {
      OAuthApi.getWXQRCode().then((res) => {
        setWXQRURL(res.authorizeUrl)
      })
    },
    [],
  )

  /** 切换登录类型 */
  const toggleLoginType = (type: 'phone' | 'email' | 'wechat') => {
    setLoginType(type)
  }

  /** 发送验证码 */
  const sendVerificationCode = async () => {
    /** 验证手机号 */
    if (!phoneNumber || phoneNumber.length < 11 || !Reg.phone.test(phoneNumber)) {
      god.messageError('请输入有效的手机号')
      return false
    }

    try {
      await API_LOGIN.sendSmsCaptcha(phoneNumber)
      god.messageSuccess('验证码发送成功')
      return true
    }
    catch (error: any) {
      god.messageError(error?.msg || '验证码发送失败')
      return false
    }
  }

  /** 登录处理 */
  const handleLogin = async () => {
    if (loginType === 'email') {
      emailLogin({
        email,
        password,
        onSuccess: () => {
          to('/p/trend')
          god.messageSuccess('登录成功')
        },
        onError: (msg) => {
          god.messageError(msg)
        },
      })
      return
    }

    /** 表单验证 */
    if (!phoneNumber || phoneNumber.length < 11) {
      god.messageError('请输入有效的手机号')
      return
    }

    if (!verificationCode) {
      god.messageError('请输入验证码')
      return
    }

    if (!isAgreementChecked) {
      god.messageError('请阅读并同意用户协议和隐私政策')
      return
    }

    /** 设置加载状态 */
    setIsLoading(true)

    try {
      const res = await API_USER.phoneLoginOrRegister({
        phone: phoneNumber,
        captcha: verificationCode,
      })

      setTokenData(res.token)
      god.messageSuccess('登录成功')
      to('/p/trend')
    }
    catch (error: any) {
      god.messageError(error?.msg || '登录失败')
    }
    finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="relative min-h-screen w-screen flex items-center justify-center">
      <ZhBg className="size-full flex items-center justify-center">
        <div className="mx-auto max-w-5xl w-full flex overflow-hidden rounded-xl bg-white shadow-xl">
          <div
            style={ {
              minHeight: 487,
            } }
            className="w-full flex">
            {/* 左侧二维码区域 */ }
            <div className="flex flex-1 flex-col items-center justify-center gap-10 border-r border-gray-100 p-12">
              {/* <motion.div
                className="text-center"
                initial={ { opacity: 0, y: -20 } }
                animate={ { opacity: 1, y: 0 } }
                transition={ { duration: 0.5 } }
              >
                <h1 className="mb-2 mb-4 text-3xl font-bold">欢迎登录</h1>
                <p className="text-sm text-gray-500">登录Picball，开启您的创作之旅</p>
              </motion.div> */}

              <iframe src={ wxQRURL } frameBorder="0" className="size-full"></iframe>

              {/* <p className="w-full flex-self-end text-center text-sm text-gray-500">
                打开微信扫一扫快速登录
              </p> */}
            </div>

            {/* 右侧手机号登录区域 */ }
            <div className="flex flex-1 flex-col justify-between gap-10 p-12">
              <div className="mb-4 flex gap-4">
                <button
                  className={ clsx(
                    'pb-2 text-lg font-medium',
                    loginType === 'phone'
                      ? 'border-b-[2px] border-primary text-primary'
                      : 'text-gray-500',
                  ) }
                  onClick={ () => toggleLoginType('phone') }
                >
                  手机号登录
                </button>
                <button
                  className={ clsx(
                    'pb-2 text-lg font-medium',
                    loginType === 'email'
                      ? 'border-b-[2px] border-primary text-primary'
                      : 'text-gray-500',
                  ) }
                  onClick={ () => toggleLoginType('email') }
                >
                  邮箱登录
                </button>
              </div>

              <div className="space-y-4">
                { loginType === 'phone'
                  ? (
                      <>
                        <PhoneInput
                          value={ phoneNumber }
                          onChange={ setPhoneNumber }
                          countryCode={ countryCode }
                          onCountryCodeChange={ setCountryCode }
                        />

                        <VerificationCodeInput
                          value={ verificationCode }
                          onChange={ setVerificationCode }
                          onSendCode={ sendVerificationCode }
                        />
                      </>
                    )
                  : (
                      <>
                        <div className="rounded-lg bg-[#F4F9FE] p-4 shadow-sm">
                          <input
                            type="email"
                            className="w-full bg-transparent text-sm outline-none placeholder:text-gray-400"
                            placeholder="请输入邮箱"
                            value={ email }
                            onChange={ e => setEmail(e.target.value) }
                          />
                        </div>
                        <div className="rounded-lg bg-[#F4F9FE] p-4 shadow-sm">
                          <input
                            type="password"
                            className="w-full bg-transparent text-sm outline-none placeholder:text-gray-400"
                            placeholder="请输入密码"
                            value={ password }
                            onChange={ e => setPassword(e.target.value) }
                          />
                        </div>
                      </>
                    ) }

                <AgreementCheckbox
                  checked={ isAgreementChecked }
                  onChange={ setIsAgreementChecked }
                >
                  阅读并同意《用户协议》和《隐私政策》
                </AgreementCheckbox>
              </div>

              <Button
                type="primary"
                onClick={ handleLogin }
                loading={ isLoading }
                className="w-full py-6"
                disabled={ loginType === 'phone'
                  ? !phoneNumber || !verificationCode || !isAgreementChecked
                  : !email || !password || !isAgreementChecked }
              >
                登录
              </Button>
            </div>
          </div>
        </div>
      </ZhBg>
    </div>
  )
})

LoginPageZh.displayName = 'LoginPage'

export default LoginPageZh

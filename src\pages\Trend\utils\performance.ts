/**
 * Trend 模块性能监控工具
 *
 * 功能：
 * - 监控组件渲染性能
 * - 监控 API 调用性能
 * - 监控内存使用情况
 * - 提供性能优化建议
 */

/** 性能监控配置 */
const PERFORMANCE_CONFIG = {
  /** 是否启用性能监控 */
  enabled: import.meta.env.DEV,
  /** 慢渲染阈值（毫秒） */
  slowRenderThreshold: 16,
  /** 慢 API 阈值（毫秒） */
  slowApiThreshold: 1000,
  /** 内存监控间隔（毫秒） */
  memoryCheckInterval: 5000,
}

/** 性能指标类型 */
interface PerformanceMetric {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  type: 'render' | 'api' | 'custom'
  metadata?: Record<string, any>
}

/** 性能监控器 */
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private memoryCheckTimer?: number

  constructor() {
    if (PERFORMANCE_CONFIG.enabled) {
      this.startMemoryMonitoring()
    }
  }

  /** 开始性能测量 */
  start(name: string, type: PerformanceMetric['type'] = 'custom', metadata?: Record<string, any>): string {
    if (!PERFORMANCE_CONFIG.enabled)
      return name

    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      type,
      metadata,
    }

    this.metrics.push(metric)
    return name
  }

  /** 结束性能测量 */
  end(name: string): number | null {
    if (!PERFORMANCE_CONFIG.enabled)
      return null

    const metric = this.metrics.find(m => m.name === name && !m.endTime)
    if (!metric) {
      console.warn(`Performance metric "${name}" not found or already ended`)
      return null
    }

    metric.endTime = performance.now()
    metric.duration = metric.endTime - metric.startTime

    /** 检查是否超过阈值 */
    this.checkThreshold(metric)

    return metric.duration
  }

  /** 检查性能阈值 */
  private checkThreshold(metric: PerformanceMetric) {
    if (!metric.duration)
      return

    let threshold = 0
    let type = ''

    switch (metric.type) {
      case 'render':
        threshold = PERFORMANCE_CONFIG.slowRenderThreshold
        type = '渲染'
        break
      case 'api':
        threshold = PERFORMANCE_CONFIG.slowApiThreshold
        type = 'API 调用'
        break
      default:
        return
    }

    if (metric.duration > threshold) {
      console.warn(
        `🐌 慢${type}检测: ${metric.name} 耗时 ${metric.duration.toFixed(2)}ms (阈值: ${threshold}ms)`,
        metric.metadata,
      )
    }
  }

  /** 获取性能报告 */
  getReport(): {
    totalMetrics: number
    averageRenderTime: number
    averageApiTime: number
    slowOperations: PerformanceMetric[]
  } {
    const completedMetrics = this.metrics.filter(m => m.duration !== undefined)
    const renderMetrics = completedMetrics.filter(m => m.type === 'render')
    const apiMetrics = completedMetrics.filter(m => m.type === 'api')

    const averageRenderTime = renderMetrics.length > 0
      ? renderMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / renderMetrics.length
      : 0

    const averageApiTime = apiMetrics.length > 0
      ? apiMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / apiMetrics.length
      : 0

    const slowOperations = completedMetrics.filter((m) => {
      if (m.type === 'render')
        return (m.duration || 0) > PERFORMANCE_CONFIG.slowRenderThreshold
      if (m.type === 'api')
        return (m.duration || 0) > PERFORMANCE_CONFIG.slowApiThreshold
      return false
    })

    return {
      totalMetrics: completedMetrics.length,
      averageRenderTime,
      averageApiTime,
      slowOperations,
    }
  }

  /** 清除性能数据 */
  clear() {
    this.metrics = []
  }

  /** 开始内存监控 */
  private startMemoryMonitoring() {
    if (typeof window === 'undefined' || !('performance' in window))
      return

    this.memoryCheckTimer = window.setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024)
        const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024)
        const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024)

        /** 如果内存使用超过 80%，发出警告 */
        if (usedMB / limitMB > 0.8) {
          console.warn(`🧠 内存使用过高: ${usedMB}MB / ${limitMB}MB (${Math.round(usedMB / limitMB * 100)}%)`)
        }
      }
    }, PERFORMANCE_CONFIG.memoryCheckInterval)
  }

  /** 停止内存监控 */
  stopMemoryMonitoring() {
    if (this.memoryCheckTimer) {
      clearInterval(this.memoryCheckTimer)
      this.memoryCheckTimer = undefined
    }
  }
}

/** 全局性能监控器实例 */
export const performanceMonitor = new PerformanceMonitor()

/** 性能监控装饰器 */
export function measurePerformance(name: string, type: PerformanceMetric['type'] = 'custom') {
  return function <T extends (...args: any[]) => any>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>,
  ) {
    const originalMethod = descriptor.value!

    descriptor.value = function (...args: any[]) {
      const metricName = `${name || `${target.constructor.name}.${propertyKey}`}`
      performanceMonitor.start(metricName, type)

      try {
        const result = originalMethod.apply(this, args)

        /** 如果返回 Promise，等待完成后再结束测量 */
        if (result instanceof Promise) {
          return result.finally(() => {
            performanceMonitor.end(metricName)
          })
        }

        performanceMonitor.end(metricName)
        return result
      }
      catch (error) {
        performanceMonitor.end(metricName)
        throw error
      }
    } as T

    return descriptor
  }
}

/** React 组件性能监控 Hook */
export function usePerformanceMonitor(componentName: string) {
  if (!PERFORMANCE_CONFIG.enabled) {
    return {
      startRender: () => {},
      endRender: () => {},
      measureApi: (name: string, fn: () => Promise<any>) => fn(),
    }
  }

  const startRender = () => {
    performanceMonitor.start(`${componentName}.render`, 'render')
  }

  const endRender = () => {
    performanceMonitor.end(`${componentName}.render`)
  }

  const measureApi = async <T>(name: string, fn: () => Promise<T>): Promise<T> => {
    const metricName = `${componentName}.${name}`
    performanceMonitor.start(metricName, 'api')

    try {
      const result = await fn()
      performanceMonitor.end(metricName)
      return result
    }
    catch (error) {
      performanceMonitor.end(metricName)
      throw error
    }
  }

  return {
    startRender,
    endRender,
    measureApi,
  }
}

/** 在开发环境下暴露到全局，方便调试 */
if (import.meta.env.DEV && typeof window !== 'undefined') {
  (window as any).__trendPerformance = {
    monitor: performanceMonitor,
    getReport: () => performanceMonitor.getReport(),
    clear: () => performanceMonitor.clear(),
  }
}

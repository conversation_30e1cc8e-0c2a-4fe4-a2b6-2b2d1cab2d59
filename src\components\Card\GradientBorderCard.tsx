import { cn } from '@/utils'
import { memo, useState, useEffect } from 'react'

export const GradientBorderCard = memo((props: GradientBorderCardProps) => {
  const {
    className,
    children,
    borderWidth = 1.5,
    borderGradient = 'linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)',
    showBackgroundOnHover = false,
    hoverBackground = 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)',
    selectedBackground = 'linear-gradient(90deg, rgba(221, 157, 255, 0.15) 0%, rgba(54, 211, 255, 0.15) 100%)',
    selected = false,
    onSelectedChange,
    ...rest
  } = props
  
  const [isHovered, setIsHovered] = useState(false)
  const [isSelected, setIsSelected] = useState(selected)

  useEffect(() => {
    setIsSelected(selected)
  }, [selected])

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (onSelectedChange) {
      const newSelected = !isSelected
      setIsSelected(newSelected)
      onSelectedChange(newSelected)
    }
    rest.onClick?.(e)
  }

  // 根据状态获取内部背景
  const getInnerBackground = () => {
    if (!showBackgroundOnHover) {
      return 'linear-gradient(to right, #fff, #fff)'
    }
    
    if (isSelected) {
      return selectedBackground
    }
    
    if (isHovered) {
      return hoverBackground
    }
    
    // 默认白色背景
    return 'linear-gradient(to right, #fff, #fff)'
  }

  return (
    <div
      className={cn(
        'relative overflow-hidden rounded-2xl transition-all duration-300',
        className
      )}
      style={{
        border: `${borderWidth}px solid transparent`,
        backgroundClip: 'padding-box, border-box',
        backgroundOrigin: 'padding-box, border-box',
        backgroundImage: `${getInnerBackground()}, ${borderGradient}`,
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
      {...rest}
    >
      {children}
    </div>
  )
})

GradientBorderCard.displayName = 'GradientBorderCard'

export type GradientBorderCardProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & {
  children: React.ReactNode
  borderWidth?: number
  borderGradient?: string
  showBackgroundOnHover?: boolean
  hoverBackground?: string
  selectedBackground?: string
  selected?: boolean
  onSelectedChange?: (selected: boolean) => void
}
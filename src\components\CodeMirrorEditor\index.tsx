import { god } from '@/god'
import { useAsyncEffect, useConst } from '@/hooks'
import { cn } from '@/utils'
import { Compartment, EditorState } from '@codemirror/state'
import { EditorView } from '@codemirror/view'
import { copyToClipboard } from '@jl-org/tool'
import { vitesseLight } from 'codemirror-theme-vitesse'
import { memo, useCallback, useEffect, useRef } from 'react'
import { Button } from '../Button'
import { getLanguageExtension } from './tools'

export const CodeMirrorEditor = memo<CodeMirrorEditorProps>((
  {
    style,
    className,
    code,
    language = 'html',
    readOnly = false,
    copyable = true,
    onChange,
  },
) => {
  const languageConf = useConst(new Compartment())
  const readonlyConf = useConst(new Compartment())

  const editorRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<EditorView | null>(null)

  // ======================
  // * Effects
  // ======================
  useAsyncEffect(async () => {
    if (!editorRef.current)
      return

    const lang = await getLanguageExtension(language)

    const state = EditorState.create({
      doc: code,
      extensions: [
        vitesseLight,
        languageConf.of(lang!),
        readonlyConf.of(EditorState.readOnly.of(readOnly)),

        EditorView.updateListener.of((update) => {
          if (update.docChanged && !readOnly) {
            const code = update.state.doc.toString()
            onChange?.(code)
          }
        }),
      ],
    })

    const view = new EditorView({
      state,
      parent: editorRef.current,
    })

    viewRef.current = view

    return () => {
      view.destroy()
      viewRef.current = null
    }
  }, []) // Initialize only once

  // Effect to switch language
  useAsyncEffect(async () => {
    if (viewRef.current) {
      const lang = await getLanguageExtension(language)
      viewRef.current.dispatch({
        effects: languageConf.reconfigure(lang!),
      })
    }
  }, [language])

  // Effect to update code
  useEffect(() => {
    const view = viewRef.current
    if (view && code !== undefined && code !== view.state.doc.toString()) {
      view.dispatch({
        changes: {
          from: 0,
          to: view.state.doc.length,
          insert: code || '',
        },
        selection: { anchor: (code || '').length },
        scrollIntoView: true,
      })
    }
  }, [code])

  useEffect(() => {
    if (viewRef.current) {
      viewRef.current.dispatch({
        effects: readonlyConf.reconfigure(
          EditorState.readOnly.of(readOnly),
        ),
      })
    }
  }, [readOnly, readonlyConf])

  // ======================
  // * Fns
  // ======================
  const handleCopy = useCallback(() => {
    copyToClipboard(code || '').then(() => {
      god.messageSuccess('Copy Success')
    })
  }, [code])

  return (
    <div
      className={ cn(
        'relative h-full border border-gray-200 dark:border-gray-800 rounded-lg overflow-hidden bg-slate-600',
        className,
      ) }
      style={ style }
    >
      { copyable && (
        <Button
          onClick={ handleCopy }
          aria-label="Copy"
          className="absolute right-2 top-2 z-10 h-6 p-1"
          size="sm"
        >
          Copy
        </Button>
      ) }

      <div
        ref={ editorRef }
        className={ cn(
          'CodeMirrorEditorContainer overflow-y-auto overflow-x-hidden h-full',
        ) }
      />
    </div>
  )
})

CodeMirrorEditor.displayName = 'CodeMirrorEditor'

export type CodeMirrorEditorProps = {
  code?: string
  language?: CodeMirrorLanguage
  onChange?: (value: string) => void
  readOnly?: boolean
  copyable?: boolean
}
& Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'>

export type CodeMirrorLanguage = 'html'

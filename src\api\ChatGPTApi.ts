import type { BaseChatParams } from './ChatApi'
import { IS_DEV } from '@/config'
import { extractLinks, http } from '@/utils'

export class ChatGPTApi {
  /**
   * GPT-4 流式对话 (Fetch API + SSE 实现)
   */
  static async streamGpt4(
    { content, onMsg, imgUrl, history = [] }: GptParams,
  ) {
    const { promise, reject, resolve } = Promise.withResolvers<string>()

    const c: Array<ImageUrlContent | TextContent> = [
      {
        text: content,
        type: 'text',
      },
    ]
    if (imgUrl) {
      c.push({
        type: 'image_url',
        image_url: {
          url: imgUrl,
        },
      })
    }

    const messages: Message[] = [
      {
        role: Role.User,
        content: c,
      },
    ]

    let finalData = ''

    try {
      const { promise: ssePromise } = await http.fetchSSE('/app/ai-conversation/stream/chat-4', {
        onMessage: ({ allJson }) => {
          const data = ChatGPTApi.fromRespGetContent(allJson as any[])
          if (data) {
            finalData = data
            onMsg?.(finalData)
          }
        },
        method: 'POST',
        needParseData: true,
        needParseJSON: true,
        body: {
          messages: [
            ...history,
            ...messages,
          ],
        },
      })

      await ssePromise
      resolve(finalData)
    }
    catch (error) {
      reject(error)
    }

    return promise
  }

  static async genImg(
    {
      prompt,
      imageUrl,
    }: {
      prompt: string
      imageUrl: string
    },
    retryCount = 3,
  ): Promise<string[]> {
    if (retryCount <= 0) {
      throw new Error('ChatGPT 生图失败，重试次数耗尽')
    }

    const data = await ChatGPTApi.streamGpt4({
      content: `You must give me a picture!! ${prompt}`,
      imgUrl: imageUrl,
    })

    console.log(`ChatGPT 生图: ${data}`)
    const links = extractLinks(data)
    console.log(`ChatGPT 生图识别 URL: ${links}`)

    const res = IS_DEV
      ? links
      : links.filter(item => item.includes('download'))
    console.log(`ChatGPT 生图过滤 URL: ${res}`)

    if (res.length > 0) {
      return res
    }
    else {
      console.warn(`${prompt}; ${imageUrl}; \n ChatGPT 生图失败，重试中`)
      return ChatGPTApi.genImg({ prompt, imageUrl }, --retryCount)
    }
  }

  static fromRespGetContent(aiResps: AiResp[]) {
    try {
      return aiResps.map(aiResp => aiResp.choices?.[0].delta?.content || '').join('')
    }
    catch (error) {
      return ''
    }
  }
}

export enum Role {
  User = 'user',
  Assistant = 'assistant',
}

export type GptParams = BaseChatParams & {
  onMsg?: (msg: string) => void
  history?: Message[]
}

export type Message = {
  role: Role
  content: Array<ImageUrlContent | TextContent>
}

export type ImageUrlContent = {
  type: 'image_url'
  image_url: { url: string }
}

export type TextContent = {
  type: 'text'
  text: string
}

export type AiResp = {
  id: string
  object: string
  model: string
  created: number
  choices: Choice[]
}

export type Choice = {
  index: number
  delta: Delta
  finish_reason: null
}

export type Delta = {
  role: string
  content: string
}

import type { ModalProps } from 'antd'
import type { CSSProperties, ReactNode } from 'react'
import { Modal } from 'antd'
import classnames from 'clsx'

import styled from 'styled-components'

const RoundedModal = styled(Modal)`
  .ant-modal-content {
    border-radius: 16px;
  }
`

function _CloseModal({
  style,
  className,
  children,
  footer = null,

  ...modalProps
}: CloseModalProps) {
  return <>
    <RoundedModal
      centered
      className={ classnames(className) }
      style={ style }
      footer={ footer }
      closeIcon={ false }
      { ...modalProps }
    >
      { children }
    </RoundedModal>

    {/* { modalProps?.open && <img
      src={ new URL('@/assets/svg/close.svg', import.meta.url).href }
      onClick={ e => modalProps?.onCancel?.(e as any) }
      className={ `fixed z-[2999] top-4 right-4 cursor-pointer text-primary
        text-[30px] hover:opacity-70 transition-opacity duration-300` }
    />} */}
  </>
}

export const CloseModal = memo<CloseModalProps>(_CloseModal)
CloseModal.displayName = 'CloseModal'

export type CloseModalProps = {
  className?: string
  style?: CSSProperties
  children?: ReactNode

  footer?: any
}
& ModalProps

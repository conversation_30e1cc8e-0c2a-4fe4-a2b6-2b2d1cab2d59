import { onMounted } from '@/hooks'
import { KeepAliveRouteCtx } from './KeepAliveRouteCtx'
import { componentMap } from './useKeepAlive'

export function useRouteActive(callback: VoidFunction) {
  const { pathname } = useLocation()
  const { registerActiveEffect, delActiveEffect } = useContext(KeepAliveRouteCtx)

  onMounted(() => {
    registerActiveEffect?.(pathname, callback)

    return () => {
      delActiveEffect(pathname)
    }
  })
}

export function useRouteDeactive(callback: VoidFunction) {
  const { pathname } = useLocation()
  const { registerDeactiveEffect, delDeactiveEffect } = useContext(KeepAliveRouteCtx)

  onMounted(() => {
    registerDeactiveEffect?.(pathname, callback)

    return () => {
      delDeactiveEffect(pathname)
    }
  })
}

export function clearKeepAlive() {
  componentMap.clear()
}

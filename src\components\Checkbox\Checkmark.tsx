import type { MotionProps } from 'framer-motion'
import { cn } from '@/utils'
import { motion } from 'framer-motion'
import React, { memo } from 'react'

export const Checkmark = memo<CheckmarkProps>((
  {
    size = 24,
    strokeWidth = 6,
    borderColor = 'currentColor',
    backgroundColor = 'transparent',
    checkmarkColor = 'currentColor',
    className = '',
    show = true,
    showCircle = true,
    animationDuration = 3,
    animationDelay = 0,
    ...rest
  },
) => {
  const draw = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: (i: number) => ({
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: {
          delay: i * animationDelay,
          type: 'spring',
          duration: animationDuration,
          ease: 'easeInOut',
        },
        opacity: { delay: i * animationDelay, duration: 0.2 },
      },
    }),
  }

  return (
    // @ts-ignore
    <motion.svg
      width={ size }
      height={ size }
      viewBox="0 0 100 100"
      initial="hidden"
      animate={ show
        ? 'visible'
        : 'hidden' }
      className={ cn(
        'outline-none',
        rest.onClick
          ? 'cursor-pointer'
          : '',
        className,
      ) }
      { ...rest }
    >
      <motion.circle
        cx="50"
        cy="50"
        r="40"
        key={ backgroundColor }
        stroke={ borderColor }
        variants={ !showCircle
          ? undefined
          : draw }
        custom={ 0 }
        style={ {
          strokeWidth,
          strokeLinecap: 'round',
          fill: backgroundColor,
        } }
      />
      <motion.path
        d="M30 50L45 65L70 35"
        stroke={ checkmarkColor }
        variants={ draw }
        custom={ 1 }
        style={ {
          strokeWidth,
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'transparent',
          animationDuration: `${animationDuration}s`,
        } }
      />
    </motion.svg>
  )
})

Checkmark.displayName = 'Checkmark'

export type CheckmarkProps = {
  size?: number
  strokeWidth?: number
  borderColor?: string
  backgroundColor?: string
  checkmarkColor?: string
  className?: string
  show?: boolean
  showCircle?: boolean
  animationDuration?: number
  animationDelay?: number
}
& React.SVGProps<SVGSVGElement>
& MotionProps

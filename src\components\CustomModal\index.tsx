import { Modal, ModalProps } from "antd";
import { CSSProperties, FC, useMemo } from "react";

import styles from "./index.module.less";

interface Props extends ModalProps {
  height?: CSSProperties["height"];
}

const CustomModal: FC<Props> = ({ children, height = 100, ...modalProps }) => {
  const style: CSSProperties = useMemo(() => ({ height }), [height]);
  const modalStyles: ModalProps["styles"] = useMemo(
    () => ({
      content: {
        padding: 0,
        height: "100%",
      },
      body: {
        padding: 0,
        height: "100%",
      },
    }),
    [],
  );

  return (
    <Modal
      className={styles["custom-modal"]}
      style={style}
      styles={modalStyles}
      title={null}
      footer={null}
      closable={false}
      maskClosable={false}
      keyboard={false}
      centered
      {...modalProps}
    >
      {children}
    </Modal>
  );
};

export default CustomModal;

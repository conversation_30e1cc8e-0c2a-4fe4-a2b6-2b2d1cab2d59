import type { CSSProperties, ReactNode } from 'react'

import { useT } from '@/hooks'
import { primaryColor } from '@/styles/variable'
import { Typography } from 'antd'
import classnames from 'clsx'

function _Empty({
  style,
  className,
}: EmptyProps) {
  const t = useT()

  return <div className="h-full w-full">
    <img
      className={ classnames(
        'absolute left-0 top-0 right-0 bottom-0 m-auto',
        className,
      ) }
      src={ new URL('@/assets/svg/empty.svg', import.meta.url).href }
      style={ style }
    />

    <Typography.Text
      className="absolute"
      style={ {
        left: '50%',
        top: '70%',
        transform: 'translate(-50%, -50%)',
        color: primaryColor,
      } }>
      { t('layout.no-more') }
    </Typography.Text>

  </div>
}
_Empty.displayName = 'Empty'

export const Empty = memo(_Empty)

export type EmptyProps = {
  className?: string
  style?: CSSProperties
  children?: ReactNode
}

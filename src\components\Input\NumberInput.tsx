import type { ChangeEvent } from 'react'
import { useFormField } from '@/components/Form'
import { cn } from '@/utils'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { forwardRef, memo, useCallback, useState } from 'react'

export const NumberInput = memo<NumberInputProps>(forwardRef<HTMLInputElement, NumberInputProps>((
  props,
  ref,
) => {
  const {
    style,
    className,
    containerClassName,
    size = 'md' as Size,
    label,
    labelPosition = 'top',
    disabled = false,
    readOnly = false,
    error = false,
    errorMessage,
    required = false,
    prefix,
    suffix,
    onFocus,
    onBlur,
    onPressEnter,
    onKeyDown,
    onChange,
    value,
    min: _min,
    max: _max,
    step = 1,
    precision = 0,
    name,
    ...rest
  } = props

  const {
    actualValue,
    actualError,
    actualErrorMessage,
    handleChangeVal,
    handleBlur: handleFieldBlur,
  } = useFormField<string | number, ChangeEvent<HTMLInputElement>, number | undefined>({
    name,
    value,
    error,
    errorMessage,
    onChange,
    defaultValue: '',
  })

  const min = typeof _min === 'string'
    ? Number.parseFloat(_min)
    : _min
  const max = typeof _max === 'string'
    ? Number.parseFloat(_max)
    : _max

  const [isFocused, setIsFocused] = useState(false)

  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value
      if (val === '') {
        handleChangeVal(0, e)
        return
      }

      const allowNegative = min === undefined || min < 0
      const allowDecimal = precision > 0

      let pattern = '\\d*'
      if (allowDecimal) {
        pattern = '\\d*\\.?\\d*'
      }

      if (allowNegative) {
        pattern = `-?${pattern}`
      }

      const regex = new RegExp(`^${pattern}$`)

      if (!regex.test(val)) {
        return
      }

      if ((allowNegative && val === '-') || (allowDecimal && val.endsWith('.'))) {
        handleChangeVal(val as any, e)
      }
      else {
        const num = Number.parseFloat(val)
        if (!Number.isNaN(num)) {
          handleChangeVal(num, e)
        }
      }
    },
    [handleChangeVal, precision, min],
  )

  const handleIncrement = useCallback(() => {
    if (disabled || readOnly)
      return

    const valStr = (actualValue ?? '').toString()
    if (max !== undefined && Number.parseFloat(valStr) >= max)
      return

    let currentValue = Number.parseFloat(valStr)
    if (Number.isNaN(currentValue))
      currentValue = 0

    const newValue = currentValue + (step || 1)
    let clampedValue = newValue
    if (max !== undefined && clampedValue > max)
      clampedValue = max

    const formattedValue = clampedValue.toFixed(precision)
    const numValue = Number(formattedValue)

    const mockEvent = {
      target: { value: String(numValue) },
    } as ChangeEvent<HTMLInputElement>

    handleChangeVal(numValue, mockEvent)
  }, [actualValue, step, disabled, readOnly, max, precision, handleChangeVal])

  const handleDecrement = useCallback(() => {
    if (disabled || readOnly)
      return

    const valStr = (actualValue ?? '').toString()
    if (min !== undefined && Number.parseFloat(valStr) <= min)
      return

    let currentValue = Number.parseFloat(valStr)
    if (Number.isNaN(currentValue))
      currentValue = 0

    const newValue = currentValue - (step || 1)
    let clampedValue = newValue
    if (min !== undefined && clampedValue < min)
      clampedValue = min

    const formattedValue = clampedValue.toFixed(precision)
    const numValue = Number(formattedValue)

    const mockEvent = {
      target: { value: String(numValue) },
    } as ChangeEvent<HTMLInputElement>

    handleChangeVal(numValue, mockEvent)
  }, [actualValue, step, disabled, readOnly, min, precision, handleChangeVal])

  const handleFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true)
    onFocus?.(e)
  }, [onFocus])

  const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false)
    handleFieldBlur()

    let valueToSet: number | undefined
    const valueStr = (actualValue ?? '').toString()

    if (valueStr !== '') {
      if (valueStr === '-' || valueStr.endsWith('.')) {
        valueToSet = undefined
      }
      else {
        let numValue = Number.parseFloat(valueStr)
        if (!Number.isNaN(numValue)) {
          if (min !== undefined && numValue < min)
            numValue = min
          if (max !== undefined && numValue > max)
            numValue = max

          valueToSet = Number(numValue.toFixed(precision))
        }
        else {
          valueToSet = undefined
        }
      }
    }
    else {
      valueToSet = undefined
    }

    const mockEvent = {
      target: { value: valueToSet === undefined
        ? ''
        : String(valueToSet) },
    } as ChangeEvent<HTMLInputElement>
    handleChangeVal(valueToSet ?? 0, mockEvent)

    onBlur?.(e)
  }, [onBlur, actualValue, min, max, precision, handleChangeVal, handleFieldBlur])

  /** 处理键盘事件 */
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    onKeyDown?.(e)

    if (e.key === 'ArrowUp') {
      e.preventDefault()
      handleIncrement()
    }
    else if (e.key === 'ArrowDown') {
      e.preventDefault()
      handleDecrement()
    }
    else if (e.key === 'Enter' && onPressEnter) {
      onPressEnter(e)
    }
  }, [onKeyDown, onPressEnter, handleIncrement, handleDecrement])

  const sizeClasses = {
    sm: 'h-8 text-sm',
    md: 'h-10 text-base',
    lg: 'h-12 text-lg',
  }

  const stepperSize = {
    sm: 14,
    md: 16,
    lg: 18,
  }

  const inputClasses = cn(
    'w-full outline-none bg-transparent text-slate-800 dark:text-slate-300',
    'transition-all duration-200 ease-in-out',
    disabled && 'cursor-not-allowed text-slate-400 dark:text-slate-500',
    readOnly && 'cursor-default',
  )

  const containerClasses = cn(
    'relative w-full flex items-center rounded-xl border',
    sizeClasses[size],
    {
      'border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900': !actualError && !disabled,
      'border-rose-500 hover:border-rose-600 focus-within:border-rose-500': actualError && !disabled,
      'border-slate-200 bg-slate-50 dark:bg-slate-800 text-slate-400 cursor-not-allowed': disabled,
      '': isFocused && !actualError && !disabled,
      'hover:border-slate-400 dark:hover:border-slate-600': !isFocused && !actualError && !disabled,
    },
  )

  const stepperButtonClasses = cn(
    'flex items-center justify-center p-0.5 text-slate-400',
    'hover:text-slate-600 dark:hover:text-slate-300',
    'transition-colors duration-200',
    disabled && 'opacity-50 cursor-not-allowed hover:text-slate-400',
    readOnly && 'opacity-50 cursor-not-allowed hover:text-slate-400',
  )

  const renderInput = () => (
    <div className={ containerClasses }>
      { prefix && (
        <div className="flex items-center justify-center pl-3 text-slate-400">
          { prefix }
        </div>
      ) }
      <input
        ref={ ref }
        value={ actualValue }
        className={ cn(
          inputClasses,
          prefix
            ? 'pl-2'
            : 'pl-3',
          'pr-2', // 为步进按钮留出空间
        ) }
        disabled={ disabled }
        readOnly={ readOnly }
        onFocus={ handleFocus }
        onBlur={ handleBlur }
        onKeyDown={ handleKeyDown }
        onChange={ handleChange }
        inputMode="decimal"
        name={ name }
        { ...rest }
      />
      <div className="mr-1 flex flex-col">
        <button
          type="button"
          className={ stepperButtonClasses }
          onClick={ handleIncrement }
          disabled={ disabled || readOnly || (max !== undefined && Number.parseFloat(actualValue?.toString() || '0') >= max) }
          tabIndex={ -1 }
        >
          <ChevronUp size={ stepperSize[size] } />
        </button>
        <button
          type="button"
          className={ stepperButtonClasses }
          onClick={ handleDecrement }
          disabled={ disabled || readOnly || (min !== undefined && Number.parseFloat(actualValue?.toString() || '0') <= min) }
          tabIndex={ -1 }
        >
          <ChevronDown size={ stepperSize[size] } />
        </button>
      </div>
      { suffix && (
        <div className="flex items-center justify-center pr-3 text-slate-400">
          { suffix }
        </div>
      ) }
    </div>
  )

  return (
    <div
      style={ style }
      className={ cn(
        'NumberInputContainer',
        {
          'flex flex-col gap-1': labelPosition === 'top',
          'flex flex-row items-center gap-2': labelPosition === 'left',
        },
        containerClassName,
      ) }
    >
      { label && (
        <label
          className={ cn(
            'block text-slate-700 dark:text-slate-300',
            {
              'text-sm': size === 'sm',
              'text-base': size === 'md',
              'text-lg': size === 'lg',
              'min-w-24': labelPosition === 'left',
              'text-rose-500': actualError,
            },
          ) }
        >
          { label }
          { required && <span className="ml-1 text-rose-500">*</span> }
        </label>
      ) }
      { renderInput() }
      { actualError && actualErrorMessage && (
        <div className="mt-1 text-sm text-rose-500">
          { actualErrorMessage }
        </div>
      ) }
    </div>
  )
}))

NumberInput.displayName = 'NumberInput'

type Size = 'sm' | 'md' | 'lg'

export type NumberInputProps =
  Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value' | 'size' | 'prefix' | 'type'>
  &
  {
    containerClassName?: string
    size?: Size
    label?: string
    labelPosition?: 'top' | 'left'
    disabled?: boolean
    readOnly?: boolean
    error?: boolean
    errorMessage?: string
    required?: boolean
    prefix?: React.ReactNode
    suffix?: React.ReactNode
    value?: string | number
    /**
     * 最小值
     */
    min?: number | string
    /**
     * 最大值
     */
    max?: number | string
    step?: number
    /**
     * 精度（小数位数）
     * @default 0
     */
    precision?: number
    onChange?: (value: number | undefined, e: ChangeEvent<HTMLInputElement>) => void
    onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void
    onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
    onPressEnter?: (e: React.KeyboardEvent<HTMLInputElement>) => void
  }

import type { FC, PropsWithChildren } from 'react'
import RouteLoading from '@/components/RouteLoading'
import SuspenseContainer from '@/components/SuspenseContainer'
import { getUserInfo, userStore } from '@/store/userStore'
import { useEffect } from 'react'
import { useSnapshot } from 'valtio'

const AuthRoute: FC<PropsWithChildren> = memo(({ children }) => {
  return <SuspenseContainer>{children}</SuspenseContainer>
})

const WithUser: FC<PropsWithChildren> = ({ children }) => {
  const snap = useSnapshot(userStore)

  useEffect(() => {
    getUserInfo()
  }, [])

  return (
    <RouteLoading loading={ !!snap.loading }>
      <AuthRoute>{children}</AuthRoute>
    </RouteLoading>
  )
}

export default memo(WithUser)

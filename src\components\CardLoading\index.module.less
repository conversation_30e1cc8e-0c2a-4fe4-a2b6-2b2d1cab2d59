.loader-box {
  .loader-block,
  .loader-block > div {
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
  }

  .loader-block {
    display: block;
    color: var(--ant-color-primary);
    font-size: 0;
  }

  .loader-block > div {
    display: inline-block;
    float: none;
    background-color: currentcolor;
    border: 0 solid currentcolor;
  }

  .loader-block > div:nth-child(1) {
    position: absolute;
    bottom: 32%;
    left: 18%;
    border-radius: 100%;
    transform-origin: center bottom;
    animation: ball-climbing-dot-jump 0.6s ease-in-out infinite;
  }

  .loader-block > div:not(:nth-child(1)) {
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 0;
    transform: translate(60%, 0);
    animation: ball-climbing-dot-steps 1.8s linear infinite;
  }

  .loader-block > div:not(:nth-child(1)):nth-child(2) {
    animation-delay: 0ms;
  }

  .loader-block > div:not(:nth-child(1)):nth-child(3) {
    animation-delay: -600ms;
  }

  .loader-block > div:not(:nth-child(1)):nth-child(4) {
    animation-delay: -1200ms;
  }
}

@keyframes ball-climbing-dot-jump {
  0% {
    transform: scale(1, 0.7);
  }

  20% {
    transform: scale(0.7, 1.2);
  }

  40% {
    transform: scale(1, 1);
  }

  50% {
    bottom: 125%;
  }

  46% {
    transform: scale(1, 1);
  }

  80% {
    transform: scale(0.7, 1.2);
  }

  90% {
    transform: scale(0.7, 1.2);
  }

  100% {
    transform: scale(1, 0.7);
  }
}

@keyframes ball-climbing-dot-steps {
  0% {
    top: 0;
    right: 0;
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    top: 100%;
    right: 100%;
    opacity: 0;
  }
}

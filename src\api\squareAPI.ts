import type { PageQuery, PagerList } from '@/dts'
import { request } from "@/utils"
import { AssetEnum } from './GenVideoApi'


export class SquareAPI {

  /**
   * @deprecated
   * 收藏状态变更
   */
  static setLike(setStateParams: SetStateParams) {
    return request.put('/app/square/convert/collection-state', setStateParams)
  }

  /**
   * 收藏
   */
  static like(resourceIds: string[]) {
    return request.post('/app/resource/collection/batch-add', {
      resourceIds,
      type: AssetEnum.Image
    })
  }

  /**
   * 取消收藏
   */
  static unLike(resourceIds: string[]) {
    return request.post('/app/resource/collection/batch-delete', { resourceIds })
  }

  /**
   * 置顶状态变更
   */
  static setTop(setStickParams: SetTopParams) {
    return request.put('/app/square/convert/sticky-state', setStickParams)
  }

  static list(params: PageQuery) {
    return request.get('/app/square', {
      params
    })
  }

  static listBy(params: PageQuery<{ sortType?: SortType }>) {
    return request.get<any, PagerList<ListStateItem>>('/app/square/page/image-sort-by-specific-state', {
      params
    })
  }

  /**
   * @deprecated
   * 收藏图片列表
   */
  static listByLike(params: PageQuery): Promise<PagerList<ListItem>> {
    return request.get('/app/square/page/collection-image', {
      params
    })
  }

  /**
   * 收藏图片列表
   */
  static listByLike2(params: PageQuery): Promise<PagerList<ListItem>> {
    return request.get('/app/resource/collection/related-detail', {
      params
    })
  }

  static add(newItem: NewItem) {
    return request.post('/app/square/batch-add', newItem)
  }

  static del(resourceIds: string[]) {
    return request.post(`/app/square/batch-delete`, {
      resourceIds
    })
  }
}


export enum SortType {
  Recommend = 1,
  Newest,
  Hotest
}

export type SetStateParams = {
  resourceIds: string[]
  collectionState: CollectionState
}

export type SetTopParams = {
  resourceIds: string[]
  stickyOnTop: CollectionState
}

export type NewItem = {
  resourceId: string[]
}

export type ListItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  resourceId: string
  name: string
  path: string
  url: string
  previewUrl: string
  width: number
  height: number
}

export type ListStateItem = ListItem & {
  collectionState: CollectionState
  stickyOnTop: StickyState
}

export type Size = {
  width: number | null
  height: number | null
}

export type ListItemDO = ListItem & Size

/** 1是取消，2是收藏 */
export enum CollectionState {
  UnCollection = 1,
  Collection = 2
}

export enum StickyState {
  UnSticky = 1,
  Sticky = 2
}

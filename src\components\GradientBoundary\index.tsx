import type { CSSProperties } from 'react'
import classnames from 'clsx'
import { memo } from 'react'

function _GradientBoundary({
  style,
  className,
  fromColor = '#fff',
  dir = 'left',
}: GradientBoundaryProps) {
  return <div
    className={ classnames(
      'absolute right-0 bottom-0 h-full w-28 pointer-events-none z-50',
      className,
    ) }
    style={ {
      backgroundImage: `linear-gradient(to ${dir}, ${fromColor}, transparent)`,
      ...style,
    } }
  >

  </div>
}

export const GradientBoundary = memo<GradientBoundaryProps>(_GradientBoundary)
GradientBoundary.displayName = 'GradientBoundary'

export type GradientBoundaryProps = {
  className?: string
  style?: CSSProperties
  fromColor?: string
  dir?: 'right' | 'left'
}

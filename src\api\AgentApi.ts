import type { LogEntry } from '@/worker/parseAgent'
import type { SSEData } from '@jl-org/http/dist/typings/types'
import { REQUEST_HEADER_KEY } from '@/config'
import { userStore } from '@/store/userStore'
import { http, request } from '@/utils'
import { deepClone } from '@jl-org/tool'

export class AgentApi {
  /**
   * 创建 agent 任务
   * @param data - 包含 prompt 和可选 images 的对象
   * @param data.prompt - 提示词
   * @param data.images - 图片文件列表 (可选)
   */
  static async createTask(data: { prompt: string, images?: File[] }): Promise<CreateAgentTaskResp> {
    const url = '/app/agent/create-task'
    const formData = new FormData()

    formData.append('prompt', data.prompt)
    if (data.images && data.images.length > 0) {
      data.images.forEach((file) => {
        // OpenAPI spec 中 'images' 是参数名，后端通常期望用这个名字接收数组
        formData.append('images', file)
      })
    }

    return request.post(url, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  }

  /**
   * 流式获取任务实时事件
   * @param taskId - 任务 ID
   */
  static async streamTaskEvents(
    taskId: string,
    onMessage: (data: { logs: readonly LogEntry[], searchRes: string }) => void,
    onError?: (error: any) => void,
  ) {
    //   const url = `/test/stream_log?task_id=${taskId}`
    // // const url = `/api/app/agent/stream/task/events/${taskId}`
    // const token = userStore.token
    // const headers = {
    //   [REQUEST_HEADER_KEY.AUTH]: `Bearer ${token}`,
    // }

    // /** 将headers添加到URL查询参数中 */
    // const urlWithParams = new URL(url, window.location.origin)
    // urlWithParams.searchParams.append(REQUEST_HEADER_KEY.AUTH, headers[REQUEST_HEADER_KEY.AUTH])

    // const eventSource = new EventSource(urlWithParams.toString())
    // eventSource.onmessage = (event) => {
    //   console.log(event.data)
    // }
    // return eventSource

    // const url = `/stream_log?task_id=${taskId}`
    const url = `/app/agent/stream/task/events/${taskId}`

    let searchRes = ''

    const { promise: ssePromise, cancel } = await http.fetchSSE(url, {
      onMessage(data) {
        console.log(data)
        const { logs, searchRes: currentSearch } = splitSearchAndLogs(data.currentJson as readonly LogEntry[])
        searchRes += currentSearch.map(item => item.message).join('\n')
        onMessage({
          logs,
          searchRes,
        })
      },
      // handleData(currentContent) {
      //   currentContent = currentContent.replace(/'/g, '"')
      //   if (currentContent.startsWith('"') && currentContent.endsWith('"')) {
      //     currentContent = currentContent.slice(1, -1)
      //   }

      //   return currentContent
      // },
      onError,
      needParseJSON: true,
      needParseData: true,
      ignoreInvalidDataPrefix: true,
      method: 'GET',
    })

    function isSearchRes(entry: LogEntry) {
      // @ts-ignore
      if (entry?.status?.includes('timeout'))
        return false

      return (
        entry.message && !entry.level
      )
      || (
        entry.message && entry.message.startsWith('Search results')
      )
    }

    function splitSearchAndLogs(entries: readonly LogEntry[]) {
      const logs: LogEntry[] = []
      const searchRes: LogEntry[] = []

      for (const entry of entries) {
        if (isSearchRes(entry)) {
          searchRes.push(entry)
        }
        else {
          /** 拼接上潜在的超时信息 */
          // @ts-ignore
          if (entry.message && (entry.level || entry.status)) {
            logs.push(entry)
          }
        }
      }

      return { logs, searchRes }
    }

    return {
      cancel,
      promise: ssePromise,
    }
  }

  /**
   * 获取任务最终结果（下载文件）
   * @param taskId - 任务 ID
   * @returns Promise<Blob> - 包含文件内容的 Blob 对象
   */
  static async downloadResult(taskId: string): Promise<Blob> {
    const url = `/app/agent/download_result/${taskId}`
    return request.get(url, {
      responseType: 'blob', /** 请求原始二进制数据，并将其包装为 Blob */
    })
  }
}

// ===========================================================================
// Type Definitions
// ===========================================================================

export type CreateAgentTaskResp = {
  /**
   * 任务 ID
   */
  taskId: string
}

import type { CodePreviewMode } from '@/components/CodePreview'
import type { MdEditorRef } from '@/components/MdEditor'
import type { MdToCodePreviewType, ReportStoreType, StateStoreType } from '../../stores'
import type { BiJiData, ReportCodeMap, ReportContentItem } from '../../types'
import { CodePreview } from '@/components/CodePreview'
import { Icon } from '@/components/Icon'
import { KeepAlive } from '@/components/KeepAlive'
import { Loading } from '@/components/Loading'
import { MdEditor } from '@/components/MdEditor'
import { Switch } from '@/components/Switch'
import { god } from '@/god'
import { useAsyncEffect, useUpdateEffect } from '@/hooks'

import { cn } from '@/utils'
import { copyToClipboard } from '@jl-org/tool'
import { motion } from 'framer-motion'
import { BarChart3, Code2, Download, FileText, TrendingUp } from 'lucide-react'
import { memo, useEffect, useMemo, useRef, useState } from 'react'

import { extractHtmlFromVisualizationData, getVisualizationCache, isCompetitorReportCompleted, requestVisualizationStream } from '../../stores/cozeStreamApi'
import { findReportByTitle } from '../../stores/create'
import { marketReportToCode, reportToCode } from '../../stores/fns'
import { formatDuration } from '../../tool'
import { BijiDataPreview } from './BijiDataPreview'
import { BijiListPreview } from './BijiListPreview'
import { DaRenListPreview } from './DaRenListPreview'
import { PhonePreview } from './PhonePreview'

export const ReportContent = memo<ReportContentProps>((
  {
    className,
    style,
    item,
    mdToCodePreview,
    reportStore,
    stateStore,
  },
) => {
  /** 订阅 reportStore 的变化，确保实时更新 */
  const reportSnap = reportStore.use()

  /** 从 store 中获取最新的 item 数据 */
  const currentItem = reportSnap.items.find(storeItem => storeItem.id === item.id) || item
  const { title, id, type, content, meta: metadata } = currentItem

  const [isFullscreen, setIsFullscreen] = useState(false)
  const codeSnap = mdToCodePreview.use()
  const curReportCodeMap = (codeSnap as any)[title || ''] as ReportCodeMap

  /** 格式化文件大小 */
  const formatFileSize = (bytes?: number) => {
    if (!bytes)
      return ''
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / 1024 ** i).toFixed(1)} ${sizes[i]}`
  }

  const mdEditorRef = useRef<MdEditorRef>(null)
  const mdHeaderHeight = 48
  const [codeMode, setCodeMode] = useState<CodePreviewMode>('code')
  const [activeTab, setActiveTab] = useState<'markdown' | 'previewCode'>('markdown')

  /** 可视化相关状态 */
  const [visualizationData, setVisualizationData] = useState<string>('')
  const [processedHtmlData, setProcessedHtmlData] = useState<string>('')
  const [isVisualizationLoading, setIsVisualizationLoading] = useState(false)
  const [visualizationError, setVisualizationError] = useState<string>('')
  const [currentReportType, setCurrentReportType] = useState<string>('')

  /** 获取 mapperStore 用于报告类型识别 */
  const mapperStore = useMemo(() => {
    return reportStore.constructor === Object
      ? { nodeTitleToReportId: {} }
      : (reportStore as any).mapperStore || { nodeTitleToReportId: {} }
  }, [reportStore])

  /** 使用更可靠的报告类型识别方法 */
  const getReportTypeFromWorkflowState = useMemo(() => {
    /** 尝试通过 mapperStore 反向查找 */

    /** 查找哪个 node_title 映射到当前报告ID */
    for (const [nodeTitle, reportId] of Object.entries(mapperStore.nodeTitleToReportId || {})) {
      if (reportId === id) {
        /** 基于 node_title 确定报告类型 */
        if (nodeTitle.includes('insight') || nodeTitle.includes('research_content')) {
          return 'insight_report'
        }
        if (nodeTitle.includes('competitor') || nodeTitle.includes('research_content_1')) {
          return 'competitor_report'
        }
        if (nodeTitle.includes('planning') || nodeTitle.includes('scheme')) {
          return 'planning_report'
        }
      }
    }

    /** 基于 meta.step 和内容特征的组合判断 */
    if (metadata?.step) {
      const step = metadata.step
      const contentText = content || ''

      if (step === 'step1' || step === 'step0') {
        if (contentText.includes('竞品') || contentText.includes('competitor') || contentText.includes('competitive')) {
          return 'competitor_report'
        }
        if (contentText.includes('策划') || contentText.includes('planning') || contentText.includes('marketing')) {
          return 'planning_report'
        }
        return 'insight_report' // 默认为洞察报告
      }
    }

    /** 最后备选：基于标题的简单匹配 */
    if (!title)
      return 'unknown'
    const titleLower = title.toLowerCase()

    if (titleLower.includes('insight') || titleLower.includes('洞察') || titleLower.includes('分析报告')) {
      return 'insight_report'
    }
    if (titleLower.includes('planning') || titleLower.includes('scheme') || titleLower.includes('策划') || titleLower.includes('营销') || titleLower.includes('marketing')) {
      return 'planning_report'
    }
    if (titleLower.includes('competitor') || titleLower.includes('competitive') || titleLower.includes('竞品') || titleLower.includes('竞争') || titleLower.includes('对手')) {
      return 'competitor_report'
    }

    return 'unknown'
  }, [id, title, content, metadata, mapperStore.nodeTitleToReportId])

  /** 判断是否应该显示可视化按钮 */
  const shouldShowVisualizationButton = useMemo(() => {
    return getReportTypeFromWorkflowState !== 'unknown'
  }, [getReportTypeFromWorkflowState])

  /** 从缓存恢复可视化数据 */
  const restoreFromCache = useMemo(() => {
    return (reportType: string) => {
      const cached = getVisualizationCache(reportType)
      if (cached) {
        console.log(`[ReportContent] 从缓存恢复报告类型 ${reportType} 的可视化数据`)
        setVisualizationData(cached.data)
        const extractedHtml = extractHtmlFromVisualizationData(cached.data)
        setProcessedHtmlData(extractedHtml)
        setVisualizationError('')
        return true
      }
      return false
    }
  }, [])

  /** 监听报告类型变化，尝试从缓存恢复或重置状态 */
  useEffect(() => {
    if (getReportTypeFromWorkflowState !== currentReportType && currentReportType !== '') {
      /** 报告类型发生变化 */
      console.log(`[ReportContent] 报告类型变化: ${currentReportType} -> ${getReportTypeFromWorkflowState}`)

      /** 尝试从缓存恢复新报告类型的数据 */
      const restored = restoreFromCache(getReportTypeFromWorkflowState)

      if (restored) {
        /** 成功从缓存恢复，切换到预览模式 */
        setActiveTab('previewCode')
        setCodeMode('preview')
      } else {
        /** 没有缓存，重置状态到初始状态 */
        setVisualizationData('')
        setProcessedHtmlData('')
        setVisualizationError('')
        setActiveTab('markdown') // 重置到markdown模式
      }

      setCurrentReportType(getReportTypeFromWorkflowState)
    }
    else if (currentReportType === '') {
      /** 初始化时设置报告类型并尝试恢复缓存 */
      setCurrentReportType(getReportTypeFromWorkflowState)
      if (getReportTypeFromWorkflowState !== 'unknown') {
        const restored = restoreFromCache(getReportTypeFromWorkflowState)
        if (restored) {
          setActiveTab('previewCode')
          setCodeMode('preview')
        }
      }
    }
  }, [getReportTypeFromWorkflowState, currentReportType, restoreFromCache])

  /** 添加状态来强制重新检查competitor_report状态 */
  const [competitorCheckTrigger, setCompetitorCheckTrigger] = useState(0)

  /** 定期检查competitor_report状态 */
  useEffect(() => {
    const interval = setInterval(() => {
      setCompetitorCheckTrigger(prev => prev + 1)
    }, 1000) // 每秒检查一次

    return () => clearInterval(interval)
  }, [])

  /** 判断可视化按钮是否应该被禁用 */
  const isVisualizationButtonDisabled = useMemo(() => {
    /** 基本禁用条件 */
    if (curReportCodeMap?.mdIsProcessing || isVisualizationLoading) {
      return true
    }

    /** 当competitor_report未完全加载完成时，禁用可视化按钮 */
    return !isCompetitorReportCompleted()
  }, [curReportCodeMap?.mdIsProcessing, isVisualizationLoading, competitorCheckTrigger])

  /** 处理Switch切换到previewCode模式时的可视化请求 */
  const handleSwitchToPreviewCode = async () => {
    if (!shouldShowVisualizationButton || !title || isVisualizationLoading) {
      /** 如果不支持可视化或正在加载中，直接切换到previewCode */
      setActiveTab('previewCode')
      return
    }

    /** 检查是否已有该报告类型的可视化数据 */
    if (processedHtmlData && currentReportType === getReportTypeFromWorkflowState) {
      /** 如果已有当前报告类型的可视化数据，直接切换到预览模式 */
      setActiveTab('previewCode')
      setCodeMode('preview')
      return
    }

    /** 尝试从全局缓存恢复数据 */
    const restored = restoreFromCache(getReportTypeFromWorkflowState)
    if (restored) {
      /** 成功从缓存恢复，直接切换到预览模式 */
      setActiveTab('previewCode')
      setCodeMode('preview')
      return
    }

    /** 立即切换到markdown模式，开始流式输出 */
    setActiveTab('markdown')
    setIsVisualizationLoading(true)
    setVisualizationError('')
    setVisualizationData('')

    try {
      /** 获取实际的报告内容，优先使用content，其次使用title作为标识 */
      const reportContent = content || title || 'unknown_report'

      await requestVisualizationStream(
        reportContent,
        (data) => {
          /** 累积可视化数据，在markdown模式中流式显示 */
          setVisualizationData(prev => prev + data.content)
        },
        (error) => {
          setVisualizationError(error.message || '可视化请求失败')
          god.messageError(`可视化请求失败: ${error.message}`)
          setIsVisualizationLoading(false)
          /** 出错时切换到previewCode模式 */
          setActiveTab('previewCode')
        },
        () => {
          setIsVisualizationLoading(false)
          /** 流式数据接收完成后，延迟处理HTML并切换模式 */
          setTimeout(() => {
            /** 只在这里进行HTML提取，避免流式过程中重复调用 */
            setVisualizationData((currentData) => {
              if (currentData) {
                const extractedHtml = extractHtmlFromVisualizationData(currentData)
                setProcessedHtmlData(extractedHtml)
              }
              return currentData
            })
            /** 切换到previewCode模式显示HTML渲染效果 */
            setActiveTab('previewCode')
            setCodeMode('preview')
          }, 500) // 短暂延迟让用户看到完整的markdown内容
        },
        undefined, // taskInstanceId
        currentItem, // 传入完整的报告项用于更准确的类型识别
        mapperStore, // 传入 mapperStore 用于报告类型识别
      )
    }
    catch (error) {
      setVisualizationError(error instanceof Error
        ? error.message
        : '未知错误')
      setIsVisualizationLoading(false)
      setActiveTab('previewCode')
    }
  }

  useEffect(
    () => {
      const nextVal = curReportCodeMap?.done
        ? 'preview'
        : 'code'

      setCodeMode(nextVal)
    },
    [curReportCodeMap?.done],
  )

  useAsyncEffect(
    async () => {
      if (
        activeTab === 'markdown'
        || !curReportCodeMap
        || curReportCodeMap.isToCoding
        || curReportCodeMap.done
      ) {
        return
      }

      type K = keyof typeof mdToCodePreview
      const keySet = new Set<K>()

      if (['brand_report', 'industry_report'].includes(title || '')) {
        const modifyParams = {
          brand_report: findReportByTitle('brand_report')?.content || '',
          industry_report: findReportByTitle('industry_report')?.content || '',
        }
        if (!modifyParams.brand_report || !modifyParams.industry_report) {
          god.messageError('Please complete the report first')
          return
        }

        await reportToCode(modifyParams, (data) => {
          Object.entries(data).forEach(([k, v]) => {
            const key = k as K
            if (!mdToCodePreview[key]) {
              return
            }

            keySet.add(key);
            (mdToCodePreview[key] as ReportCodeMap).code = v;
            (mdToCodePreview[key] as ReportCodeMap).isToCoding = true
          })
        })

        setCodeMode('preview')
      }
      else if (['brand_market_report'].includes(title || '')) {
        await marketReportToCode(stateStore.cacheAiData.brand_market_report, (code) => {
          const key = 'brand_market_report' as K
          if (!mdToCodePreview[key]) {
            return
          }

          keySet.add(key);
          (mdToCodePreview[key] as ReportCodeMap).code = code;
          (mdToCodePreview[key] as ReportCodeMap).isToCoding = true
        })

        setCodeMode('preview')
      }

      keySet.forEach((key) => {
        (mdToCodePreview[key] as ReportCodeMap).isToCoding = false;
        (mdToCodePreview[key] as ReportCodeMap).done = true
      })
    },
    [activeTab, curReportCodeMap],
    {
      onlyRunInUpdate: true,
    },
  )

  return (
    <motion.div
      className={ cn(
        'ReportContentContainer h-full px-4 relative',
        className,
      ) }
      style={ style }
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.3 } }
      key={ codeMode }
    >
      <KeepAlive active={ type === 'text' }>
        <div className="prose prose-sm max-w-none">
          <p className="whitespace-pre-wrap text-gray-700 leading-relaxed dark:text-gray-300">
            { content || '' }
          </p>
        </div>
      </KeepAlive>

      <KeepAlive active={ type === 'markdown' && activeTab === 'markdown' }>
        <MdEditor
          ref={ mdEditorRef }
          key={ codeMode }
          content={ isVisualizationLoading && visualizationData
            ? visualizationData
            : content || '' }
          className="h-full border-none shadow-none"
          mdClassName="p-0"
          headerHeight={ mdHeaderHeight }
          title={ isVisualizationLoading && visualizationData
            ? `${title || 'Chart'} - 生成中...`
            : title }
          onChange={ (md) => {
            if (!isVisualizationLoading) {
              const target = reportStore.items.find(item => item.id === id)
              if (target) {
                target.content = md
              }
            }
          } }
          renderHeader={ ({ toggleEditMode: _toggleEditMode }) => (
            <div
              style={ {
                height: mdHeaderHeight,
              } }
              className="flex items-center justify-end px-4"
            >
              {/* { !curReportCodeMap?.mdIsProcessing && <div
                className="absolute right-30 cursor-pointer text-sm text-blue-500 transition-all duration-300 -top-9.4 hover:text-blue-600"
                onClick={ toggleEditMode }
              >
                Edit
              </div> } */}

              { shouldShowVisualizationButton && <Switch
                data-testid="visualization-switch"
                disabled={ isVisualizationButtonDisabled }
                checked={ activeTab === 'previewCode' }
                onChange={ (checked) => {
                  if (checked) {
                    /** 切换到previewCode时，先执行可视化请求 */
                    handleSwitchToPreviewCode()
                  }
                  else {
                    /** 切换回markdown时，直接切换 */
                    setActiveTab('markdown')
                  }
                } }
                size="md"
                background="#e5e7eb"
                withGradient={ false }
                checkedIcon={ <BarChart3 size={ 12 } /> }
                uncheckedIcon={
                  isVisualizationLoading
                    ? <div className="h-3 w-3 animate-spin border border-gray-400 border-t-transparent rounded-full" />
                    : <img src={ new URL('@/assets/svg/doc.svg', import.meta.url).href } />
                }
                iconClassName={
                  activeTab === 'markdown'
                    ? 'bg-primary'
                    : 'bg-gradient-to-r from-blue-400 to-purple-500 dark:from-blue-500 dark:to-purple-600 text-white'
                }
              /> }
            </div>
          ) }
        />
      </KeepAlive>

      <KeepAlive active={ type === 'markdown' && activeTab === 'previewCode' }>
        <Loading loading={ isVisualizationLoading || (!processedHtmlData && !curReportCodeMap?.code) }></Loading>

        <CodePreview
          mode={ codeMode }
          className={ cn(
            'h-full overflow-auto',
            isFullscreen && 'fixed inset-2 z-50',
          ) }
          readonly
          copyable={ false }
          code={ processedHtmlData || curReportCodeMap?.code || '' }
          language="html"
          title={ processedHtmlData
            ? `${title || 'Chart'} - 可视化`
            : title || 'Chart' }
          headerHeight={ 48 }
          customHeader={ _props => <div
            style={ {
              height: mdHeaderHeight,
            } }
            className="flex items-center justify-end gap-4 px-4"
          >
            <div
              className="cursor-pointer text-sm text-blue-500 transition-all duration-300 hover:text-blue-600"
              onClick={ () => {
                const codeToCopy = processedHtmlData || curReportCodeMap?.code || ''
                copyToClipboard(codeToCopy).then(() => {
                  god.messageSuccess('Copied to clipboard')
                })
              } }
            >
              Copy
            </div>

            { metadata?.canTransformCode && <Switch
              checked={ activeTab === 'previewCode' }
              onChange={ checked => setActiveTab(checked
                ? 'previewCode'
                : 'markdown') }
              size="md"
              background="#e5e7eb"
              withGradient={ false }
              checkedIcon={ <BarChart3 size={ 12 } /> }
              uncheckedIcon={ <Code2 size={ 12 } /> }
              iconClassName="bg-gradient-to-r from-blue-400 to-purple-500 dark:from-blue-500 dark:to-purple-600 text-white"
            /> }
          </div> }
        />
      </KeepAlive>

      <KeepAlive active={ type === 'image' }>
        <div className="group relative">
          <img
            src={ content }
            alt={ title || '图片' }
            className="h-auto w-full rounded-lg shadow-sm transition-transform duration-300 group-hover:scale-[1.02]"
            loading="lazy"
          />
          { metadata?.description && (
            <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-black/60 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              <p className="px-4 text-center text-sm text-white">
                { metadata.description }
              </p>
            </div>
          ) }
        </div>
      </KeepAlive>

      <KeepAlive active={ type === 'video' }>
        <div className="group relative">
          <video
            src={ content }
            poster={ metadata?.thumbnail }
            controls
            className="h-auto w-full rounded-lg shadow-sm"
            preload="metadata"
          >
            您的浏览器不支持视频播放
          </video>
          { metadata?.duration && (
            <div className="absolute right-2 top-2 rounded bg-black/70 px-2 py-1 text-xs text-white">
              { formatDuration(metadata.duration) }
            </div>
          ) }
        </div>
      </KeepAlive>

      <KeepAlive active={ type === 'file' }>
        <div className="border border-gray-200 rounded-lg p-4 transition-colors duration-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <FileText className="h-8 w-8 text-blue-500 dark:text-blue-400" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="truncate text-sm text-gray-900 font-medium dark:text-gray-200">
                { title || '未命名文件' }
              </p>
              <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                { metadata?.mimeType && (
                  <span className="uppercase">
                    { metadata.mimeType.split('/')[1] }
                  </span>
                ) }
                { metadata?.size && (
                  <span>{ formatFileSize(metadata.size) }</span>
                ) }
              </div>
            </div>

            <Icon asChild>
              <a
                href={ content }
                download={ title }
                target="_blank"
                rel="noopener noreferrer"
              >
                <Download strokeWidth={ 1.5 } size={ 18 } />
              </a>
            </Icon>
          </div>
          { metadata?.description && (
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
              { metadata.description }
            </p>
          ) }
        </div>
      </KeepAlive>

      <KeepAlive active={ type === 'biji_data' }>
        { metadata?.bijiData && <BijiDataPreview data={ metadata.bijiData } /> }
      </KeepAlive>

      <KeepAlive active={ type === 'biji_list' }>
        { metadata?.bijiList && <BijiListPreview data={ metadata.bijiList } stateStore={ stateStore } /> }
      </KeepAlive>

      <KeepAlive active={ type === 'daren_list' }>
        { metadata?.darenList && <DaRenListPreview data={ metadata.darenList } /> }
      </KeepAlive>

      <KeepAlive active={ type === 'phone_preview' }>
        <div className="h-full overflow-auto">
          <PhonePreview
            content={ content }
            title={ title }
            imageUrl={ metadata?.phonePreview?.imageUrl }
            author={ metadata?.phonePreview?.author }
            authorAvatar={ metadata?.phonePreview?.authorAvatar }
            likes={ metadata?.phonePreview?.likes }
            shares={ metadata?.phonePreview?.shares }
            comments={ metadata?.phonePreview?.comments }
            scale={ metadata?.phonePreview?.scale || 0.8 }
          />
        </div>
      </KeepAlive>
    </motion.div>
  )
})

ReportContent.displayName = 'ReportContent'

export type ReportContentProps = {
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 自定义样式
   */
  style?: React.CSSProperties
  /**
   * 标题
   */
  item: ReportContentItem

  mdToCodePreview: MdToCodePreviewType
  reportStore: ReportStoreType
  stateStore: StateStoreType
}

import {
  PageQuery,
  PagerList,
  TemplateImageDO,
} from "@/dts"
import { request } from "@/utils"


export const API_BG_RESOURCE = {

  /**
   * 获取背景库图片公共库
   */
  async getBgLib(
    p: PageQuery<Partial<{ tagIds: string[]; typeIds: string[] }>>,
  ): Promise<PagerList<TemplateImageDO>> {
    const { tagIds, typeIds, ...rest } = p
    const params = { ...rest } as any

    if (tagIds && tagIds.length > 0) params['tagIds'] = tagIds.join(',')
    if (typeIds && typeIds.length > 0) params['typeIds'] = typeIds.join(',')

    return request.get('/app/bg-resource/related-detail', {
      params,
    })
  },

  /**
   * 个人库
   */
  async getPersonalBgLib(params: PageQuery): Promise<PagerList<PersonalRes>> {
    return request.get('/app/bg-resource/private/personal', { params })
  },

  /**
   * 单个背景模板关联细节查询
   */
  async getDetail(id: string): Promise<ResoueceDetail> {
    return request.get(`/app/bg-resource/related-detail/${id}`)
  },
  /**
   * 单个背景个人库关联细节查询
   */
  async getPersonalDetail(id: string): Promise<PersonalDetail> {
    return request.get(`/app/bg-resource/private/personal/${id}`)
  },

  /**
   * 材质详情
   */
  async getMaterialTypeList(params: PageQuery<{ typeId?: string }>): Promise<PagerList<MaterialType>> {
    return request.get('/app/bg-resource/management/related-detail', { params })
  },
}

export type MaterialType = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  url: string
  compressPath: string
  compressUrl: string
  angle: number
  depressionAngle: number
  focalLength: number
  masks: Mask[]
  tags: Tag[]
}

export type PersonalRes = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  url: string
  compressPath: string
  compressUrl: string
  type: 'personal'
}

export type ResoueceDetail = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  url: string
  compressPath: string
  compressUrl: string
  angle: number
  depressionAngle: number
  focalLength: number
  masks: Mask[]
  tags: Tag[]
  type: 'public'
}

export type PersonalDetail = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  url: string
  compressPath: string
  compressUrl: string
  type: 'personal'
}

export type Tag = {
  id: number
  disabled: boolean
  name: string
}

export type Mask = {
  typeId: number
  name: string
  path: string
}

import type { ReactNode } from 'react'
import { motion } from 'framer-motion'

export interface TipButtonProps {
  children: ReactNode
  onClick?: () => void
  showPulse?: boolean
  showBadge?: boolean
  className?: string
  style?: React.CSSProperties
  pulseColor?: string
}

export const TipButton = memo(({
  children,
  onClick,
  showPulse = true,
  showBadge = true,
  className = '',
  style,
  pulseColor = 'rgba(59, 130, 246, 0.15)',
}: TipButtonProps) => {
  return (
    <motion.div
      whileHover={ { scale: 1.02 } }
      whileTap={ { scale: 0.98 } }
      className="relative"
      style={ style }
    >
      { showPulse && (
        <motion.div
          animate={ {
            boxShadow: [
              `0 0 0 0 rgba(59, 130, 246, 0)`,
              `0 0 0 2px rgba(59, 130, 246, 0.05)`,
              `0 0 0 4px rgba(59, 130, 246, 0.1)`,
              `0 0 0 6px ${pulseColor}`,
              `0 0 0 4px rgba(59, 130, 246, 0.1)`,
              `0 0 0 2px rgba(59, 130, 246, 0.05)`,
              `0 0 0 0 rgba(59, 130, 246, 0)`,
            ],
          } }
          transition={ {
            duration: 3,
            repeat: Infinity,
            ease: 'easeInOut',
            repeatType: 'mirror',
            times: [0, 0.2, 0.4, 0.5, 0.6, 0.8, 1],
          } }
          className="rounded-full"
        >
          <button
            onClick={ onClick }
            className={ `group relative flex gap-2 overflow-hidden border border-blue-100 rounded-full bg-white/80 px-4 py-2 text-blue-600 backdrop-blur-sm transition-all hover:bg-blue-50 ${className}` }
          >
            { children }
          </button>
        </motion.div>
      ) }

      { !showPulse && (
        <button
          onClick={ onClick }
          className={ `group relative flex gap-2 overflow-hidden border border-blue-100 rounded-full bg-white/80 px-4 py-2 text-blue-600 backdrop-blur-sm transition-all hover:bg-blue-50 ${className}` }
        >
          { children }
        </button>
      ) }

      { showBadge && (
        <motion.div
          className="absolute h-2 w-2 rounded-full bg-red-500 -right-1 -top-1"
          animate={ {
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5],
          } }
          transition={ {
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
            repeatType: 'mirror',
          } }
        />
      ) }
    </motion.div>
  )
})

TipButton.displayName = 'TipButton'

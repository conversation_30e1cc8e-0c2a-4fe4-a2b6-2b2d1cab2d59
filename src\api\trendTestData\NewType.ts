/**
 * 小红书笔记数据类型定义
 */
export interface XiaohongshuNote {
  /**
   * 笔记标题
   * @example "夏季必备！5款超好用的防晒霜推荐"
   */
  title: string

  /**
   * 笔记阅读量/浏览量
   * @example "125000" - 表示12.5万次浏览
   */
  read: string | number

  /**
   * 总互动量（计算值：点赞+收藏+评论+分享）
   * @example "5842" - 表示5842次总互动
   */
  stat: string | number

  /**
   * 点赞数量
   * @example "3245" - 表示3245个赞
   */
  like: string | number

  /**
   * 收藏数量
   * @example "1876" - 表示1876次收藏
   */
  coll: string | number

  /**
   * 评论数量
   * @example "521" - 表示521条评论
   */
  comm: string | number

  /**
   * 分享数量
   * @example "200" - 表示200次分享
   */
  share: string | number

  /**
   * 发布该笔记的达人昵称
   * @example "美妆小达人Lisa"
   */
  nick: string

  /**
   * 达人当前粉丝总数
   * @example "145000" - 表示14.5万粉丝
   */
  fans: string | number

  /**
   * 笔记内容类型
   * @enum {string}
   * @example "normal" - 图文笔记
   * @example "video" - 视频笔记
   */
  type: 'normal' | 'video'

  /**
   * 笔记详细描述/正文内容（可能包含emoji和换行）
   * @example "夏天到了防晒真的太重要了！\n这几款都是我亲身试用过的好物❤️\n..."
   */
  desc: string

  /**
   * 笔记关联的话题标签列表（多个话题用数组表示）
   * @example ["好物推荐", "防晒测评", "夏日必备"]
   */
  topicList: string[]

  /**
   * 该笔记被推荐的理由（需要详细说明）
   * @example "该笔记展示了5款热门防晒产品的实测效果，互动率高达8%，内容真实可信，达人粉丝粘性强，适合美妆类品牌合作"
   */
  reason: string
}

export type BijiObj = {
  noteLink: string
  info: XiaohongshuNote
}

export type BiJiList = {
  koc: BijiObj[]
  kol: BijiObj[]
  suren: BijiObj[]
}

export type DaRenInfo = {
  /**
   * 达人昵称
   * @example "你要找哪只熊"
   */
  nick: string

  /**
   * 达人简介/个人描述 邮箱
   * @example 一起学化妆叭\\n别搬运我的视频 ‼️❗️\\n📮<EMAIL>
   */
  userText: string

  /**
   * 当前粉丝总数
   * @example "962874"
   */
  fans_count: string

  /**
   * 达人标签（多个标签用逗号分隔）
   * @example "头部达人"
   */
  tagList: string

  /**
   * 达人认证类型
   * @example "头部达人"
   */
  type: string

  /**
   * 近7天新增粉丝数
   * @example "3434"
   */
  fansAdd7: string

  /**
   * 点赞和收藏总数
   * @example "12408992"
   */
  likeCollCount: string

  /**
   * 图文内容的CPE（Cost Per Engagement）成本
   * @description 每互动成本，单位：元
   * @example "0"
   */
  pictureCpe: string

  /**
   * 图文内容的CPM（Cost Per Mille）成本
   * @description 每千次曝光成本，单位：元
   * @example "0"
   */
  pictureCpm: string

  /**
   * 视频内容的CPE（Cost Per Engagement）成本
   * @description 每互动成本，单位：元
   * @example "95"
   */
  videoCpe: string

  /**
   * 视频内容的CPM（Cost Per Mille）成本
   * @description 每千次曝光成本，单位：元
   * @example "10243"
   */
  videoCpm: string

  /**
   * 该达人被推荐的理由
   * @example "头部达人，粉丝基数大，互动率高，适合品牌曝光"
   */
  reason: string
}

export interface DaRenObj {
  anchorLink: string
  info: DaRenInfo
}

export interface DaRenList {
  koc: DaRenObj[]
  kol: DaRenObj[]
  suren: DaRenObj[]
}

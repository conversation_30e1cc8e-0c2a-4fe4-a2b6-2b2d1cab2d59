/**
 * TrendWelcome - Trend页面的Welcome界面组件
 * 与ChatV2页面的Welcome界面保持一致的设计和功能
 */

import type { BlogArticleResp } from '@/api/BlogApi'
import { BlogArticleApi } from '@/api/BlogApi'
import { FileAPI } from '@/api/FileAPI'
import VideoIcon from '@/assets/svg/Video.svg'
import { userStore } from '@/store/userStore'
import { request } from '@/utils'
import { message, Modal } from 'antd'
import cn from 'clsx'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { StreamingText, TypewriterText } from './TypewriterText'

// ==================== 类型定义 ====================

/**
 * 功能按钮配置
 */
export interface QuickActionButton {
  id: string
  label: string
  onClick?: () => void
}

/**
 * 代理图标配置
 */
export interface AgentIcon {
  name: string
  imageSrc: string
  altText?: string
}

/**
 * TrendWelcome组件Props
 */
export interface TrendWelcomeProps {
  // ========== Welcome界面配置 ==========
  welcomeTitle?: string
  welcomeDescription?: string
  welcomeTitleStyle?: React.CSSProperties
  agentIcons?: AgentIcon[]
  quickActionButtons?: QuickActionButton[]
  selectedButton?: string | null
  onButtonSelect?: (buttonId: string) => void

  // ========== 输入区域配置 ==========
  message?: string
  onMessageChange?: (message: string) => void
  uploadedImage?: string | null
  onImageUpload?: (fileOrUrl: File | string) => void
  onImageRemove?: () => void
  urlInputValue?: string
  onUrlInputChange?: (value: string) => void
  inputPlaceholder?: string
  generateButtonText?: string
  generatingButtonText?: string
  onGenerate?: () => void
  isGenerating?: boolean

  // ========== 数据回调 ==========
  onFormDataCollected?: (data: {
    brand_name: string
    product_name: string
    industry_name: string
    competitor_name: string
    platform: string
  }) => void

  onSSEMessagesCollected?: (messages: Array<{
    content: string
    node_title?: string
    timestamp: number
  }>) => void

  onSessionCreated?: (sessionData: any) => void // 新增：会话创建回调
  // ========== 样式配置 ==========
  className?: string
  style?: React.CSSProperties
}

// ==================== 默认配置 ====================

const defaultAgentIcons: AgentIcon[] = [
  { name: 'Research Analyst', imageSrc: '/src/assets/image/home/<USER>' },
  { name: 'Brand Strategist', imageSrc: '/src/assets/image/home/<USER>' },
  { name: 'Creative Director', imageSrc: '/src/assets/image/home/<USER>' },
  { name: 'Operations Manager', imageSrc: '/src/assets/image/home/<USER>' },
]

const defaultQuickActionButtons: QuickActionButton[] = [
  { id: 'create-post', label: 'Create Post' },
  { id: 'generate-image', label: 'Generate Image' },
  { id: 'generate-video', label: 'Generate Video' },
  { id: 'account-analytics', label: 'Account Analytics' },
  { id: 'analyze-trending', label: 'Analyze Trending Post' },
  { id: 'comparison-post', label: 'Comparison Post' },
]

// ==================== 主组件 ====================

/**
 * TrendWelcome 主组件
 */
export const TrendWelcome: React.FC<TrendWelcomeProps> = ({
  // Welcome界面
  welcomeTitle = 'Welcome to PhotoG!',
  welcomeDescription = 'An efficient AI-Marketing team solve all your marketing problems.',
  welcomeTitleStyle = {
    backgroundImage: 'linear-gradient(90deg, #CB6BFF 0%, #05C9FF 100%)',
    fontSize: 'clamp(1.25rem, 3.5vw, 3rem)',
  },
  agentIcons = defaultAgentIcons,
  quickActionButtons = defaultQuickActionButtons,
  selectedButton,
  onButtonSelect,

  /** 输入区域 */
  message = '',
  onMessageChange,
  uploadedImage,
  onImageUpload,
  onImageRemove,
  urlInputValue = '',
  onUrlInputChange,
  inputPlaceholder = 'Enter a description of your brand and product for trend analysis',
  generateButtonText = 'Generate',
  generatingButtonText = 'Generating...',
  onGenerate,
  isGenerating = false,

  /** 数据回调 */
  onFormDataCollected,
  onSSEMessagesCollected,
  onSessionCreated,
  /** 样式 */
  className = '',
  style = {},
}) => {
  // ========== 状态管理 ==========
  const [showExampleModal, setShowExampleModal] = useState(false)
  const [selectedExample, setSelectedExample] = useState<'rednote' | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [playbackSpeed, setPlaybackSpeed] = useState('1x')
  const [resolution, setResolution] = useState('720p')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [videoState, setVideoState] = useState<'default' | 'playing' | 'error' | 'ended'>('default')
  const [videoRef, setVideoRef] = useState<HTMLVideoElement | null>(null)
  const [showResolutionMenu, setShowResolutionMenu] = useState(false)
  const [currentResolution, setCurrentResolution] = useState<'720p' | '1080p'>('720p')
  const resolutionMenuRef = useRef<HTMLDivElement>(null)
  const [isChangingResolution, setIsChangingResolution] = useState(false)
  const [isUploadingImage, setIsUploadingImage] = useState(false)

  /** Thinking 流式渲染相关状态 */
  const [thinkingContent, setThinkingContent] = useState<string>('')
  const [isShowingThinking, setIsShowingThinking] = useState(false)
  const [thinkingModalVisible, setThinkingModalVisible] = useState(false)
  const [isWaitingForThinking, setIsWaitingForThinking] = useState(false) // 新增：等待数据状态
  const [thinkingMessages, setThinkingMessages] = useState<string[]>([]) // 存储所有thinking消息
  const [currentThinkingIndex, setCurrentThinkingIndex] = useState(0) // 当前渲染到第几条

  /** 真实的视频源URL - 这里使用示例，实际应该是不同分辨率的视频 */
  const [videoSources, setVideoSources] = useState({
    '720p': '',
    '1080p': '',
  })

  // ========== 事件处理 ==========

  const handleImageChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      try {
        setIsUploadingImage(true)
        console.log('开始上传图片到 OSS...')

        /** 先创建本地预览 URL */
        const localPreviewUrl = URL.createObjectURL(file)
        if (onImageUpload) {
          onImageUpload(localPreviewUrl)
        }

        /** 立即上传到 OSS 获取 URL */
        const uploadResult = await FileAPI.upFileToUrl([file])
        if (uploadResult?.downloadLoadFileDetails?.[0]?.url) {
          const ossUrl = uploadResult.downloadLoadFileDetails[0].url
          console.log('图片上传成功，OSS URL:', ossUrl)

          /** 清理本地预览 URL */
          URL.revokeObjectURL(localPreviewUrl)

          /** 更新为 OSS URL */
          if (onImageUpload) {
            onImageUpload(ossUrl)
          }

          message.success('图片上传成功')
        }
        else {
          message.error('图片上传失败')
          /** 上传失败时清除图片 */
          if (onImageRemove) {
            onImageRemove()
          }
        }
      }
      catch (error) {
        console.error('图片上传失败:', error)
        message.error('图片上传失败，请重试')
        /** 上传失败时清除图片 */
        if (onImageRemove) {
          onImageRemove()
        }
      }
      finally {
        setIsUploadingImage(false)
      }
    }
  }, [onImageUpload, onImageRemove])

  /** 将文件转换为 base64 */
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = error => reject(error)
    })
  }

  /** 处理Generate按钮点击 - 调用后端接口 */
  const handleGenerateClick = useCallback(async () => {
    if (!message.trim()) {
      message.error('请输入描述内容')
      return
    }

    try {
      /** 处理图片 - uploadedImage 现在应该已经是 OSS URL */
      let imageUrlList: string[] = []

      if (uploadedImage) {
        // uploadedImage 现在应该是一个 http URL（在上传时已经获取）
        if (typeof uploadedImage === 'string' && uploadedImage.startsWith('http')) {
          imageUrlList = [uploadedImage]
          console.log('使用图片 URL:', imageUrlList[0])
        }
        else {
          console.warn('图片格式异常:', uploadedImage)
        }
      }

      /** 准备请求参数 - 将URL拼接到inputText */
      let finalInputText = message
      if (urlInputValue && urlInputValue.trim()) {
        finalInputText = `${message} 这是我的主页链接：${urlInputValue.trim()}`
      }
      
      const requestData = {
        detectedIntent: '', // 🔧 修复：添加缺失的 detectedIntent 参数
        platform: 'rednote', // 🔧 修复：平台名称应该是 'rednote' 而不是 'XHS'
        inputText: finalInputText,
        inputImages: imageUrlList, // 使用 OSS URL
      }

      /** 打印请求信息用于调试 */
      console.log('准备发送请求:')
      console.log('- 请求路径: /app/market/create-session')
      console.log('- baseURL:', import.meta.env.VITE_API_BASE_URL)
      console.log('- 代理目标:', import.meta.env.LOCAL_API_PROXY || '未设置(需要重启服务)')
      console.log('- 实际请求URL:', `${import.meta.env.VITE_API_BASE_URL}/app/market/create-session`)
      console.log('- 请求数据:', requestData)

      /** 设置为加载状态并显示 Thinking Modal */
      if (onGenerate) {
        onGenerate()
      }

      /** 立即显示 Thinking Modal 的等待状态 */
      setThinkingModalVisible(true)
      setIsWaitingForThinking(true)

      /** 添加临时拦截器以调试请求 */
      const interceptorId = request.interceptors.request.use(
        (config) => {
          console.log('Request 实际请求配置:', {
            url: config.url,
            baseURL: config.baseURL,
            fullURL: `${config.baseURL}${config.url}`,
            method: config.method,
            headers: config.headers,
          })
          return config
        },
        (error) => {
          return Promise.reject(error)
        },
      )

      /**
       * 调用后端接口 - request 已经配置了 baseURL=/api
       * 所以实际请求路径是 /api/app/market/create-session
       */
      const response = await request.post('/app/market/create-session', requestData)

      /** 移除临时拦截器 */
      request.interceptors.request.eject(interceptorId)

      console.log('创建会话成功，完整响应:', response)

      /**
       * 获取 taskInstanceId 并调用执行流接口
       * 由于响应拦截器已经处理，response 就是原始响应的 data 字段内容
       */
      if (response?.taskInstanceId) {
        const { taskInstanceId, detectedIntent, intentName, taskStatus, nextStep } = response
        console.log('获取到任务ID:', taskInstanceId)
        console.log('检测到的意图:', detectedIntent, intentName)
        console.log('下一步:', nextStep)

        /** 保存taskInstanceId和detectedIntent到localStorage，供ChatPage使用 */
        localStorage.setItem('taskInstanceId', taskInstanceId)
        localStorage.setItem('detectedIntent', detectedIntent || 'create_post')

        /** 检查 nextStep 或 detectedIntent 是否为 code_0 */
        if (nextStep === 'code_0' || detectedIntent === 'code_0') {
          console.log('Detected code_0, entering AI chat mode', { nextStep, detectedIntent })
          /** 关闭 thinking modal */
          setThinkingModalVisible(false)
          setIsWaitingForThinking(false)
          /** 通知父组件进入 AI 聊天模式 */
          if (onSessionCreated) {
            onSessionCreated(response)
          }
          return // 不继续执行后续流程
        }

        /** 检查 detectedIntent 是否为 code_1 - 直接切换到 ChatPage 的 text_link_disassemble 模式 */
        if (detectedIntent === 'code_1' || nextStep === 'code_1') {
          console.log('Detected code_1, switching to text_link_disassemble workflow', { nextStep, detectedIntent })
          /** 关闭 thinking modal */
          setThinkingModalVisible(false)
          setIsWaitingForThinking(false)

          /** 保存必要的数据到 localStorage */
          localStorage.setItem('userMessage', message)
          if (uploadedImage) {
            localStorage.setItem('uploadedImage', uploadedImage)
          }

          /** 通知父组件切换到 text_link_disassemble 模式 */
          if (onSessionCreated) {
            onSessionCreated({
              ...response,
              needTextLinkDisassemble: true, // 标记需要执行 text_link_disassemble
            })
          }
          return // 不继续执行后续流程
        }
        /** 定义一个函数来处理 SSE 流式请求 */
        const executeSSERequest = async (workflowName: string, parameters: any, stepName: string) => {
          console.log(`\n${'='.repeat(50)}`)
          console.log(`开始执行 ${stepName}`)
          console.log(`工作流: ${workflowName}`)
          console.log('参数:', parameters)
          console.log('='.repeat(50))

          const executeParams = {
            taskInstanceId,
            platform: 'XHS',
            workflowName,
            parameters,
          }

          const token = userStore.token
          const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify(executeParams),
          })

          if (!sseResponse.ok) {
            throw new Error(`SSE request failed for ${workflowName}: ${sseResponse.status}`)
          }

          /** 处理 SSE 流式数据 */
          const reader = sseResponse.body?.getReader()
          const decoder = new TextDecoder()

          if (reader) {
            console.log(`开始接收 ${stepName} 的 SSE 流式数据...`)

            let buffer = '' // 用于处理不完整的数据块
            let messageCount = 0

            while (true) {
              const { done, value } = await reader.read()
              if (done) {
                console.log(`${stepName} 流式数据接收完成，共接收 ${messageCount} 条消息`)
                break
              }

              /** 解码并添加到缓冲区 */
              const chunk = decoder.decode(value, { stream: true })
              buffer += chunk

              /** 按行分割，但保留最后一个可能不完整的行 */
              const lines = buffer.split('\n')
              buffer = lines.pop() || '' // 保留最后一行（可能不完整）

              for (const line of lines) {
                /** 处理空行 */
                if (line.trim() === '') {
                  continue
                }

                /** 处理 SSE 数据行 */
                if (line.startsWith('data: ')) {
                  const data = line.slice(6).trim()

                  try {
                    if (data === '[DONE]') {
                      console.log(`→ ${stepName} SSE 流结束标记 [DONE]`)
                      continue
                    }

                    /** 尝试解析 JSON */
                    const jsonData = JSON.parse(data)
                    messageCount++

                    /** 格式化输出 */
                    console.log(`\n📦 [${stepName}] 消息 #${messageCount}:`)
                    console.log('-'.repeat(40))

                    /** 根据数据类型进行不同的处理 */
                    if (jsonData.type) {
                      console.log(`类型: ${jsonData.type}`)
                    }
                    if (jsonData.content) {
                      console.log(`内容: ${jsonData.content.substring(0, 100)}${jsonData.content.length > 100
                        ? '...'
                        : ''}`)
                    }
                    if (jsonData.node_title) {
                      console.log(`节点: ${jsonData.node_title}`)
                    }
                    if (jsonData.status) {
                      console.log(`状态: ${jsonData.status}`)
                    }

                    /** 打印完整对象（用于调试） */
                    console.log('完整数据:', jsonData)
                  }
                  catch (e) {
                    /** 如果不是 JSON，直接显示原始数据 */
                    console.log(`📝 [${stepName}] 原始 SSE 数据:`, data)
                  }
                }
                else if (line.startsWith('event: ')) {
                  /** 处理 SSE 事件类型 */
                  const eventType = line.slice(7).trim()
                  console.log(`📢 [${stepName}] SSE 事件:`, eventType)
                }
                else if (line.startsWith(':')) {
                  // SSE 注释，通常用于保持连接
                  /** 静默处理，不打印 */
                }
                else {
                  /** 其他未知格式 */
                  console.log(`❓ [${stepName}] 未知格式:`, line)
                }
              }
            }

            /** 处理缓冲区中剩余的数据 */
            if (buffer.trim()) {
              console.log(`⚠️ [${stepName}] 缓冲区剩余数据:`, buffer)
            }
          }

          console.log(`\n${stepName} 执行完成`)
          console.log('='.repeat(50))
        }

        /** 第一步：调用 command_pot */
        console.log('\n🚀 开始执行工作流...')
        await executeSSERequest(
          'command_pot',
          {
            user_submit: message,
            intent_result: detectedIntent, // 使用 detectedIntent 的值
          },
          '第一步: command_pot',
        )

        /** 第二步：调用 text_link_disassemble */
        await executeSSERequest(
          'text_link_disassemble',
          {
            user_submit: message, // 使用用户输入的文本
          },
          '第二步: text_link_disassemble',
        )

        console.log('\n✅ 所有工作流执行完成！')
        message.success('任务执行完成')

        /** 关闭 thinking modal */
        setThinkingModalVisible(false)
        setIsWaitingForThinking(false)
      }
      else {
        console.warn('未获取到 taskInstanceId')
        message.success('会话创建成功')

        /** 关闭 thinking modal */
        setThinkingModalVisible(false)
        setIsWaitingForThinking(false)
      }

      /**
       * 这里可以根据需要处理响应数据
       * 比如跳转到聊天页面或更新状态
       */
    }
    catch (error: any) {
      console.error('创建会话失败:', error)
      console.error('错误详情:', {
        message: error.message,
        response: error.response,
        config: error.config,
      })

      /** 关闭 thinking modal */
      setThinkingModalVisible(false)
      setIsWaitingForThinking(false)

      /** 检查是否是网络错误 */
      if (error.response) {
        message.error(`请求失败: ${error.response.status} - ${error.response.statusText}`)
      }
      else if (error.request) {
        message.error('网络请求失败，请检查网络连接和后端服务')
      }
      else {
        message.error('创建会话失败，请重试')
      }
    }
  }, [message, uploadedImage, urlInputValue, onGenerate, onSessionCreated])

  const handleViewExample = (exampleType: 'rednote', id: number) => {
    setSelectedExample(exampleType)
    setShowExampleModal(true)
    fetchBlogDetail(id)
  }
  const fetchBlogDetail = async (id: number) => {
    const { coverImageOrVideoUrl } = await BlogArticleApi.getBlogDetail(id)
    setVideoSources({
      '720p': coverImageOrVideoUrl,
      '1080p': coverImageOrVideoUrl,
    })
  }
  const handleCloseModal = () => {
    /** 关闭弹窗时停止视频并重置状态 */
    if (videoRef) {
      videoRef.pause()
      videoRef.currentTime = 0
    }
    setVideoState('default')
    setShowExampleModal(false)
    setSelectedExample(null)
    setIsFullscreen(false)
  }

  const handleFullscreen = () => {
    const modalElement = document.querySelector('.example-preview-modal .ant-modal-wrap')
    if (!modalElement)
      return

    if (!isFullscreen) {
      /** 进入全屏 */
      if (modalElement.requestFullscreen) {
        modalElement.requestFullscreen()
      }
      else if ((modalElement as any).webkitRequestFullscreen) {
        (modalElement as any).webkitRequestFullscreen()
      }
      else if ((modalElement as any).mozRequestFullScreen) {
        (modalElement as any).mozRequestFullScreen()
      }
      else if ((modalElement as any).msRequestFullscreen) {
        (modalElement as any).msRequestFullscreen()
      }
      setIsFullscreen(true)
    }
    else {
      /** 退出全屏 */
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
      else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen()
      }
      else if ((document as any).mozCancelFullScreen) {
        (document as any).mozCancelFullScreen()
      }
      else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen()
      }
      setIsFullscreen(false)
    }
  }

  /** 监听全屏状态变化 */
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
    }
  }, [])

  /** 点击外部关闭分辨率菜单 */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (!target.closest('.resolution-menu-container')) {
        setShowResolutionMenu(false)
      }
    }

    if (showResolutionMenu) {
      document.addEventListener('click', handleClickOutside)
      return () => {
        document.removeEventListener('click', handleClickOutside)
      }
    }
  }, [showResolutionMenu])

  const [blogList, setBlogList] = useState<BlogArticleResp[]>([])
  useEffect(() => {
    const fetchBlogList = async () => {
      const result = await BlogArticleApi.publicList({
        type: 4,
        page: 1,
        size: 10,
      })
      setBlogList(result.list)
    }
    fetchBlogList()
  }, [])

  // ========== 渲染 ==========

  return (
    <>
      <style>
        {`
        .fullscreen-modal .ant-modal {
          max-width: 100% !important;
          margin: 0 !important;
          padding: 0 !important;
          top: 0 !important;
        }
        .fullscreen-modal .ant-modal-content {
          height: 100vh !important;
          border-radius: 0 !important;
        }
        .fullscreen-modal .ant-modal-body {
          height: calc(100vh - 55px) !important;
        }
        .fullscreen-modal .ant-modal-wrap {
          overflow: hidden !important;
        }

        /* 自定义滚动条样式 */
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.05);
          border-radius: 4px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #DD9DFF 0%, #36D3FF 100%);
          opacity: 0.1;
          border-radius: 4px;
          transition: all 0.3s ease;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #DD9DFF 0%, #36D3FF 100%);
          opacity: 0.2;
          box-shadow: 0 0 12px rgba(221, 157, 255, 0.3);
        }

        /* Firefox 滚动条样式 */
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: rgba(221, 157, 255, 0.5) rgba(0, 0, 0, 0.0);
        }
      `}
      </style>
      <div className={ cn('flex flex-col overflow-hidden', className) } style={ { height: '100%', backgroundColor: '#fff', ...style } }>
        {/* Welcome界面 - 为 TopBar 预留空间 */}
        <div className="custom-scrollbar flex-1 overflow-y-auto" style={ { paddingTop: '40px', paddingBottom: '20px' } }>
          <div className="mx-auto flex flex-col px-4" style={ { width: '100%', maxWidth: '1600px' } }>
            {/* Welcome 标题和图标区域 */}
            <div className="mb-4 flex flex-col items-center justify-center" style={ { gap: '0.5rem' } }>
              <h1
                className="bg-clip-text text-transparent font-semibold"
                style={ { ...welcomeTitleStyle, fontSize: 'clamp(1.5rem, 3vw, 2.5rem)' } }
              >
                {welcomeTitle}
              </h1>

              {/* 功能图标网格 */}
              <div className="flex items-center justify-center gap-4 lg:gap-12 md:gap-8">
                {agentIcons.map((agent, index) => (
                  <div key={ index } className="flex flex-col items-center gap-1">
                    <div className="h-12 w-12 flex items-center justify-center overflow-hidden rounded-xl lg:h-20 lg:w-20 md:h-16 md:w-16">
                      <img
                        src={ agent.imageSrc }
                        alt={ agent.altText || agent.name }
                        className="h-full w-full object-contain"
                      />
                    </div>
                    <span className="text-center text-xs text-gray-600 md:text-sm">
                      {agent.name}
                    </span>
                  </div>
                ))}
              </div>

              <p className="px-4 text-center text-sm lg:text-lg md:text-base" style={ { color: '#434343' } }>
                {welcomeDescription}
              </p>
            </div>

            {/* 输入区域 */}
            <div className="w-full flex flex-col">
              {/* 输入框区域 */}
              <div className="mb-4 rounded-xl bg-white p-4 shadow-sm" style={ { border: '1px solid #E5E7EB' } }>
                {/* 输入框 */}
                <textarea
                  placeholder={ inputPlaceholder }
                  className={ cn(
                    'w-full resize-none outline-none text-sm md:text-base',
                    isGenerating
                      ? 'text-gray-400 placeholder-gray-300 bg-gray-50 cursor-not-allowed'
                      : 'text-gray-700 placeholder-gray-400',
                  ) }
                  style={ {
                    minHeight: '60px',
                    height: '80px',
                  } }
                  value={ message }
                  onChange={ e => onMessageChange?.(e.target.value) }
                  disabled={ isGenerating }
                />

                {/* Upload上传区域 */}
                <div className="mt-2">
                  <label className="inline-block cursor-pointer">
                    <input
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={ handleImageChange }
                    />
                    {uploadedImage
                      ? (
                          <div
                            className="relative h-16 w-16 overflow-hidden border border-gray-300 rounded-lg md:h-20 md:w-20"
                          >
                            <img src={ uploadedImage } alt="Preview" className="h-full w-full object-cover" />
                            {isUploadingImage && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                                <div className="text-xs text-white">上传中...</div>
                              </div>
                            )}
                            {!isUploadingImage && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 opacity-0 transition-all hover:bg-opacity-30 hover:opacity-100">
                                <button
                                  type="button"
                                  onClick={ (e) => {
                                    e.preventDefault()
                                    onImageRemove?.()
                                  } }
                                  className="h-6 w-6 flex items-center justify-center rounded-full bg-white text-sm text-red-500 font-bold"
                                >
                                  ×
                                </button>
                              </div>
                            )}
                          </div>
                        )
                      : (
                          <div
                            className="h-16 w-16 flex flex-col items-center justify-center gap-1 border border-gray-300 rounded-lg border-dashed bg-gray-50 transition-colors md:h-20 md:w-20 hover:bg-gray-100"
                          >
                            <div
                              className="flex items-center justify-center rounded"
                              style={ { width: '2vw', height: '2vw', minWidth: '20px', minHeight: '20px' } }
                            >
                              <svg
                                style={ { width: '1vw', height: '1vw', minWidth: '10px', minHeight: '10px' } }
                                viewBox="0 0 16 16"
                                fill="none"
                              >
                                <path d="M8 4V12M4 8H12" stroke="#60A5FA" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                            </div>
                            <span className="text-gray-500" style={ { fontSize: 'clamp(0.5rem, 0.8vw, 0.75rem)' } }>Upload</span>
                          </div>
                        )}
                  </label>
                </div>

                {/* 分隔线和其他功能区域 */}
                <div className="mt-3 border-t border-gray-200 pt-3">
                  {/* Add URL输入框 */}
                  <div className="relative" style={ { marginBottom: '1vh' } }>
                    <div className="absolute -translate-y-1/2" style={ { left: '1vw', top: '50%' } }>
                      <svg
                        style={ { width: 'clamp(12px, 1vw, 14px)', height: 'clamp(12px, 1vw, 14px)' } }
                        viewBox="0 0 14 14"
                        fill="none"
                      >
                        <path d="M7 1V13M1 7H13" stroke="#60A5FA" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </div>
                    <input
                      type="text"
                      placeholder="Add URL"
                      value={ urlInputValue }
                      onChange={ e => onUrlInputChange?.(e.target.value) }
                      className="w-full border border-gray-200 rounded-lg outline-none focus:border-blue-400 placeholder-gray-400"
                      style={ {
                        paddingLeft: '2.5vw',
                        paddingRight: '1vw',
                        paddingTop: '0.5vh',
                        paddingBottom: '0.5vh',
                        fontSize: 'clamp(0.75rem, 1vw, 0.875rem)',
                      } }
                    />
                  </div>

                  {/* 快速按钮 */}
                  <div className="flex flex-wrap gap-2">
                    {quickActionButtons.map(button => (
                      <button
                        key={ button.id }
                        className={ cn(
                          'px-3 py-1 text-xs md:text-sm font-bold rounded-lg transition-all relative overflow-hidden',
                          selectedButton === button.id
                            ? 'text-gray-700'
                            : 'text-gray-700 bg-gray-100',
                        ) }
                        onClick={ () => {
                          onButtonSelect?.(button.id)
                          button.onClick?.()
                        } }
                      >
                        {selectedButton === button.id && (
                          <div
                            className="absolute inset-0 opacity-10"
                            style={ { background: 'linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)' } }
                          />
                        )}
                        <span className="relative">{button.label}</span>
                      </button>
                    ))}

                    <button
                      className={ cn(
                        'px-4 py-1 text-xs md:text-sm rounded-full transition-colors ml-auto',
                        (!message.trim() || isGenerating)
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-black text-white hover:bg-gray-800',
                      ) }
                      disabled={ !message.trim() || isGenerating }
                      onClick={ handleGenerateClick }
                    >
                      {isGenerating
                        ? generatingButtonText
                        : generateButtonText}
                    </button>
                  </div>
                </div>
              </div>
              {/* Working with PhotoG 示例展示区域 - 在输入框下方 */}
              <div className="mt-4 pb-4">
                <h3 className="mb-2 text-base font-semibold md:text-lg" style={ { color: '#333' } }>Working with PhotoG</h3>
                <p className="mb-3 text-sm text-gray-600">Here are some examples from us</p>

                {/* 示例卡片响应式网格 - 移动端1列，平板2列，桌面3列 */}
                <div className="grid grid-cols-1 gap-3 lg:grid-cols-3 md:grid-cols-2 md:gap-4">
                  {/* Rednote 示例卡片 1 */}
                  {blogList.map((item) => {
                    return (
                      <div className="border border-gray-200 rounded-lg bg-white p-4" key={ item.id }>
                        <h4 className="mb-2 text-sm font-medium">{item.title}</h4>
                        <p className="mb-3 text-xs text-gray-600" style={ { minHeight: '36px' } }>
                          {item.summary}
                        </p>
                        <p className="mb-3 text-xs text-gray-500">
                          -
                          {item.createUserString}
                        </p>
                        <button
                          onClick={ () => handleViewExample('rednote', item.id) }
                          className="relative inline-flex items-center gap-2 text-xs"
                          style={ {
                            padding: '2px',
                            background: 'linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)',
                            borderRadius: '999px',
                          } }
                        >
                          <span
                            className="flex items-center gap-2 bg-white px-3 py-1 hover:from-[rgba(221,157,255,1)] hover:to-[rgba(54,211,255,1)] hover:bg-gradient-to-r"
                            style={ {
                              borderRadius: '999px',
                            } }
                          >
                            <img src="@/assets/svg/Video.svg" alt="video" className="h-3 w-4" />
                            <span style={ { color: '#434343' } }>View example</span>
                          </span>
                        </button>
                      </div>
                    )
                  })}

                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 预览弹窗 */}
        <Modal
          open={ showExampleModal }
          onCancel={ handleCloseModal }
          width={ isFullscreen
            ? '100%'
            : '95%' }
          style={ { maxWidth: isFullscreen
            ? '100%'
            : '1495px' } }
          bodyStyle={ {
            height: isFullscreen
              ? '100vh'
              : 'calc(95vh - 100px)',
            maxHeight: isFullscreen
              ? '100vh'
              : '841px',
            padding: 0,
          } }
          footer={ null }
          centered
          className="example-preview-modal"
          wrapClassName={ isFullscreen
            ? 'fullscreen-modal'
            : '' }
          title={
            <div className="flex items-center justify-between px-6 py-4">
              <span className="text-lg font-medium"></span>
              <button
                onClick={ handleCloseModal }
                className="text-2xl text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            </div>
          }
          closeIcon={ null }
        >
          {selectedExample === 'rednote' && (
            <div className="relative h-full bg-black">
              {/* 分辨率切换按钮 - 移到右上角 */}
              <div className="resolution-menu-container absolute right-4 top-4 z-50">
                <div className="relative" ref={ resolutionMenuRef }>
                  <button
                    onClick={ (e) => {
                      e.stopPropagation()
                      setShowResolutionMenu(!showResolutionMenu)
                    } }
                    className="flex items-center gap-1 border border-gray-600 rounded bg-black bg-opacity-60 px-2 py-1 text-sm text-white transition-all hover:bg-opacity-80"
                  >
                    <span className="font-medium">{currentResolution}</span>
                    {currentResolution === '1080p' && <span className="text-xs text-blue-400">HD</span>}
                  </button>

                  {/* 分辨率菜单 */}
                  {showResolutionMenu && (
                    <div className="absolute right-0 top-full mt-2 min-w-[120px] overflow-hidden border border-gray-700 rounded-lg bg-gray-900 shadow-xl">
                      <div className="py-1">
                        <button
                          onClick={ (e) => {
                            e.stopPropagation()
                            if (currentResolution !== '720p') {
                              const video = videoRef
                              if (video) {
                                const currentTime = video.currentTime
                                const wasPlaying = !video.paused
                                const volume = video.volume

                                /** 设置正在切换分辨率的标志 */
                                setIsChangingResolution(true)
                                setCurrentResolution('720p')
                                setShowResolutionMenu(false)

                                /** 重新加载视频 */
                                video.load()

                                /** 监听视频加载完成 */
                                const handleLoadedMetadata = () => {
                                  video.currentTime = currentTime
                                  video.volume = volume

                                  if (wasPlaying) {
                                    video.play().then(() => {
                                      setVideoState('playing')
                                    }).catch(console.error)
                                  }

                                  /** 延迟清除标志，确保所有事件处理完成 */
                                  setTimeout(() => {
                                    setIsChangingResolution(false)
                                  }, 500)

                                  video.removeEventListener('loadedmetadata', handleLoadedMetadata)
                                }

                                video.addEventListener('loadedmetadata', handleLoadedMetadata)
                              }
                            }
                            else {
                              setShowResolutionMenu(false)
                            }
                          } }
                          className={ `w-full flex items-center justify-between px-3 py-2 text-left text-sm transition-colors hover:bg-gray-800 ${
                            currentResolution === '720p'
                              ? 'bg-blue-600 bg-opacity-20 text-blue-400'
                              : 'text-white'
                          }` }
                        >
                          <span>720p</span>
                          {currentResolution === '720p' && (
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
                            </svg>
                          )}
                        </button>
                        <button
                          onClick={ (e) => {
                            e.stopPropagation()
                            if (currentResolution !== '1080p') {
                              const video = videoRef
                              if (video) {
                                const currentTime = video.currentTime
                                const wasPlaying = !video.paused
                                const volume = video.volume

                                /** 设置正在切换分辨率的标志 */
                                setIsChangingResolution(true)
                                setCurrentResolution('1080p')
                                setShowResolutionMenu(false)

                                /** 重新加载视频 */
                                video.load()

                                /** 监听视频加载完成 */
                                const handleLoadedMetadata = () => {
                                  video.currentTime = currentTime
                                  video.volume = volume

                                  if (wasPlaying) {
                                    video.play().then(() => {
                                      setVideoState('playing')
                                    }).catch(console.error)
                                  }

                                  /** 延迟清除标志，确保所有事件处理完成 */
                                  setTimeout(() => {
                                    setIsChangingResolution(false)
                                  }, 500)

                                  video.removeEventListener('loadedmetadata', handleLoadedMetadata)
                                }

                                video.addEventListener('loadedmetadata', handleLoadedMetadata)
                              }
                            }
                            else {
                              setShowResolutionMenu(false)
                            }
                          } }
                          className={ `w-full flex items-center justify-between px-3 py-2 text-left text-sm transition-colors hover:bg-gray-800 ${
                            currentResolution === '1080p'
                              ? 'bg-blue-600 bg-opacity-20 text-blue-400'
                              : 'text-white'
                          }` }
                        >
                          <div className="flex items-center gap-2">
                            <span>1080p</span>
                            <span className="text-xs text-gray-400">HD</span>
                          </div>
                          {currentResolution === '1080p' && (
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 真实的视频播放器 */}
              <video
                ref={ ref => setVideoRef(ref) }
                className="h-full w-full object-contain"
                controls
                controlsList="nodownload"
                onLoadStart={ () => {
                /** 不再显示loading页面 */
                } }
                onCanPlay={ () => {
                /** 视频可以播放时，保持当前状态 */
                } }
                onPlay={ () => {
                /** 播放时立即设置为playing状态 */
                  setVideoState('playing')
                } }
                onPause={ () => {
                /** 暂停时检查状态 */
                  if (!isChangingResolution && videoRef) {
                  /** 如果不是拖动进度条且视频没结束 */
                    if (!videoRef.seeking && !videoRef.ended && videoState === 'playing') {
                      setVideoState('default')
                    }
                  }
                } }
                onSeeking={ () => {
                /** 拖动进度条时不改变状态 */
                } }
                onSeeked={ () => {
                /** 拖动结束后不自动改变状态 */
                } }
                onError={ () => {
                  setVideoState('error')
                } }
                onWaiting={ () => {
                /** 缓冲时不显示loading页面，让视频自带的加载指示器处理 */
                } }
                onPlaying={ () => {
                /** 视频正在播放时确保状态正确 */
                  if (videoState !== 'playing') {
                    setVideoState('playing')
                  }
                } }
                onEnded={ () => {
                  setVideoState('ended')
                } }
              >
                {/* 根据分辨率选择不同的视频源 */}
                <source
                  key={ `mp4-${currentResolution}` }
                  src={ videoSources[currentResolution] }
                  type="video/mp4"
                />
                您的浏览器不支持 HTML5 视频。
              </video>
              {/* 默认页面覆盖层 */}
              {videoState === 'default' && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-60">
                  <div
                    className="relative max-w-2xl border border-blue-400 border-opacity-50 rounded-lg p-8"
                    style={ {
                      background: 'linear-gradient(135deg, rgba(30, 58, 95, 0.95) 0%, rgba(15, 30, 55, 0.95) 100%)',
                      boxShadow: '0 0 50px rgba(59, 130, 246, 0.3)',
                    } }>
                    {/* 标题区域 */}
                    <div className="mb-6 flex items-start justify-between">
                      <div>
                        <h2 className="mb-2 text-3xl text-white font-bold">Insight Analyst</h2>
                        <p className="text-sm text-blue-300">
                          Market Decoder | Reveal Blue Oceans
                          <br />
                          Through Data Microscopy
                        </p>
                      </div>
                      <span className="rounded-full bg-blue-500 px-4 py-2 text-sm text-white font-medium">Data Expert</span>
                    </div>

                    {/* 内容区域 */}
                    <div className="text-white space-y-5">
                      <div>
                        <h3 className="mb-3 text-lg font-semibold">Core Features</h3>
                        <ul className="text-sm text-gray-300 space-y-2">
                          <li className="flex items-start">
                            <span className="mr-2 text-blue-400">✓</span>
                            <span>Monitoring 230M global product price fluctuations with 72-hour preemptive alerts</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2 text-blue-400">✓</span>
                            <span>Market prediction models with confidence intervals (accuracy ≥89%)</span>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h3 className="mb-3 text-lg font-semibold">Tech Highlights</h3>
                        <ul className="text-sm text-gray-300 space-y-2">
                          <li className="flex items-start">
                            <span className="mr-2 text-blue-400">✓</span>
                            <span>Sentiment analysis engine mapping consumer comment hotspots</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2 text-blue-400">✓</span>
                            <span>3D market penetration visualization maps</span>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h3 className="mb-3 text-lg font-semibold">Use Case</h3>
                        <p className="text-sm text-gray-300 italic">
                          "A cosmetics brand identified mid-range market gaps via competitor pricing analysis,
                          <br />
                          achieving 17% market share growth in 3 months"
                        </p>
                      </div>
                    </div>

                    {/* 按钮 */}
                    <button className="mt-6 flex items-center gap-2 rounded-lg bg-blue-500 px-6 py-3 text-white font-medium transition-colors hover:bg-blue-600">
                      Unlock Your Industry Radar Map
                      <span>→</span>
                    </button>
                  </div>

                  {/* 中央播放按钮 */}
                  <button
                    onClick={ () => {
                      if (videoRef) {
                        videoRef.play()
                      /** 不要在这里设置状态，让 onPlay 事件处理 */
                      }
                    } }
                    className="absolute inset-0 flex items-center justify-center bg-transparent transition-opacity hover:bg-black hover:bg-opacity-30"
                  >
                    <div className="h-20 w-20 flex items-center justify-center rounded-full bg-white bg-opacity-90 transition-all hover:bg-opacity-100">
                      <svg width="32" height="32" viewBox="0 0 24 24" fill="black">
                        <path d="M8 5v14l11-7z" />
                      </svg>
                    </div>
                  </button>
                </div>
              )}

              {/* 加载中页面 - 已移除 */}

              {/* 错误页面 */}
              {videoState === 'error' && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80">
                  <div className="text-center">
                    <div className="mx-auto mb-4 h-20 w-20 flex items-center justify-center rounded-full bg-red-500 bg-opacity-20">
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#ef4444" strokeWidth="2">
                        <path d="M12 9v4m0 4h.01M5.07 19L5 19.07A10 10 0 1 1 19 5l.07.07A10 10 0 0 1 5.07 19z" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </div>
                    <h3 className="mb-2 text-xl text-white">Video Load Failed</h3>
                    <p className="mb-4 text-sm text-gray-400">Unable to load the video. Please try again.</p>
                    <button
                      onClick={ () => {
                        if (videoRef) {
                          videoRef.load()
                          setVideoState('loading')
                        }
                      } }
                      className="mx-auto flex items-center gap-2 rounded-lg bg-blue-500 px-6 py-2 text-white transition-colors hover:bg-blue-600"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" />
                      </svg>
                      <span>Refresh</span>
                    </button>
                  </div>
                </div>
              )}

              {/* 视频播放结束后的刷新页面 */}
              {videoState === 'ended' && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-60">
                  <div
                    className="relative max-w-2xl border border-blue-400 border-opacity-50 rounded-lg p-8"
                    style={ {
                      background: 'linear-gradient(135deg, rgba(30, 58, 95, 0.95) 0%, rgba(15, 30, 55, 0.95) 100%)',
                      boxShadow: '0 0 50px rgba(59, 130, 246, 0.3)',
                    } }>
                    {/* 标题区域 */}
                    <div className="mb-6 flex items-start justify-between">
                      <div>
                        <h2 className="mb-2 text-3xl text-white font-bold">Insight Analyst</h2>
                        <p className="text-sm text-blue-300">
                          Market Decoder | Reveal Blue Oceans
                          <br />
                          Through Data Microscopy
                        </p>
                      </div>
                      <span className="rounded-full bg-blue-500 px-4 py-2 text-sm text-white font-medium">Competitive Analysis</span>
                    </div>

                    {/* 内容区域 */}
                    <div className="text-white space-y-5">
                      <div>
                        <h3 className="mb-3 text-lg font-semibold">Core Features</h3>
                        <ul className="text-sm text-gray-300 space-y-2">
                          <li className="flex items-start">
                            <span className="mr-2 text-blue-400">✓</span>
                            <span>Monitoring 230M global product price fluctuations with 72-hour preemptive alerts</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2 text-blue-400">✓</span>
                            <span>Market prediction models with confidence intervals (accuracy ≥89%)</span>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h3 className="mb-3 text-lg font-semibold">Tech Highlights</h3>
                        <ul className="text-sm text-gray-300 space-y-2">
                          <li className="flex items-start">
                            <span className="mr-2 text-blue-400">✓</span>
                            <span>Sentiment analysis engine mapping consumer comment hotspots</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2 text-blue-400">✓</span>
                            <span>3D market penetration visualization maps</span>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h3 className="mb-3 text-lg font-semibold">Use Case</h3>
                        <p className="text-sm text-gray-300 italic">
                          "A cosmetics brand identified mid-range market gaps via competitor pricing analysis,
                          <br />
                          achieving 17% market share growth in 3 months"
                        </p>
                      </div>
                    </div>

                    {/* 按钮 */}
                    <button className="mt-6 flex items-center gap-2 rounded-lg bg-blue-500 px-6 py-3 text-white font-medium transition-colors hover:bg-blue-600">
                      Unlock Your Industry Radar Map
                      <span>→</span>
                    </button>
                  </div>

                  {/* 中央刷新按钮 - 带循环箭头 */}
                  <button
                    onClick={ () => {
                      if (videoRef) {
                        videoRef.currentTime = 0
                        /** 直接播放，让视频事件处理状态 */
                        videoRef.play()
                      }
                    } }
                    className="absolute inset-0 flex items-center justify-center bg-transparent transition-opacity hover:bg-black hover:bg-opacity-30"
                  >
                    <div className="h-20 w-20 flex items-center justify-center border-2 border-white border-opacity-50 rounded-full bg-white bg-opacity-10 transition-all hover:bg-opacity-20">
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="white">
                        <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z" />
                      </svg>
                    </div>
                  </button>
                </div>
              )}
            </div>
          )}
        </Modal>

        {/* Thinking Loading Overlay - 全屏遮罩层 */}
        {thinkingModalVisible && isWaitingForThinking && (
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* 半透明背景遮罩 */}
            <div className="absolute inset-0 bg-black/40 backdrop-blur-sm" />

            {/* Loading 内容 */}
            <div className="relative z-10 flex flex-col items-center">
              {/* 三个点的动画 loading */}
              <div className="mb-4 flex space-x-2">
                <div className="h-3 w-3 animate-bounce rounded-full bg-white" style={ { animationDelay: '0ms' } }></div>
                <div className="h-3 w-3 animate-bounce rounded-full bg-white" style={ { animationDelay: '150ms' } }></div>
                <div className="h-3 w-3 animate-bounce rounded-full bg-white" style={ { animationDelay: '300ms' } }></div>
              </div>
              <p className="text-lg text-white font-medium">Thinking...</p>
            </div>
          </div>
        )}

        {/* Thinking Content Modal - 只在有内容时显示 */}
        {thinkingModalVisible && !isWaitingForThinking && thinkingContent && (
          <Modal
            title="AI Thinking"
            open
            onCancel={ () => {
              setThinkingModalVisible(false)
              setIsShowingThinking(false)
              setIsWaitingForThinking(false)
              setThinkingContent('')
              setThinkingMessages([])
              setCurrentThinkingIndex(0)
            } }
            footer={ null }
            width={ 800 }
            centered
            bodyStyle={ { maxHeight: '60vh', overflowY: 'auto', padding: '20px' } }
          >
            <StreamingText
              text={ thinkingContent }
              speed={ 5 }
              className="whitespace-pre-wrap text-gray-700"
              style={ { lineHeight: 1.6, fontSize: '14px' } }
            />
          </Modal>
        )}
      </div>
    </>
  )
}

export default TrendWelcome

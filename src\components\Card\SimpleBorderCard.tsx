import { cn } from '@/utils'
import { memo, useState } from 'react'

export const SimpleBorderCard = memo((props: SimpleBorderCardProps) => {
  const {
    className,
    children,
    borderColor = '#DD9DFF',
    showBackgroundOnHover = false,
    selected = false,
    onSelectedChange,
    ...rest
  } = props
  
  const [isHovered, setIsHovered] = useState(false)
  const [isSelected, setIsSelected] = useState(selected)

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (onSelectedChange) {
      const newSelected = !isSelected
      setIsSelected(newSelected)
      onSelectedChange(newSelected)
    }
    rest.onClick?.(e)
  }

  // 根据状态获取背景样式
  const getBackgroundStyle = () => {
    if (!showBackgroundOnHover) return undefined
    
    if (isHovered || isSelected) {
      return 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)'
    }
    
    return undefined
  }

  return (
    <div
      className={cn(
        'relative overflow-hidden rounded-2xl transition-all duration-300',
        className
      )}
      style={{
        border: `1.5px solid ${borderColor}`,
        background: getBackgroundStyle(),
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
      {...rest}
    >
      {children}
    </div>
  )
})

SimpleBorderCard.displayName = 'SimpleBorderCard'

export type SimpleBorderCardProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & {
  children: React.ReactNode
  borderColor?: string
  showBackgroundOnHover?: boolean
  selected?: boolean
  onSelectedChange?: (selected: boolean) => void
}
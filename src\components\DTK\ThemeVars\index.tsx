import { Table, Typography, message, theme } from "antd";
import type { FC } from "react";

import { useAppSelector, useTheme } from "@/hooks";

const { defaultAlgorithm, defaultSeed } = theme;

const mapToken = defaultAlgorithm(defaultSeed);

type TableRecord = Record<"name" | "value" | "key", string>;

const columns = [
  {
    title: "name",
    dataIndex: "name",
    width: 220,
    render: (name: string, { value }: TableRecord) => {
      const copyStr = `var(--ant-${getKebabCase(name)})`;

      const isColor =
        typeof value === "string" &&
        ["#", "rgb"].some((s) => 0 === value.indexOf(s));
      const isRadius =
        typeof name === "string" && -1 < name.toLowerCase().indexOf("radius");
      return (
        <>
          {isColor && (
            <span
              style={{
                display: "inline-block",
                width: "20px",
                height: "20px",
                verticalAlign: "middle",
                marginRight: 6,
                background: value,
              }}
            />
          )}

          {isRadius && (
            <span
              style={{
                display: "inline-block",
                width: "20px",
                height: "20px",
                verticalAlign: "middle",
                marginRight: 6,
                border: "1px solid black",
                borderRadius: `${value}px`,
              }}
            />
          )}

          <span onClick={() => copyToClipboard(copyStr)}>{name}</span>
        </>
      );
    },
  },
  {
    title: "value",
    dataIndex: "value",
  },
];

const style = {
  height: "100%",
};

const ThemeVars: FC = () => {
  const themeMode = useAppSelector((state) => state.view.themeMode);

  const currentTheme = useTheme(themeMode);
  const modifyVars = { ...mapToken, ...currentTheme?.token };

  const dataSource = Object.entries(modifyVars).map(([name, value], i) => ({
    name,
    value,
    key: `${name}-${i}`,
  })) as TableRecord[];

  return (
    <>
      <Typography.Text>
        单击/双击表格行复制css变量name/变量value
      </Typography.Text>
      <Table<TableRecord>
        dataSource={dataSource}
        columns={columns}
        scroll={
          window.visualViewport?.height
            ? { y: window.visualViewport.height - 120 }
            : undefined
        }
        onRow={(record) => {
          const { name, value } = record;
          return {
            onClick: () => {
              const copyStr = `var(--ant-${getKebabCase(name)})`;
              copyToClipboard(copyStr);
            },
            onDoubleClick: () => {
              copyToClipboard(value);
            },
          };
        }}
        style={style}
        bordered
        pagination={false}
        size="small"
      />
    </>
  );
};

async function copyToClipboard(s: string): Promise<string> {
  try {
    await navigator.clipboard.writeText(s);
    message.success({
      key: "dtk",
      content: `${s}`,
    });
    return s;
  } catch (error) {
    console.error(error);
    return "error";
  }
}

// 驼峰转短横线
function getKebabCase(str: string): string {
  return str.replace(/[A-Z]/g, function (s) {
    return "-" + s.toLowerCase();
  });
}

export default ThemeVars;

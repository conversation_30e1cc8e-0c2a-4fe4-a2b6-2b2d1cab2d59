import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CollapseProps,
  Input,
  Modal,
  Tooltip,
  Tree,
  TreeProps,
  Typography,
} from "antd"
import { FC, Key, useCallback, useMemo, useState } from "react"

import styles from "./index.module.less"

import {
  useAppDispatch,
  useAppSelector,
  useT,
} from "@/hooks"
import { CatalogDO, CatalogVO } from "@/dts"
import SvgIcon from "@/components/SvgIcon"
import {
  addDirectory,
  openAddRenameModal,
  updateAddCatalogParams,
  updateAddModalOpen,
} from "@/store/features/catalogSlice"

import TreeTitleNode from "./TreeTitleNode"
import classNames from 'clsx'
import { ChevronDown, ChevronRight, Plus } from 'lucide-react'

const fieldNames = {
  title: "name",
  key: "id",
  children: "children",
}

const panelKey = "catalog-tree-panel"

interface Props
  extends Pick<TreeProps, "expandedKeys" | "selectedKeys" | "onExpand"> {
  className?: string
  treeList: CatalogVO[]
  onSelect: (d: CatalogDO) => void
  mode: "select" | "action"
}

const CatalogTree: FC<Props> = (props) => {
  const {
    className,
    treeList,
    selectedKeys,
    onSelect,
    onExpand,
    expandedKeys,
    mode,
  } = props

  const appDispatch = useAppDispatch()

  const t = useT()

  const addCatalogModalOpen = useAppSelector(
    (state) => state.catalog.addCatalogModalOpen,
  )
  const addCatalogParams = useAppSelector(
    (state) => state.catalog.addCatalogParams,
  )

  const [collapsed, setCollapsed] = useState<boolean>(true)

  const isActionMode = useMemo(() => "action" === mode, [mode])

  const onChange = useCallback(() => {
    setCollapsed((c) => !c)
  }, [])

  const genExpandIcon = useCallback(
    () => (
      <>
        <SvgIcon icon="catalog" />
        <Typography.Text style={{ marginLeft: 24 }}>
          {t("folder")}
        </Typography.Text>
        ({treeList.length})
        {collapsed ? (
          <ChevronDown className={styles["expand-icon"]} />
        ) : (
          <ChevronRight className={styles["expand-icon"]} />
        )}
      </>
    ),
    [collapsed, treeList, t],
  )

  const genExtra = useCallback(
    () => (
      <Tooltip title={t("models.new-folder")}>
        <Button
          type="text"
          size="small"
          icon={<Plus />}
          onClick={() => {
            appDispatch(
              openAddRenameModal({
                name: "",
                isRename: false,
              }),
            )
          }}
        />
      </Tooltip>
    ),
    [appDispatch, t],
  )

  const titleRender = useCallback(
    (nodeData: CatalogVO) => <TreeTitleNode data={nodeData} mode={mode} />,
    [mode],
  )

  const onSelectHandle = useCallback(
    async (_: Key[], info: { node: CatalogVO }) => {
      onSelect(info.node)
    },
    [onSelect],
  )

  const genTreeList = useCallback(
    () => (
      <Tree
        rootClassName={styles["tree"]}
        blockNode
        showLine
        autoExpandParent
        draggable={false}
        showIcon={false}
        fieldNames={fieldNames}
        titleRender={titleRender}
        treeData={treeList}
        selectedKeys={selectedKeys}
        onSelect={onSelectHandle}
        expandedKeys={expandedKeys}
        onExpand={onExpand}
      />
    ),
    [
      titleRender,
      treeList,
      selectedKeys,
      onSelectHandle,
      expandedKeys,
      onExpand,
    ],
  )

  const genItems = useCallback(() => {
    return [
      {
        key: panelKey,
        children: genTreeList(),
        extra: isActionMode ? genExtra() : undefined,
      },
    ] as CollapseProps["items"]
  }, [genTreeList, genExtra, isActionMode])

  const addCatalog = useCallback(async () => {
    await appDispatch(
      addDirectory({
        parentDirectoryId: addCatalogParams.dirId,
        name: addCatalogParams.name,
      }),
    )
  }, [appDispatch, addCatalogParams])

  return (
    <>
      <Collapse
        className={classNames(styles["catalog-tree-collapse"], className)}
        expandIcon={genExpandIcon}
        items={genItems()}
        activeKey={collapsed ? [panelKey] : []}
        onChange={onChange}
        bordered={false}
        ghost
        collapsible="icon"
        size="small"
      />

      <Modal
        title={t("models.new-folder")}
        open={addCatalogModalOpen}
        onOk={addCatalog}
        cancelButtonProps={{ hidden: true }}
        okButtonProps={{ disabled: !addCatalogParams?.name }}
        onCancel={() => appDispatch(updateAddModalOpen(false))}
        width={360}
      >
        <Input
          value={addCatalogParams?.name}
          onChange={(e) =>
            appDispatch(updateAddCatalogParams({ name: e.target.value }))
          }
          placeholder={t("models.form-input-placeholder")}
        />
      </Modal>
    </>
  )
}

export default CatalogTree

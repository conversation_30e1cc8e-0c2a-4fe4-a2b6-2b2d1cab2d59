import type { CatalogDO } from '@/dts'
import type { TreeSelectProps } from 'antd'
import type { FC } from 'react'
import { god } from '@/god'

import { useAppDispatch, useT } from '@/hooks'
import { addDirectory } from '@/store/features/catalogSlice'
import { Button, Input, Space, TreeSelect } from 'antd'
import { Plus } from 'lucide-react'
import { useCallback, useState } from 'react'

interface Props {
  treeList: CatalogDO[]
  value?: CatalogDO['id']
  onChange?: (v: CatalogDO['id']) => void
}

const fieldNames = {
  label: 'name',
  value: 'id',
  children: 'children',
}

const CatalogSelect: FC<Props> = ({
  value,
  treeList,
  onChange: formOnChange,
}) => {
  const t = useT()

  const [inputValue, setInputValue] = useState('')

  const appDispatch = useAppDispatch()

  const onChange = useCallback(
    (value: CatalogDO['id']) => {
      formOnChange?.(value)
    },
    [formOnChange],
  )

  const addItem = useCallback(async () => {
    if (!inputValue) {
      return
    }
    await appDispatch(addDirectory({ name: inputValue }))
    god.messageSuccess(t('success'))
  }, [appDispatch, inputValue, t])

  const dropdownRender: TreeSelectProps['dropdownRender']
    = treeList.length > 0
      ? undefined
      : () => (
          <>
            <Space>
              <Input
                placeholder={ t('models.folder-name') }
                value={ inputValue }
                onChange={ (e) => {
                  setInputValue(e.target.value)
                } }
              />
              <Button
                type="primary"
                icon={ <Plus /> }
                disabled={ !inputValue }
                onClick={ addItem }
              >
                {t('models.new-folder')}
              </Button>
            </Space>
          </>
        )

  return (
    <TreeSelect
      showSearch
      style={ { width: '100%' } }
      treeData={ treeList }
      fieldNames={ fieldNames }
      value={ value }
      placeholder={ t('models.form-select-placeholder') }
      allowClear
      treeDefaultExpandAll
      onChange={ onChange }
      dropdownStyle={ { maxHeight: 400, overflow: 'auto' } }
      dropdownRender={ dropdownRender }
    />
  )
}

export default CatalogSelect

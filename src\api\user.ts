import type { UserDO, UserInfoUpdateReq } from '@/dts'
import { LOGIN_ENCRYPT_PUBLIC_KEY } from '@/config'
import { request } from '@/utils'
import JSEncrypt from 'jsencrypt'

const encrypt = new JSEncrypt()
encrypt.setPublicKey(LOGIN_ENCRYPT_PUBLIC_KEY)

export class API_USER {
  /** 获取用户信息 */
  static async getUserInfo(): Promise<UserDO> {
    return request.get(`/auth/user/info`)
  }

  /** 帐号登录 */
  static async accountLogin(accountLoginReq: ApiAccountLoginParams): Promise<{
    token: string
  }> {
    const url = `/auth/account`
    return request.post(url, accountLoginReq)
  }

  /** 退出登录 */
  static async logout(): Promise<unknown> {
    const url = `/auth/logout`
    return request.post(url)
  }

  /** 邮箱登录 */
  static async emailLogin(params: {
    email: string
    password: string
  }): Promise<{ token: string }> {
    const encPassword = encrypt.encrypt(params.password) as string
    return request.post(`/auth/email`, {
      ...params,
      password: encPassword,
    })
  }

  /** 邮箱注册 */
  static async emailRegister(params: {
    email: string
    password: string
    captcha: string
  }): Promise<boolean> {
    const encPassword = encrypt.encrypt(params.password) as string
    return request.post(`/register/email`, {
      ...params,
      password: encPassword,
    })
  }

  /** 忘记密码 */
  static async forgotPwd(params: {
    email: string
    password: string
    captcha: string
  }): Promise<boolean> {
    const encPassword = encrypt.encrypt(params.password) as string
    return request.post('/auth/forgot-password', {
      ...params,
      password: encPassword,
    })
  }

  /** 第三方登录 */
  static async thirdLogin(params: ThirdLoginParams): Promise<LoginResp> {
    const encPassword = encrypt.encrypt(params.uuid) as string
    return request.post('/oauth/social-login', {
      ...params,
      uuid: encPassword,
    })
  }

  /**
   * 手机号登录/注册
   */
  static async phoneLoginOrRegister(
    data: PhoneLoginReq,
  ): Promise<LoginResp> {
    return request.post('/auth/mobile', data)
  }

  /**
   * 获取所有行业
   */
  static async listAllIndustries(): Promise<IndustryDO[]> {
    return request.get('/system/industries')
  }

  /**
   * 修改用户基础信息
   */
  static async updateBasicUserInfo(data: UserInfoUpdateReq): Promise<void> {
    return request.patch('/system/user/update/info', data)
  }
}

export type IndustryDO = {
  id: number
  createUser: number
  createTime: string
  updateUser: number
  updateTime: string
  name: string
  code: string
  sort: number
  description: string
  status: boolean
}

export type PhoneLoginReq = {
  /** 手机号 */
  phone: string
  /** 验证码 */
  captcha: string
}

export type LoginResp = {
  /** 令牌 */
  token: string
}

interface ApiAccountLoginParams {
  username: string
  password: string
  captcha: string
  uuid: string
}

export type ThirdLoginParams = {
  uuid: string
  email: string
  source: 'google'
  username?: string
  nickname?: string
  avatar?: string
  blog?: string
  company?: string
  location?: string
  remark?: string
}

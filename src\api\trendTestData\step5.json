{"data": {"输出": {"content": "```html\n<div style=\"max-width:750px; margin:0 auto; padding:0 30px; font-family:'PingFang SC','Microsoft YaHei',sans-serif; color:#333; background:#fff;\">\n  <!-- 标题区 -->\n  <div style=\"text-align:center; margin-bottom:40px;\">\n    <h1 style=\"font-size:32px; font-weight:600; margin-bottom:20px; color:#222;\">卡姿兰无痕锁妆粉底液小红书营销策略</h1>\n    <div style=\"height:4px; width:120px; background:linear-gradient(90deg, #FF9A9E 0%, #FAD0C4 100%); margin:0 auto;\"></div>\n  </div>\n\n  <!-- 市场洞察 -->\n  <div style=\"margin-bottom:40px; background:#F8F9FA; border-radius:12px; padding:25px;\">\n    <h2 style=\"font-size:26px; font-weight:600; margin-bottom:20px; color:#222;\">🔍 市场机会洞察</h2>\n    \n    <div style=\"display:flex; flex-wrap:wrap; gap:20px; margin-bottom:25px;\">\n      <div style=\"flex:1; min-width:200px; background:#fff; border-radius:8px; padding:15px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n        <div style=\"display:flex; align-items:center; margin-bottom:10px;\">\n          <div style=\"width:36px; height:36px; background:#FFD166; border-radius:50%; display:flex; align-items:center; justify-content:center; margin-right:10px;\">📊</div>\n          <h3 style=\"font-size:18px; font-weight:600;\">彩妆赛道数据</h3>\n        </div>\n        <p style=\"font-size:15px; line-height:1.6; color:#555;\">月均阅读1.5亿，互动940.8万，商业笔记占比44.15%</p>\n      </div>\n      \n      <div style=\"flex:1; min-width:200px; background:#fff; border-radius:8px; padding:15px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n        <div style=\"display:flex; align-items:center; margin-bottom:10px;\">\n          <div style=\"width:36px; height:36px; background:#06D6A0; border-radius:50%; display:flex; align-items:center; justify-content:center; margin-right:10px;\">💰</div>\n          <h3 style=\"font-size:18px; font-weight:600;\">投放效率</h3>\n        </div>\n        <p style=\"font-size:15px; line-height:1.6; color:#555;\">品牌CPM 166.3元，CPE 2.29元，爆文率21.1%</p>\n      </div>\n    </div>\n    \n    <div style=\"background:#fff; border-radius:8px; padding:20px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n      <h3 style=\"font-size:20px; font-weight:600; margin-bottom:15px; color:#222;\">核心机会点</h3>\n      <ul style=\"padding-left:20px; margin-bottom:0;\">\n        <li style=\"margin-bottom:8px; font-size:15px; line-height:1.6;\">填补\"高性价比强锁妆&无痕柔焦\"市场空白</li>\n        <li style=\"margin-bottom:8px; font-size:15px; line-height:1.6;\">利用品牌现有21.1%爆文率优势快速扩散</li>\n        <li style=\"margin-bottom:8px; font-size:15px; line-height:1.6;\">精准投放\"封控油\"\"抗闷汗\"等细分场景</li>\n      </ul>\n    </div>\n  </div>\n\n  <!-- 人群洞察 -->\n  <div style=\"margin-bottom:40px;\">\n    <h2 style=\"font-size:26px; font-weight:600; margin-bottom:20px; color:#222;\">👥 核心人群洞察</h2>\n    \n    <div style=\"display:flex; overflow-x:auto; gap:15px; padding-bottom:10px; margin-bottom:25px;\">\n      <div style=\"min-width:220px; background:#fff; border-radius:8px; padding:20px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n        <div style=\"width:50px; height:50px; background:#FF9A9E; border-radius:50%; display:flex; align-items:center; justify-content:center; margin:0 auto 15px; font-size:24px;\">👩‍💼</div>\n        <h3 style=\"font-size:18px; font-weight:600; text-align:center; margin-bottom:10px;\">轻熟白领</h3>\n        <p style=\"font-size:14px; text-align:center; color:#666;\">25-34岁，油皮/混油皮，广州/杭州/深圳</p>\n      </div>\n      \n      <div style=\"min-width:220px; background:#fff; border-radius:8px; padding:20px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n        <div style=\"width:50px; height:50px; background:#A0E7E5; border-radius:50%; display:flex; align-items:center; justify-content:center; margin:0 auto 15px; font-size:24px;\">👩‍🎓</div>\n        <h3 style=\"font-size:18px; font-weight:600; text-align:center; margin-bottom:10px;\">学生党</h3>\n        <p style=\"font-size:14px; text-align:center; color:#666;\">18-24岁，中性皮/痘肌，宿舍上妆场景</p>\n      </div>\n      \n      <div style=\"min-width:220px; background:#fff; border-radius:8px; padding:20px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n        <div style=\"width:50px; height:50px; background:#B5EAD7; border-radius:50%; display:flex; align-items:center; justify-content:center; margin:0 auto 15px; font-size:24px;\">💄</div>\n        <h3 style=\"font-size:18px; font-weight:600; text-align:center; margin-bottom:10px;\">唇妆老粉</h3>\n        <p style=\"font-size:14px; text-align:center; color:#666;\">卡姿兰口红用户，品牌好感度高</p>\n      </div>\n    </div>\n    \n    <div style=\"background:#F8F9FA; border-radius:12px; padding:25px;\">\n      <h3 style=\"font-size:20px; font-weight:600; margin-bottom:15px; color:#222;\">人群痛点地图</h3>\n      <div style=\"display:flex; flex-wrap:wrap; gap:10px; margin-bottom:15px;\">\n        <span style=\"background:#E9F5FF; color:#0066CC; padding:6px 12px; border-radius:16px; font-size:14px;\">持妆</span>\n        <span style=\"background:#FFEEF6; color:#CC0066; padding:6px 12px; border-radius:16px; font-size:14px;\">遮瑕</span>\n        <span style=\"background:#FFF5E6; color:#FF6600; padding:6px 12px; border-radius:16px; font-size:14px;\">控油</span>\n        <span style=\"background:#E6F9EE; color:#009933; padding:6px 12px; border-radius:16px; font-size:14px;\">服帖</span>\n        <span style=\"background:#F0E6FF; color:#6600CC; padding:6px 12px; border-radius:16px; font-size:14px;\">不卡粉</span>\n        <span style=\"background:#E6F5FF; color:#0066CC; padding:6px 12px; border-radius:16px; font-size:14px;\">轻薄</span>\n      </div>\n      <p style=\"font-size:15px; line-height:1.6; color:#555;\">典型用户原话：\"10小时会议连轴转都不脱妆\"、\"素颜肌那种光，像天生气色好\"、\"口罩一整天，不沾\"</p>\n    </div>\n  </div>\n\n  <!-- 投放策略 -->\n  <div style=\"margin-bottom:40px;\">\n    <h2 style=\"font-size:26px; font-weight:600; margin-bottom:20px; color:#222;\">📈 KFS投放策略</h2>\n    \n    <div style=\"background:#F8F9FA; border-radius:12px; padding:25px; margin-bottom:25px;\">\n      <h3 style=\"font-size:20px; font-weight:600; margin-bottom:15px; color:#222;\">达人投放结构</h3>\n      \n      <div style=\"display:flex; align-items:flex-end; height:200px; gap:15px; margin-bottom:20px;\">\n        <div style=\"flex:1; text-align:center;\">\n          <div style=\"height:40px; background:#FF9A9E; border-radius:6px 6px 0 0;\"></div>\n          <p style=\"font-size:12px; margin-top:5px;\">头部达人</p>\n          <p style=\"font-size:12px; color:#666;\">6人</p>\n        </div>\n        <div style=\"flex:2; text-align:center;\">\n          <div style=\"height:120px; background:#A0E7E5; border-radius:6px 6px 0 0;\"></div>\n          <p style=\"font-size:12px; margin-top:5px;\">腰部达人</p>\n          <p style=\"font-size:12px; color:#666;\">250人</p>\n        </div>\n        <div style=\"flex:3; text-align:center;\">\n          <div style=\"height:160px; background:#B5EAD7; border-radius:6px 6px 0 0;\"></div>\n          <p style=\"font-size:12px; margin-top:5px;\">初级达人</p>\n          <p style=\"font-size:12px; color:#666;\">794人</p>\n        </div>\n        <div style=\"flex:4; text-align:center;\">\n          <div style=\"height:80px; background:#FFD166; border-radius:6px 6px 0 0;\"></div>\n          <p style=\"font-size:12px; margin-top:5px;\">素人</p>\n          <p style=\"font-size:12px; color:#666;\">9,200+</p>\n        </div>\n      </div>\n      \n      <div style=\"display:flex; flex-wrap:wrap; gap:15px;\">\n        <div style=\"flex:1; min-width:200px; background:#fff; border-radius:8px; padding:15px;\">\n          <p style=\"font-size:14px; color:#666; margin-bottom:5px;\">头部达人CPM</p>\n          <p style=\"font-size:18px; font-weight:600;\">554.01元</p>\n        </div>\n        <div style=\"flex:1; min-width:200px; background:#fff; border-radius:8px; padding:15px;\">\n          <p style=\"font-size:14px; color:#666; margin-bottom:5px;\">素人CPM</p>\n          <p style=\"font-size:18px; font-weight:600;\">9.49元</p>\n        </div>\n      </div>\n    </div>\n    \n    <div style=\"background:#F8F9FA; border-radius:12px; padding:25px;\">\n      <h3 style=\"font-size:20px; font-weight:600; margin-bottom:15px; color:#222;\">关键词矩阵策略</h3>\n      \n      <div style=\"display:flex; flex-wrap:wrap; gap:15px; margin-bottom:20px;\">\n        <div style=\"flex:1; min-width:200px; background:#fff; border-radius:8px; padding:15px;\">\n          <h4 style=\"font-size:16px; font-weight:600; margin-bottom:10px; color:#222;\">防守25%</h4>\n          <ul style=\"padding-left:20px; margin:0; font-size:14px; color:#555;\">\n            <li style=\"margin-bottom:5px;\">卡姿兰粉底液</li>\n            <li style=\"margin-bottom:5px;\">卡姿兰无痕锁妆</li>\n          </ul>\n        </div>\n        \n        <div style=\"flex:1; min-width:200px; background:#fff; border-radius:8px; padding:15px;\">\n          <h4 style=\"font-size:16px; font-weight:600; margin-bottom:10px; color:#222;\">进攻55%</h4>\n          <ul style=\"padding-left:20px; margin:0; font-size:14px; color:#555;\">\n            <li style=\"margin-bottom:5px;\">无痕粉底</li>\n            <li style=\"margin-bottom:5px;\">锁妆粉底</li>\n            <li style=\"margin-bottom:5px;\">脱妆拯救</li>\n          </ul>\n        </div>\n        \n        <div style=\"flex:1; min-width:200px; background:#fff; border-radius:8px; padding:15px;\">\n          <h4 style=\"font-size:16px; font-weight:600; margin-bottom:10px; color:#222;\">长尾20%</h4>\n          <ul style=\"padding-left:20px; margin:0; font-size:14px; color:#555;\">\n            <li style=\"margin-bottom:5px;\">38℃户外妆</li>\n            <li style=\"margin-bottom:5px;\">素颜高光肌</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 执行计划 -->\n  <div style=\"margin-bottom:40px;\">\n    <h2 style=\"font-size:26px; font-weight:600; margin-bottom:20px; color:#222;\">📅 90天执行计划</h2>\n    \n    <div style=\"background:#F8F9FA; border-radius:12px; padding:25px;\">\n      <div style=\"display:flex; margin-bottom:25px;\">\n        <div style=\"flex:1; min-width:0; padding-right:15px;\">\n          <div style=\"width:100%; height:8px; background:#E0E0E0; border-radius:4px; margin-bottom:10px;\">\n            <div style=\"width:33%; height:100%; background:#FF9A9E; border-radius:4px;\"></div>\n          </div>\n          <h3 style=\"font-size:18px; font-weight:600; margin-bottom:5px;\">Month 1</h3>\n          <p style=\"font-size:14px; color:#666;\">冷启动 & 认知破圈</p>\n        </div>\n        \n        <div style=\"flex:1; min-width:0; padding-right:15px;\">\n          <div style=\"width:100%; height:8px; background:#E0E0E0; border-radius:4px; margin-bottom:10px;\">\n            <div style=\"width:66%; height:100%; background:#A0E7E5; border-radius:4px;\"></div>\n          </div>\n          <h3 style=\"font-size:18px; font-weight:600; margin-bottom:5px;\">Month 2</h3>\n          <p style=\"font-size:14px; color:#666;\">放量 & 进攻痛点</p>\n        </div>\n        \n        <div style=\"flex:1; min-width:0;\">\n          <div style=\"width:100%; height:8px; background:#E0E0E0; border-radius:4px; margin-bottom:10px;\">\n            <div style=\"width:100%; height:100%; background:#B5EAD7; border-radius:4px;\"></div>\n          </div>\n          <h3 style=\"font-size:18px; font-weight:600; margin-bottom:5px;\">Month 3</h3>\n          <p style=\"font-size:14px; color:#666;\">转化 & 长尾固化</p>\n        </div>\n      </div>\n      \n      <div style=\"background:#fff; border-radius:8px; padding:20px; margin-bottom:15px;\">\n        <h3 style=\"font-size:20px; font-weight:600; margin-bottom:15px; color:#222;\">核心指标</h3>\n        <div style=\"display:flex; flex-wrap:wrap; gap:15px;\">\n          <div style=\"flex:1; min-width:200px;\">\n            <p style=\"font-size:14px; color:#666; margin-bottom:5px;\">累计阅读量</p>\n            <p style=\"font-size:18px; font-weight:600;\">6,500万</p>\n          </div>\n          <div style=\"flex:1; min-width:200px;\">\n            <p style=\"font-size:14px; color:#666; margin-bottom:5px;\">爆文数量</p>\n            <p style=\"font-size:18px; font-weight:600;\">350篇</p>\n          </div>\n          <div style=\"flex:1; min-width:200px;\">\n            <p style=\"font-size:14px; color:#666; margin-bottom:5px;\">CPE目标</p>\n            <p style=\"font-size:18px; font-weight:600;\">≤1.6元</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 总结 -->\n  <div style=\"background:linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%); border-radius:12px; padding:30px; text-align:center;\">\n    <h2 style=\"font-size:26px; font-weight:600; margin-bottom:15px; color:#222;\">✨ 预期成效</h2>\n    <p style=\"font-size:16px; line-height:1.6; color:#555; margin-bottom:25px;\">90天实现电商转化率提升32%，线下专柜客流同比+19%</p>\n    <div style=\"display:inline-block; background:#222; color:#fff; padding:12px 24px; border-radius:30px; font-weight:600; font-size:16px;\">无痕锁妆 · 持久美丽</div>\n  </div>\n</div>\n```"}}, "allJson": [{"executionId": "1942422206231482369", "processInstanceId": "1942185758874013696", "__internal__event": "meta"}, {"content": "```", "node_title": "输出", "node_seq_id": "0", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "html\n<div", "node_title": "输出", "node_seq_id": "1", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"max-width:750", "node_title": "输出", "node_seq_id": "2", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; margin:0 auto; padding:0", "node_title": "输出", "node_seq_id": "3", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 30px; font-family", "node_title": "输出", "node_seq_id": "4", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":'<PERSON>", "node_title": "输出", "node_seq_id": "5", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "Fang SC','Microsoft YaHei", "node_title": "输出", "node_seq_id": "6", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "',sans-serif;", "node_title": "输出", "node_seq_id": "7", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#333; background:#fff;\">\n", "node_title": "输出", "node_seq_id": "8", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "  <!-- 标题区 -->\n  <div", "node_title": "输出", "node_seq_id": "9", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"text-align:center", "node_title": "输出", "node_seq_id": "10", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:40px;\">\n   ", "node_title": "输出", "node_seq_id": "11", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <h1 style=\"font-size:32", "node_title": "输出", "node_seq_id": "12", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; font-weight:600;", "node_title": "输出", "node_seq_id": "13", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " margin-bottom:20px", "node_title": "输出", "node_seq_id": "14", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#222;\">卡姿兰无", "node_title": "输出", "node_seq_id": "15", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "痕锁妆粉底液小红书营销策略", "node_title": "输出", "node_seq_id": "16", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</h1>\n   ", "node_title": "输出", "node_seq_id": "17", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"height:4px; width", "node_title": "输出", "node_seq_id": "18", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":120px; background:linear-gradient", "node_title": "输出", "node_seq_id": "19", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "(90deg, #FF", "node_title": "输出", "node_seq_id": "20", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "9A9E 0%, #FAD", "node_title": "输出", "node_seq_id": "21", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0C4", "node_title": "输出", "node_seq_id": "22", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 100%); margin:0 auto;\"></div>\n", "node_title": "输出", "node_seq_id": "23", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "  </div>\n\n  <!-- ", "node_title": "输出", "node_seq_id": "24", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "市场洞察 -->\n  <div style", "node_title": "输出", "node_seq_id": "25", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"margin-bottom:40px; background:#", "node_title": "输出", "node_seq_id": "26", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "F8F9FA; border-radius:", "node_title": "输出", "node_seq_id": "27", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; padding:25px;\">\n", "node_title": "输出", "node_seq_id": "28", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "    <h2", "node_title": "输出", "node_seq_id": "29", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:", "node_title": "输出", "node_seq_id": "30", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "26px; font-weight:600", "node_title": "输出", "node_seq_id": "31", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:20px;", "node_title": "输出", "node_seq_id": "32", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#222", "node_title": "输出", "node_seq_id": "33", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">🔍 市场机会洞察</h", "node_title": "输出", "node_seq_id": "34", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2>\n    \n    <div style=\"", "node_title": "输出", "node_seq_id": "35", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "display:flex; flex-wrap", "node_title": "输出", "node_seq_id": "36", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":wrap; gap:20px; margin", "node_title": "输出", "node_seq_id": "37", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:25px;\">\n      <", "node_title": "输出", "node_seq_id": "38", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"flex:1; min", "node_title": "输出", "node_seq_id": "39", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-width:200px; background:#fff; border", "node_title": "输出", "node_seq_id": "40", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:8px; padding:15px", "node_title": "输出", "node_seq_id": "41", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; box-shadow:0 2", "node_title": "输出", "node_seq_id": "42", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px 8px rgba(", "node_title": "输出", "node_seq_id": "43", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0,0,0,0.", "node_title": "输出", "node_seq_id": "44", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "05);\">\n        <div style=\"display", "node_title": "输出", "node_seq_id": "45", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex; align-items:center; margin-bottom", "node_title": "输出", "node_seq_id": "46", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":10px;\">\n          <div style", "node_title": "输出", "node_seq_id": "47", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"width:36px;", "node_title": "输出", "node_seq_id": "48", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " height:36px; background:#FFD166", "node_title": "输出", "node_seq_id": "49", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-radius:50%; display", "node_title": "输出", "node_seq_id": "50", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex; align-items:center; justify", "node_title": "输出", "node_seq_id": "51", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-content:center; margin-right:10", "node_title": "输出", "node_seq_id": "52", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">📊</div>\n          <h3 style=\"font-size:", "node_title": "输出", "node_seq_id": "53", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "18px; font-weight", "node_title": "输出", "node_seq_id": "54", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":600;\">彩妆赛道数据</h", "node_title": "输出", "node_seq_id": "55", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "3>\n        </div>\n        <", "node_title": "输出", "node_seq_id": "56", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p style=\"font-size:15px; line", "node_title": "输出", "node_seq_id": "57", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-height:1.6; color", "node_title": "输出", "node_seq_id": "58", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#555;\">", "node_title": "输出", "node_seq_id": "59", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "月均阅读1.5亿，互动940", "node_title": "输出", "node_seq_id": "60", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ".8万，", "node_title": "输出", "node_seq_id": "61", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "商业笔记", "node_title": "输出", "node_seq_id": "62", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "占比44.15%</p>\n      </", "node_title": "输出", "node_seq_id": "63", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n      \n      <", "node_title": "输出", "node_seq_id": "64", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"flex:1; min", "node_title": "输出", "node_seq_id": "65", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-width:200px; background:#fff;", "node_title": "输出", "node_seq_id": "66", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:8px;", "node_title": "输出", "node_seq_id": "67", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " padding:15px; box", "node_title": "输出", "node_seq_id": "68", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-shadow:0 2px 8px rgba", "node_title": "输出", "node_seq_id": "69", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "(0,0,0,0", "node_title": "输出", "node_seq_id": "70", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ".05);\">\n        <div", "node_title": "输出", "node_seq_id": "71", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"display:flex;", "node_title": "输出", "node_seq_id": "72", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " align-items:center; margin-bottom:10", "node_title": "输出", "node_seq_id": "73", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">\n          <div style=\"", "node_title": "输出", "node_seq_id": "74", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "width:36px; height:", "node_title": "输出", "node_seq_id": "75", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "36px; background:#", "node_title": "输出", "node_seq_id": "76", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "06D6A0; border-radius:", "node_title": "输出", "node_seq_id": "77", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "50%; display:flex; align-items", "node_title": "输出", "node_seq_id": "78", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":center; justify-content:center; margin-right", "node_title": "输出", "node_seq_id": "79", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":10px;\">💰</div", "node_title": "输出", "node_seq_id": "80", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n          <h", "node_title": "输出", "node_seq_id": "81", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "3 style=\"font-size:18px;", "node_title": "输出", "node_seq_id": "82", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-weight:600;\">投放", "node_title": "输出", "node_seq_id": "83", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "效率</h3>\n        </", "node_title": "输出", "node_seq_id": "84", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n        <p style=\"font-size", "node_title": "输出", "node_seq_id": "85", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":15px; line-height:", "node_title": "输出", "node_seq_id": "86", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1.6; color:#555;\">", "node_title": "输出", "node_seq_id": "87", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "品牌CPM 166.3元", "node_title": "输出", "node_seq_id": "88", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "，CPE 2.29元", "node_title": "输出", "node_seq_id": "89", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "，爆文率21.1%</", "node_title": "输出", "node_seq_id": "90", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p>\n      </div>\n    </div>\n", "node_title": "输出", "node_seq_id": "91", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "    \n    <div style=\"", "node_title": "输出", "node_seq_id": "92", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "background:#fff; border-radius:8", "node_title": "输出", "node_seq_id": "93", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; padding:20px; box", "node_title": "输出", "node_seq_id": "94", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-shadow:0 2px ", "node_title": "输出", "node_seq_id": "95", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "8px rgba(0,0,", "node_title": "输出", "node_seq_id": "96", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0,0.05);\">\n      <", "node_title": "输出", "node_seq_id": "97", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "h3 style=\"font-size:20px", "node_title": "输出", "node_seq_id": "98", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-weight:600; margin-bottom", "node_title": "输出", "node_seq_id": "99", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":15px; color:#222", "node_title": "输出", "node_seq_id": "100", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">核心机会点</h3>\n", "node_title": "输出", "node_seq_id": "101", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "      <ul style=\"padding-left:20px", "node_title": "输出", "node_seq_id": "102", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin", "node_title": "输出", "node_seq_id": "103", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:0;\">\n        <li style", "node_title": "输出", "node_seq_id": "104", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"margin-bottom:8px; font", "node_title": "输出", "node_seq_id": "105", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:15px; line-height:", "node_title": "输出", "node_seq_id": "106", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1.6;\">填补\"高性价比强", "node_title": "输出", "node_seq_id": "107", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "锁妆&无痕柔焦", "node_title": "输出", "node_seq_id": "108", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"市场空白</li>\n       ", "node_title": "输出", "node_seq_id": "109", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <li style=\"margin-bottom:8px;", "node_title": "输出", "node_seq_id": "110", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-size:15px;", "node_title": "输出", "node_seq_id": "111", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " line-height:1.6;\">利用品牌", "node_title": "输出", "node_seq_id": "112", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "现有21.1%爆文", "node_title": "输出", "node_seq_id": "113", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "率优势快速扩散</li>\n        <li", "node_title": "输出", "node_seq_id": "114", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"margin-bottom:8px", "node_title": "输出", "node_seq_id": "115", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-size:15px;", "node_title": "输出", "node_seq_id": "116", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " line-height:1.6;\">精准投放\"封", "node_title": "输出", "node_seq_id": "117", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "控油\"\"抗闷汗\"等", "node_title": "输出", "node_seq_id": "118", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "细分场景", "node_title": "输出", "node_seq_id": "119", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</li>\n      </ul>\n    </div", "node_title": "输出", "node_seq_id": "120", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n  </div>\n\n  <!-- ", "node_title": "输出", "node_seq_id": "121", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "人群洞察 -->\n  <", "node_title": "输出", "node_seq_id": "122", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"margin-bottom:40px", "node_title": "输出", "node_seq_id": "123", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n    <h2 style=\"font-size:26px; font-weight:600", "node_title": "输出", "node_seq_id": "124", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:20px; color:#", "node_title": "输出", "node_seq_id": "125", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "222;\">👥 核心人群", "node_title": "输出", "node_seq_id": "126", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "洞察</h2>\n    \n    <div style", "node_title": "输出", "node_seq_id": "127", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"display:flex; overflow", "node_title": "输出", "node_seq_id": "128", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-x:auto; gap:15px; padding", "node_title": "输出", "node_seq_id": "129", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:10px; margin", "node_title": "输出", "node_seq_id": "130", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:25px;\">\n      <div", "node_title": "输出", "node_seq_id": "131", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"min-width:220px; background:#", "node_title": "输出", "node_seq_id": "132", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "fff; border-radius:8", "node_title": "输出", "node_seq_id": "133", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; padding:20px; box", "node_title": "输出", "node_seq_id": "134", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-shadow:0 2px 8px", "node_title": "输出", "node_seq_id": "135", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " rgba(0,0,", "node_title": "输出", "node_seq_id": "136", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0,0.05);\">\n        <", "node_title": "输出", "node_seq_id": "137", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"width", "node_title": "输出", "node_seq_id": "138", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":50px; height:50", "node_title": "输出", "node_seq_id": "139", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; background:#FF9A9E", "node_title": "输出", "node_seq_id": "140", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-radius:50%; display:", "node_title": "输出", "node_seq_id": "141", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "flex; align-items:center; justify-content", "node_title": "输出", "node_seq_id": "142", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":center; margin:0 auto", "node_title": "输出", "node_seq_id": "143", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 15px", "node_title": "输出", "node_seq_id": "144", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-size:24px;\">👩", "node_title": "输出", "node_seq_id": "145", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "‍💼</div>\n       ", "node_title": "输出", "node_seq_id": "146", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <h3 style=\"font-size:", "node_title": "输出", "node_seq_id": "147", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "18px; font-weight", "node_title": "输出", "node_seq_id": "148", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":600; text-align:center;", "node_title": "输出", "node_seq_id": "149", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " margin-bottom:10px;\">轻", "node_title": "输出", "node_seq_id": "150", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "熟白领</h3>\n        <", "node_title": "输出", "node_seq_id": "151", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p style=\"font-size:14", "node_title": "输出", "node_seq_id": "152", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; text-align:center; color:#666", "node_title": "输出", "node_seq_id": "153", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">25-34岁，油皮", "node_title": "输出", "node_seq_id": "154", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "/混油皮，广州/", "node_title": "输出", "node_seq_id": "155", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "杭州/深圳</p>\n      </div>\n", "node_title": "输出", "node_seq_id": "156", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "      \n      <div style=\"", "node_title": "输出", "node_seq_id": "157", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "min-width:220px; background", "node_title": "输出", "node_seq_id": "158", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#fff; border-radius:8px", "node_title": "输出", "node_seq_id": "159", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding:20px; box-shadow", "node_title": "输出", "node_seq_id": "160", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":0 2px 8px rgba", "node_title": "输出", "node_seq_id": "161", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "(0,0,0,", "node_title": "输出", "node_seq_id": "162", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0.05);\">\n        <div style", "node_title": "输出", "node_seq_id": "163", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"width:50px; height:", "node_title": "输出", "node_seq_id": "164", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "50px; background:#A0", "node_title": "输出", "node_seq_id": "165", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "E7E5", "node_title": "输出", "node_seq_id": "166", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-radius:50%; display:flex; align", "node_title": "输出", "node_seq_id": "167", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-items:center; justify-content", "node_title": "输出", "node_seq_id": "168", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":center; margin:0 auto ", "node_title": "输出", "node_seq_id": "169", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px; font-size:24px", "node_title": "输出", "node_seq_id": "170", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">👩‍🎓</div>\n", "node_title": "输出", "node_seq_id": "171", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        <h3 style=\"", "node_title": "输出", "node_seq_id": "172", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:18px; font-weight", "node_title": "输出", "node_seq_id": "173", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":600; text-align:center; margin-bottom:", "node_title": "输出", "node_seq_id": "174", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "10px;\">学生党</h", "node_title": "输出", "node_seq_id": "175", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "3>\n        <p style=\"font-size", "node_title": "输出", "node_seq_id": "176", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":14px; text-align:center", "node_title": "输出", "node_seq_id": "177", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#666;\">18-", "node_title": "输出", "node_seq_id": "178", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "24岁，中性皮/痘", "node_title": "输出", "node_seq_id": "179", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "肌，宿舍上妆场景</", "node_title": "输出", "node_seq_id": "180", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p>\n      </div>\n      \n      <", "node_title": "输出", "node_seq_id": "181", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"min-width:220px", "node_title": "输出", "node_seq_id": "182", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; background:#fff; border-radius:8px", "node_title": "输出", "node_seq_id": "183", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding:20px; box-shadow", "node_title": "输出", "node_seq_id": "184", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":0 2px 8px", "node_title": "输出", "node_seq_id": "185", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " rgba(0,0,0,", "node_title": "输出", "node_seq_id": "186", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0.05);\">\n       ", "node_title": "输出", "node_seq_id": "187", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"width:50", "node_title": "输出", "node_seq_id": "188", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; height:50px; background:#", "node_title": "输出", "node_seq_id": "189", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "B5EAD7; border-radius", "node_title": "输出", "node_seq_id": "190", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":50%; display:flex; align-items", "node_title": "输出", "node_seq_id": "191", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":center; justify-content:center", "node_title": "输出", "node_seq_id": "192", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin:0 auto 15px;", "node_title": "输出", "node_seq_id": "193", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-size:24px;\">", "node_title": "输出", "node_seq_id": "194", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "💄</div>\n        <h3 style=\"font-size", "node_title": "输出", "node_seq_id": "195", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":18px; font-weight:", "node_title": "输出", "node_seq_id": "196", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "600; text-align:center; margin-bottom", "node_title": "输出", "node_seq_id": "197", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":10px;\">唇妆老", "node_title": "输出", "node_seq_id": "198", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "粉</h3>\n        <p", "node_title": "输出", "node_seq_id": "199", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:14px;", "node_title": "输出", "node_seq_id": "200", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " text-align:center; color:#666;\">", "node_title": "输出", "node_seq_id": "201", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "卡姿兰口红用户，品牌好感", "node_title": "输出", "node_seq_id": "202", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "度高</p>\n      </div>\n", "node_title": "输出", "node_seq_id": "203", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "    </div>\n    \n    <", "node_title": "输出", "node_seq_id": "204", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"background:#F8F9", "node_title": "输出", "node_seq_id": "205", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "FA; border-radius:12px; padding:", "node_title": "输出", "node_seq_id": "206", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "25px;\">\n      <h", "node_title": "输出", "node_seq_id": "207", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "3 style=\"font-size:20px;", "node_title": "输出", "node_seq_id": "208", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-weight:600; margin-bottom:", "node_title": "输出", "node_seq_id": "209", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px; color:#222;\">人群痛点", "node_title": "输出", "node_seq_id": "210", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "地图</h3", "node_title": "输出", "node_seq_id": "211", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n      <div", "node_title": "输出", "node_seq_id": "212", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"display:flex;", "node_title": "输出", "node_seq_id": "213", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " flex-wrap:wrap; gap:10px", "node_title": "输出", "node_seq_id": "214", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:15px;\">\n", "node_title": "输出", "node_seq_id": "215", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        <span style=\"background:#", "node_title": "输出", "node_seq_id": "216", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "E9F5FF; color:#006", "node_title": "输出", "node_seq_id": "217", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6CC; padding:6px 12", "node_title": "输出", "node_seq_id": "218", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; border-radius:16px; font", "node_title": "输出", "node_seq_id": "219", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:14px;\">持", "node_title": "输出", "node_seq_id": "220", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "妆</span>\n        <span style=\"", "node_title": "输出", "node_seq_id": "221", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "background:#FF", "node_title": "输出", "node_seq_id": "222", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "EEF6; color:#CC006", "node_title": "输出", "node_seq_id": "223", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6; padding:6", "node_title": "输出", "node_seq_id": "224", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px 12px", "node_title": "输出", "node_seq_id": "225", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-radius:16px", "node_title": "输出", "node_seq_id": "226", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-size:14", "node_title": "输出", "node_seq_id": "227", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">遮瑕</span>\n", "node_title": "输出", "node_seq_id": "228", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        <span style=\"background:#FFF5", "node_title": "输出", "node_seq_id": "229", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "E6; color:#FF6600; padding", "node_title": "输出", "node_seq_id": "230", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":6px 12px; border", "node_title": "输出", "node_seq_id": "231", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:16", "node_title": "输出", "node_seq_id": "232", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; font-size:14px;\">", "node_title": "输出", "node_seq_id": "233", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "控油</span>\n        <span", "node_title": "输出", "node_seq_id": "234", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"background:#E6F", "node_title": "输出", "node_seq_id": "235", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "9EE; color:#009933; padding", "node_title": "输出", "node_seq_id": "236", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":6px 12px;", "node_title": "输出", "node_seq_id": "237", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:16px; font", "node_title": "输出", "node_seq_id": "238", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:14px;\">服帖</span", "node_title": "输出", "node_seq_id": "239", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n        <span style=\"background", "node_title": "输出", "node_seq_id": "240", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#F0E6FF; color:#", "node_title": "输出", "node_seq_id": "241", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6600CC; padding", "node_title": "输出", "node_seq_id": "242", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":6px 12px;", "node_title": "输出", "node_seq_id": "243", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:16px; font-size", "node_title": "输出", "node_seq_id": "244", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":14px;\">不卡粉</span", "node_title": "输出", "node_seq_id": "245", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n        <span style=\"", "node_title": "输出", "node_seq_id": "246", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "background:#E6F5", "node_title": "输出", "node_seq_id": "247", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "FF; color:#0066CC; padding", "node_title": "输出", "node_seq_id": "248", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":6", "node_title": "输出", "node_seq_id": "249", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px 12px; border-radius", "node_title": "输出", "node_seq_id": "250", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":16px; font-size:14", "node_title": "输出", "node_seq_id": "251", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">轻薄</span>\n      </div", "node_title": "输出", "node_seq_id": "252", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n      <p", "node_title": "输出", "node_seq_id": "253", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:15px;", "node_title": "输出", "node_seq_id": "254", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " line-height:1.6; color", "node_title": "输出", "node_seq_id": "255", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#555;\">典型", "node_title": "输出", "node_seq_id": "256", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "用户原话：\"10小时会议连", "node_title": "输出", "node_seq_id": "257", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "轴转都不脱妆\"、\"素", "node_title": "输出", "node_seq_id": "258", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "颜肌那种光，像天", "node_title": "输出", "node_seq_id": "259", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "生气色好\"、\"口罩一整天，不", "node_title": "输出", "node_seq_id": "260", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "沾\"</p>\n    </div>\n", "node_title": "输出", "node_seq_id": "261", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "  </div>\n\n ", "node_title": "输出", "node_seq_id": "262", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <!-- 投放策略 -->\n  <div style", "node_title": "输出", "node_seq_id": "263", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"margin-bottom:40px;\">\n", "node_title": "输出", "node_seq_id": "264", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "    <h2 style=\"font-size:", "node_title": "输出", "node_seq_id": "265", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "26px; font-weight:600;", "node_title": "输出", "node_seq_id": "266", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " margin-bottom:20px; color", "node_title": "输出", "node_seq_id": "267", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#222;\">📈 K", "node_title": "输出", "node_seq_id": "268", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "FS投放策略</h2>\n", "node_title": "输出", "node_seq_id": "269", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "    \n    <div style=\"background:#", "node_title": "输出", "node_seq_id": "270", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "F8F9FA; border", "node_title": "输出", "node_seq_id": "271", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:12px; padding:25", "node_title": "输出", "node_seq_id": "272", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; margin-bottom:25px;\">\n      <h", "node_title": "输出", "node_seq_id": "273", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "3 style=\"font-size:", "node_title": "输出", "node_seq_id": "274", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "20px; font-weight:600", "node_title": "输出", "node_seq_id": "275", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:15px; color:#222;\">", "node_title": "输出", "node_seq_id": "276", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "达人投放结构", "node_title": "输出", "node_seq_id": "277", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</h3>\n      \n      <div style=\"display:flex;", "node_title": "输出", "node_seq_id": "278", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " align-items:flex-end; height:200", "node_title": "输出", "node_seq_id": "279", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; gap:", "node_title": "输出", "node_seq_id": "280", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px; margin-bottom:20px", "node_title": "输出", "node_seq_id": "281", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n        <div style=\"flex:", "node_title": "输出", "node_seq_id": "282", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1; text-align:center;\">\n         ", "node_title": "输出", "node_seq_id": "283", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"height:40", "node_title": "输出", "node_seq_id": "284", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; background:#FF9A9", "node_title": "输出", "node_seq_id": "285", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "E; border-radius:6px 6", "node_title": "输出", "node_seq_id": "286", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px 0 0;\"></div", "node_title": "输出", "node_seq_id": "287", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n          <p style=\"font-size:12", "node_title": "输出", "node_seq_id": "288", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; margin-top:5", "node_title": "输出", "node_seq_id": "289", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">头部达人</p>\n         ", "node_title": "输出", "node_seq_id": "290", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <p style=\"font-size:12px; color", "node_title": "输出", "node_seq_id": "291", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#666;\">6人", "node_title": "输出", "node_seq_id": "292", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</p>\n        </div>\n        <", "node_title": "输出", "node_seq_id": "293", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"flex:2;", "node_title": "输出", "node_seq_id": "294", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " text-align:center;\">\n          <div", "node_title": "输出", "node_seq_id": "295", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"height:120px; background:#A", "node_title": "输出", "node_seq_id": "296", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0E7E5; border-radius:", "node_title": "输出", "node_seq_id": "297", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6px 6px ", "node_title": "输出", "node_seq_id": "298", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0 0;\"></div>\n         ", "node_title": "输出", "node_seq_id": "299", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <p style=\"font-size:12", "node_title": "输出", "node_seq_id": "300", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; margin-top:5px;\">腰部", "node_title": "输出", "node_seq_id": "301", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "达人</p>\n          <p style", "node_title": "输出", "node_seq_id": "302", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:12px; color", "node_title": "输出", "node_seq_id": "303", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#666;\">", "node_title": "输出", "node_seq_id": "304", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "250人</p>\n        </div>\n       ", "node_title": "输出", "node_seq_id": "305", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"flex:3", "node_title": "输出", "node_seq_id": "306", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; text-align:center;\">\n          <div", "node_title": "输出", "node_seq_id": "307", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"height:160px; background", "node_title": "输出", "node_seq_id": "308", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#B5EAD7;", "node_title": "输出", "node_seq_id": "309", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:6px 6px 0", "node_title": "输出", "node_seq_id": "310", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 0;\"></div>\n          <p", "node_title": "输出", "node_seq_id": "311", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:12", "node_title": "输出", "node_seq_id": "312", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; margin-top:5px;\">初级达人", "node_title": "输出", "node_seq_id": "313", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</p>\n          <", "node_title": "输出", "node_seq_id": "314", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p style=\"font", "node_title": "输出", "node_seq_id": "315", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:12px; color:#666;\">", "node_title": "输出", "node_seq_id": "316", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "794人</p>\n        </div>\n", "node_title": "输出", "node_seq_id": "317", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        <div style=\"flex", "node_title": "输出", "node_seq_id": "318", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":4; text-align:center;\">\n         ", "node_title": "输出", "node_seq_id": "319", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"height:80px; background", "node_title": "输出", "node_seq_id": "320", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#FFD166; border", "node_title": "输出", "node_seq_id": "321", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:6px 6px ", "node_title": "输出", "node_seq_id": "322", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0 0;\"></div>\n          <", "node_title": "输出", "node_seq_id": "323", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p style=\"font-size", "node_title": "输出", "node_seq_id": "324", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":12px; margin", "node_title": "输出", "node_seq_id": "325", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-top:5px;\">素", "node_title": "输出", "node_seq_id": "326", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "人</p>\n          <p style", "node_title": "输出", "node_seq_id": "327", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:12px; color", "node_title": "输出", "node_seq_id": "328", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#666;\">9,200+</", "node_title": "输出", "node_seq_id": "329", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p>\n       ", "node_title": "输出", "node_seq_id": "330", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n     ", "node_title": "输出", "node_seq_id": "331", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n      \n", "node_title": "输出", "node_seq_id": "332", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "      <div style=\"display:flex;", "node_title": "输出", "node_seq_id": "333", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " flex-wrap:wrap; gap:15", "node_title": "输出", "node_seq_id": "334", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">\n        <div style=\"flex:1", "node_title": "输出", "node_seq_id": "335", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; min-width:", "node_title": "输出", "node_seq_id": "336", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "200px; background:#fff; border", "node_title": "输出", "node_seq_id": "337", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:8px; padding:15", "node_title": "输出", "node_seq_id": "338", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">\n          <p style", "node_title": "输出", "node_seq_id": "339", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:14px; color:#666", "node_title": "输出", "node_seq_id": "340", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:5px", "node_title": "输出", "node_seq_id": "341", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">头部达人CPM</p>\n          <", "node_title": "输出", "node_seq_id": "342", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p", "node_title": "输出", "node_seq_id": "343", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:18px;", "node_title": "输出", "node_seq_id": "344", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-weight:600", "node_title": "输出", "node_seq_id": "345", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">554.01元</p>\n        </", "node_title": "输出", "node_seq_id": "346", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n       ", "node_title": "输出", "node_seq_id": "347", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"flex:1;", "node_title": "输出", "node_seq_id": "348", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " min-width:200px; background:#", "node_title": "输出", "node_seq_id": "349", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "fff; border-radius:8px; padding:", "node_title": "输出", "node_seq_id": "350", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px;\">\n", "node_title": "输出", "node_seq_id": "351", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <p style=\"font-size", "node_title": "输出", "node_seq_id": "352", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":14px; color:#666", "node_title": "输出", "node_seq_id": "353", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:5px;\">素", "node_title": "输出", "node_seq_id": "354", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "人CPM</p>\n          <p", "node_title": "输出", "node_seq_id": "355", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:18px;", "node_title": "输出", "node_seq_id": "356", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-weight:600", "node_title": "输出", "node_seq_id": "357", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">9.49元</p", "node_title": "输出", "node_seq_id": "358", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n        </div>\n      </div>\n   ", "node_title": "输出", "node_seq_id": "359", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n    \n    <div", "node_title": "输出", "node_seq_id": "360", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"background:#F8", "node_title": "输出", "node_seq_id": "361", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "F9FA; border-radius:", "node_title": "输出", "node_seq_id": "362", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; padding:25", "node_title": "输出", "node_seq_id": "363", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">\n      <h3 style=\"font", "node_title": "输出", "node_seq_id": "364", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:20px; font-weight:", "node_title": "输出", "node_seq_id": "365", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "600; margin-bottom", "node_title": "输出", "node_seq_id": "366", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":15px; color:#222;\">关键词矩阵", "node_title": "输出", "node_seq_id": "367", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "策略</h3>\n", "node_title": "输出", "node_seq_id": "368", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "      \n      <div style=\"display:flex", "node_title": "输出", "node_seq_id": "369", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; flex-wrap:wrap", "node_title": "输出", "node_seq_id": "370", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; gap:15px; margin", "node_title": "输出", "node_seq_id": "371", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:20px;\">\n        <div", "node_title": "输出", "node_seq_id": "372", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"flex:1; min-width", "node_title": "输出", "node_seq_id": "373", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":200px; background:#fff; border", "node_title": "输出", "node_seq_id": "374", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:8px; padding", "node_title": "输出", "node_seq_id": "375", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":15px;\">\n          <h4", "node_title": "输出", "node_seq_id": "376", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:16px; font", "node_title": "输出", "node_seq_id": "377", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-weight:600; margin-bottom:", "node_title": "输出", "node_seq_id": "378", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "10px; color:#222", "node_title": "输出", "node_seq_id": "379", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">防守25%</h4>\n          <ul style=\"padding", "node_title": "输出", "node_seq_id": "380", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-left:20px; margin:0", "node_title": "输出", "node_seq_id": "381", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-size:14px; color:#", "node_title": "输出", "node_seq_id": "382", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "555;\">\n            <li style=\"margin-bottom", "node_title": "输出", "node_seq_id": "383", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":5px;\">卡姿兰粉底", "node_title": "输出", "node_seq_id": "384", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "液</li>\n            <li", "node_title": "输出", "node_seq_id": "385", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"margin-bottom:5", "node_title": "输出", "node_seq_id": "386", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">卡姿兰无痕锁妆", "node_title": "输出", "node_seq_id": "387", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</li>\n          </ul>\n", "node_title": "输出", "node_seq_id": "388", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        </div>\n        \n        <div", "node_title": "输出", "node_seq_id": "389", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"flex:1; min-width:200px", "node_title": "输出", "node_seq_id": "390", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; background:#fff; border", "node_title": "输出", "node_seq_id": "391", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:8px; padding:15", "node_title": "输出", "node_seq_id": "392", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">\n          <h4 style=\"", "node_title": "输出", "node_seq_id": "393", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:16", "node_title": "输出", "node_seq_id": "394", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; font-weight:600;", "node_title": "输出", "node_seq_id": "395", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " margin-bottom:10px; color:#222;\">", "node_title": "输出", "node_seq_id": "396", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "进攻55%</h4>\n         ", "node_title": "输出", "node_seq_id": "397", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <ul style=\"padding-left", "node_title": "输出", "node_seq_id": "398", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; margin:0; font", "node_title": "输出", "node_seq_id": "399", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:14px; color:#", "node_title": "输出", "node_seq_id": "400", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "555;\">\n            <li style=\"margin-bottom:5", "node_title": "输出", "node_seq_id": "401", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">无痕粉底", "node_title": "输出", "node_seq_id": "402", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</li>\n            <li style=\"", "node_title": "输出", "node_seq_id": "403", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "margin-bottom:5px;\">锁妆粉底", "node_title": "输出", "node_seq_id": "404", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</li>\n            <", "node_title": "输出", "node_seq_id": "405", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "li style=\"margin-bottom:5px;\">", "node_title": "输出", "node_seq_id": "406", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "脱妆拯救</li>\n         ", "node_title": "输出", "node_seq_id": "407", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </ul>\n        </div>\n        \n", "node_title": "输出", "node_seq_id": "408", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        <div style=\"flex:1;", "node_title": "输出", "node_seq_id": "409", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " min-width:200px; background:#fff;", "node_title": "输出", "node_seq_id": "410", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:8px", "node_title": "输出", "node_seq_id": "411", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding:15px;\">\n          <h", "node_title": "输出", "node_seq_id": "412", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "4 style=\"font-size:16px", "node_title": "输出", "node_seq_id": "413", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-weight:600; margin-bottom", "node_title": "输出", "node_seq_id": "414", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":10px; color:#222;\">", "node_title": "输出", "node_seq_id": "415", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "长尾20%</h4>\n", "node_title": "输出", "node_seq_id": "416", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <ul style=\"padding-left", "node_title": "输出", "node_seq_id": "417", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; margin:0;", "node_title": "输出", "node_seq_id": "418", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-size:14px; color:#555;\">\n", "node_title": "输出", "node_seq_id": "419", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "            <li style=\"margin", "node_title": "输出", "node_seq_id": "420", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:5px;\">38℃户外妆", "node_title": "输出", "node_seq_id": "421", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</li>\n           ", "node_title": "输出", "node_seq_id": "422", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <li style=\"margin-bottom:5", "node_title": "输出", "node_seq_id": "423", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">素颜高光肌", "node_title": "输出", "node_seq_id": "424", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</li>\n          </ul>\n        </", "node_title": "输出", "node_seq_id": "425", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n      </div>\n    </div>\n", "node_title": "输出", "node_seq_id": "426", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "  </div>\n\n ", "node_title": "输出", "node_seq_id": "427", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <!-- 执行计划 -->\n  <div style", "node_title": "输出", "node_seq_id": "428", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"margin-bottom", "node_title": "输出", "node_seq_id": "429", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":40px;\">\n    <h2 style", "node_title": "输出", "node_seq_id": "430", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:26px;", "node_title": "输出", "node_seq_id": "431", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-weight:600; margin-bottom:", "node_title": "输出", "node_seq_id": "432", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "20px; color:#", "node_title": "输出", "node_seq_id": "433", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "222;\">📅 90天执行", "node_title": "输出", "node_seq_id": "434", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "计划</h2>\n    \n    <", "node_title": "输出", "node_seq_id": "435", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"", "node_title": "输出", "node_seq_id": "436", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "background:#F8F9FA;", "node_title": "输出", "node_seq_id": "437", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:12px; padding:", "node_title": "输出", "node_seq_id": "438", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "25px;\">\n      <div style=\"display", "node_title": "输出", "node_seq_id": "439", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex; margin-bottom:25", "node_title": "输出", "node_seq_id": "440", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">\n        <div style=\"flex", "node_title": "输出", "node_seq_id": "441", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":1; min-width:0; padding", "node_title": "输出", "node_seq_id": "442", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-right:15px;\">\n          <div", "node_title": "输出", "node_seq_id": "443", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"width:", "node_title": "输出", "node_seq_id": "444", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "100%; height:8px;", "node_title": "输出", "node_seq_id": "445", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " background:#E0E0E0;", "node_title": "输出", "node_seq_id": "446", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:4px; margin-bottom:", "node_title": "输出", "node_seq_id": "447", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "10px;\">\n            <div style=\"width", "node_title": "输出", "node_seq_id": "448", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":33", "node_title": "输出", "node_seq_id": "449", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "%; height:100%; background:#", "node_title": "输出", "node_seq_id": "450", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "FF9A9E; border-radius", "node_title": "输出", "node_seq_id": "451", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":4px;\"></div>\n          </div", "node_title": "输出", "node_seq_id": "452", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n          <h3 style=\"font-size:", "node_title": "输出", "node_seq_id": "453", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "18px; font-weight", "node_title": "输出", "node_seq_id": "454", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":600; margin-bottom:5px;\">", "node_title": "输出", "node_seq_id": "455", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "Month 1</h3>\n          <", "node_title": "输出", "node_seq_id": "456", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p style=\"font-size:14px;", "node_title": "输出", "node_seq_id": "457", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#", "node_title": "输出", "node_seq_id": "458", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "666;\">冷启动 & 认知", "node_title": "输出", "node_seq_id": "459", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "破圈</p>\n        </div>\n", "node_title": "输出", "node_seq_id": "460", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        \n        <div style=\"flex:", "node_title": "输出", "node_seq_id": "461", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1; min-width:0;", "node_title": "输出", "node_seq_id": "462", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " padding-right:15px", "node_title": "输出", "node_seq_id": "463", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n          <div style=\"width:100%;", "node_title": "输出", "node_seq_id": "464", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " height:8px; background", "node_title": "输出", "node_seq_id": "465", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#E0E0E0", "node_title": "输出", "node_seq_id": "466", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-radius:4px; margin-bottom", "node_title": "输出", "node_seq_id": "467", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":10px;\">\n            <div style", "node_title": "输出", "node_seq_id": "468", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"width:66%; height:100", "node_title": "输出", "node_seq_id": "469", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "%; background:#A0E7E5;", "node_title": "输出", "node_seq_id": "470", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:4px;\"></div", "node_title": "输出", "node_seq_id": "471", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n          </div>\n         ", "node_title": "输出", "node_seq_id": "472", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <h3 style=\"font-size:", "node_title": "输出", "node_seq_id": "473", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "18px; font-weight:600; margin", "node_title": "输出", "node_seq_id": "474", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:5px;\">Month 2</", "node_title": "输出", "node_seq_id": "475", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "h3>\n          <p", "node_title": "输出", "node_seq_id": "476", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:14px; color", "node_title": "输出", "node_seq_id": "477", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#666;\">放量 & 进攻痛点", "node_title": "输出", "node_seq_id": "478", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</p>\n        </div>\n        \n", "node_title": "输出", "node_seq_id": "479", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        <div style=\"flex:1", "node_title": "输出", "node_seq_id": "480", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; min-width:0;\">\n          <", "node_title": "输出", "node_seq_id": "481", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"width:100%;", "node_title": "输出", "node_seq_id": "482", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " height:8px; background:#E", "node_title": "输出", "node_seq_id": "483", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0E0E0; border", "node_title": "输出", "node_seq_id": "484", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:4px; margin-bottom:10px", "node_title": "输出", "node_seq_id": "485", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n            <div style=\"width:100", "node_title": "输出", "node_seq_id": "486", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "%; height:100%; background:#", "node_title": "输出", "node_seq_id": "487", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "B5EAD7; border", "node_title": "输出", "node_seq_id": "488", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:4px;\"></", "node_title": "输出", "node_seq_id": "489", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n          </div>\n         ", "node_title": "输出", "node_seq_id": "490", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <h3 style=\"font-size:18px", "node_title": "输出", "node_seq_id": "491", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-weight:600; margin", "node_title": "输出", "node_seq_id": "492", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:5px;\">Month", "node_title": "输出", "node_seq_id": "493", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 3</h3>\n          <p style", "node_title": "输出", "node_seq_id": "494", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:14px;", "node_title": "输出", "node_seq_id": "495", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#666;\">转化", "node_title": "输出", "node_seq_id": "496", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " & 长", "node_title": "输出", "node_seq_id": "497", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "尾固化</p>\n        </", "node_title": "输出", "node_seq_id": "498", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n      </div>\n      \n      <", "node_title": "输出", "node_seq_id": "499", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"background:#fff;", "node_title": "输出", "node_seq_id": "500", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:8px; padding:20", "node_title": "输出", "node_seq_id": "501", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; margin-bottom:", "node_title": "输出", "node_seq_id": "502", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px;\">\n        <h3 style=\"", "node_title": "输出", "node_seq_id": "503", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:20px;", "node_title": "输出", "node_seq_id": "504", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-weight:600; margin-bottom:15px; color", "node_title": "输出", "node_seq_id": "505", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#222;\">核心", "node_title": "输出", "node_seq_id": "506", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "指标</h3>\n        <", "node_title": "输出", "node_seq_id": "507", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"display", "node_title": "输出", "node_seq_id": "508", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex; flex-wrap", "node_title": "输出", "node_seq_id": "509", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":wrap; gap:15px;\">\n", "node_title": "输出", "node_seq_id": "510", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <div style=\"flex:", "node_title": "输出", "node_seq_id": "511", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1; min-width:200px;\">\n           ", "node_title": "输出", "node_seq_id": "512", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <p style=\"font-size:14", "node_title": "输出", "node_seq_id": "513", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; color:#666; margin-bottom", "node_title": "输出", "node_seq_id": "514", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":5px;\">累计阅读量</p>\n", "node_title": "输出", "node_seq_id": "515", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "            <p style=\"", "node_title": "输出", "node_seq_id": "516", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:18px; font-weight:", "node_title": "输出", "node_seq_id": "517", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "600;\">6,500万</p>\n", "node_title": "输出", "node_seq_id": "518", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          </div>\n          <div style", "node_title": "输出", "node_seq_id": "519", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"flex:1; min", "node_title": "输出", "node_seq_id": "520", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-width:200px;\">\n            <p style", "node_title": "输出", "node_seq_id": "521", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:14px; color", "node_title": "输出", "node_seq_id": "522", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#666; margin-bottom:5px", "node_title": "输出", "node_seq_id": "523", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">爆文数量</p>\n           ", "node_title": "输出", "node_seq_id": "524", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <p style=\"font-size:18px", "node_title": "输出", "node_seq_id": "525", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-weight:600;\">", "node_title": "输出", "node_seq_id": "526", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "350篇</p>\n          </div", "node_title": "输出", "node_seq_id": "527", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n          <div style=\"flex:1", "node_title": "输出", "node_seq_id": "528", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; min-width:200px;\">\n           ", "node_title": "输出", "node_seq_id": "529", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <p style=\"font-size:14", "node_title": "输出", "node_seq_id": "530", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; color:#666; margin-bottom:5", "node_title": "输出", "node_seq_id": "531", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">", "node_title": "输出", "node_seq_id": "532", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "CPE目标</p>\n            <p style", "node_title": "输出", "node_seq_id": "533", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:18px", "node_title": "输出", "node_seq_id": "534", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-weight:600;\">≤1.6", "node_title": "输出", "node_seq_id": "535", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "元</p>\n         ", "node_title": "输出", "node_seq_id": "536", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n        </", "node_title": "输出", "node_seq_id": "537", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n      </div>\n    </div>\n", "node_title": "输出", "node_seq_id": "538", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "  </div>\n\n  <!-- 总结", "node_title": "输出", "node_seq_id": "539", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " -->\n  <div style", "node_title": "输出", "node_seq_id": "540", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"background:linear-gradient(135deg, #F", "node_title": "输出", "node_seq_id": "541", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "8F9FA 0%, #", "node_title": "输出", "node_seq_id": "542", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "FFFFFF 100%); border-radius:12", "node_title": "输出", "node_seq_id": "543", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;", "node_title": "输出", "node_seq_id": "544", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " padding:30px; text-align:center", "node_title": "输出", "node_seq_id": "545", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n    <h2 style=\"font-size", "node_title": "输出", "node_seq_id": "546", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":26px; font-weight", "node_title": "输出", "node_seq_id": "547", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":600; margin-bottom:15", "node_title": "输出", "node_seq_id": "548", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; color:#222;\">✨ 预期成效", "node_title": "输出", "node_seq_id": "549", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</h2>\n", "node_title": "输出", "node_seq_id": "550", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "    <p", "node_title": "输出", "node_seq_id": "551", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:16px; line", "node_title": "输出", "node_seq_id": "552", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-height:1.6; color:#555", "node_title": "输出", "node_seq_id": "553", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:25", "node_title": "输出", "node_seq_id": "554", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">90天实现电商转化率提升32", "node_title": "输出", "node_seq_id": "555", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "%，线下专柜", "node_title": "输出", "node_seq_id": "556", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "客流同比", "node_title": "输出", "node_seq_id": "557", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "+19%</p>\n    <", "node_title": "输出", "node_seq_id": "558", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"display:inline-block;", "node_title": "输出", "node_seq_id": "559", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " background:#222; color:#fff; padding", "node_title": "输出", "node_seq_id": "560", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":12px 24px; border", "node_title": "输出", "node_seq_id": "561", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:30px; font-weight:", "node_title": "输出", "node_seq_id": "562", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "600; font", "node_title": "输出", "node_seq_id": "563", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:16px;\">无痕锁妆", "node_title": "输出", "node_seq_id": "564", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " · 持久美丽</div>\n ", "node_title": "输出", "node_seq_id": "565", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n</div>\n```", "node_title": "输出", "node_seq_id": "566", "node_is_finish": true, "token": null, "ext": null, "__internal__event": "Message"}, {"debugUrl": {"debug_url": "https://www.coze.cn/work_flow?execute_id=7524544413730471988&space_id=7509711081779593253&workflow_id=7514643074774368306&execute_mode=2"}, "__internal__event": "Done"}, "[DONE]"], "hasError": false}
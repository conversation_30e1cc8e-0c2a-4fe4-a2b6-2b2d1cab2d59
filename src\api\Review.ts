import { IS_DEV } from '@/config'
import { request } from '@/utils'

export class ReviewApi {
  /**
   * 违规检测
   */
  static async reviewContent(
    data: ReviewContentReq,
  ): Promise<ReviewContentResp[]> {
    if (IS_DEV) {
      return request.post('/app/review', data)
    }
    else {
      return [{
        pass: true,
        message: '审核通过',
        type: 'text',
      }]
    }
  }
}

export type ReviewContentReq = {
  /** 文本 */
  text?: string
  /** image-url */
  imageUrl?: string
}

export type ReviewContentResp = {
  /** 是否通过 */
  pass: boolean
  /** 违规详情（如果有） */
  message?: string
  /** 内容类型 */
  type?: string
}

import { cn } from '@/utils'
import { memo, useEffect, useRef, useState } from 'react'

export const GlowBorderWithHover = memo((props: GlowBorderWithHoverProps) => {
  const {
    className,
    children,
    borderSize = 2,
    gradientColors = ['#D89FFF', '#A7AFFF', '#5AC8FF', '#3AD2FF'],
    animationDuration = '4s',
    spin = true,
    // 新增属性
    showBackgroundOnHover = false,
    hoverBackground,
    selectedBackground,
    selected = false,
    onSelectedChange,
    ...rest
  } = props
  
  const containerRef = useRef<HTMLDivElement>(null)
  const [glowSize, setGlowSize] = useState(0)
  const [isHovered, setIsHovered] = useState(false)
  const [isSelected, setIsSelected] = useState(selected)

  useEffect(() => {
    setIsSelected(selected)
  }, [selected])

  useEffect(() => {
    if (!containerRef.current)
      return

    const updateSize = () => {
      const el = containerRef.current
      if (!el)
        return

      const { width, height } = el.getBoundingClientRect()
      const maxSize = Math.max(width, height)
      setGlowSize(maxSize * 1.4)
    }

    updateSize()

    const resizeObserver = new ResizeObserver(updateSize)
    resizeObserver.observe(containerRef.current)

    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current)
      }
      resizeObserver.disconnect()
    }
  }, [])

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (onSelectedChange) {
      const newSelected = !isSelected
      setIsSelected(newSelected)
      onSelectedChange(newSelected)
    }
    rest.onClick?.(e)
  }

  // 计算背景样式
  const getBackgroundStyle = () => {
    if (!showBackgroundOnHover) return undefined
    
    const shouldShowBackground = isHovered || isSelected
    if (!shouldShowBackground) return undefined

    if (isSelected && selectedBackground) {
      return selectedBackground
    }
    if (isHovered && hoverBackground) {
      return hoverBackground
    }
    
    // 默认背景渐变
    return isSelected
      ? 'linear-gradient(135deg, #FAF5FF 0%, #F6F8FF 50%, #F0FAFF 100%)'
      : 'linear-gradient(135deg, #FCFAFF 0%, #FAFBFF 50%, #F8FCFF 100%)'
  }

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-hidden', className)}
      style={{ padding: borderSize }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
      {...rest}
    >
      {glowSize > 0 && (
        <div
          style={{
            width: glowSize,
            height: glowSize,
            background: `conic-gradient(from 0deg, ${gradientColors.join(', ')}, ${gradientColors[0]})`,
            translate: '-50% -50%',
            animationDuration,
          }}
          aria-hidden="true"
          className={cn('absolute left-1/2 top-1/2 z-1', { 'animate-spin': spin })}
        />
      )}

      <div
        className="relative z-2 h-full w-full overflow-hidden bg-white dark:bg-gray-900"
        style={{
          borderRadius: 'inherit',
          transition: 'background 0.3s ease',
        }}
      >
        {/* 背景层 - 只在需要时显示 */}
        {showBackgroundOnHover && (isHovered || isSelected) && (
          <div 
            className="absolute inset-0 pointer-events-none"
            style={{
              background: getBackgroundStyle(),
              borderRadius: 'inherit',
            }}
          />
        )}
        {/* 内容层 */}
        <div className="relative z-10">
          {children}
        </div>
      </div>
    </div>
  )
})

GlowBorderWithHover.displayName = 'GlowBorderWithHover'

export type GlowBorderWithHoverProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & {
  children: React.ReactNode
  borderSize?: number
  gradientColors?: string[]
  animationDuration?: string
  spin?: boolean
  // 新增属性
  showBackgroundOnHover?: boolean
  hoverBackground?: string
  selectedBackground?: string
  selected?: boolean
  onSelectedChange?: (selected: boolean) => void
}
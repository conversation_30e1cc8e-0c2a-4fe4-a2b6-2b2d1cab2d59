import { useScrollBottom } from '@/hooks'
import { cn } from '@/utils'
import { isToBottom } from '@jl-org/tool'
import { forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react'

const InnerAutoScrollAnimate = forwardRef<AutoScrollAnimateRef, AutoScrollAnimateProps>((
  {
    children,
    autoScroll = true,
    fadeInMask = true,
    fadeInMaskHeight = 18,
    fadeInColor = '#ffffff',
    height = '100%',
    width = '100%',
    scrollBottomThreshold = 5,
    delay = 0,
    smooth = false,
    updateBy,
    className,
    containerClassName,
    style,
    ...rest
  },
  ref,
) => {
  const [shouldAutoScroll, setShouldAutoScroll] = useState(autoScroll)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isDownScroll, setIsDownScroll] = useState(true)

  const handleWheel = useCallback(
    (e: React.WheelEvent) => {
      setIsDownScroll(e.deltaY > 0)
    },
    [],
  )

  const checkIfNearBottom = useCallback(
    () => {
      if (!containerRef.current)
        return true

      return isToBottom(containerRef.current, scrollBottomThreshold)
    },
    [scrollBottomThreshold],
  )

  const handleScroll = useCallback(
    () => {
      if (!isDownScroll) {
        setShouldAutoScroll(false)
      }
      else if (checkIfNearBottom()) {
        setShouldAutoScroll(autoScroll)
      }
    },
    [autoScroll, checkIfNearBottom, isDownScroll],
  )

  const { scrollToBottom } = useScrollBottom(
    containerRef,
    [updateBy, shouldAutoScroll],
    {
      enabled: shouldAutoScroll,
      delay,
      smooth,
    },
  )

  useEffect(
    () => {
      if (updateBy || !containerRef.current) {
        return
      }

      const ob = new MutationObserver(() => {
        scrollToBottom()
      })

      ob.observe(containerRef.current, {
        childList: true,
        subtree: true,
        characterData: true,
      })

      return () => {
        ob.disconnect()
      }
    },
    [scrollToBottom, updateBy],
  )

  useEffect(() => {
    setShouldAutoScroll(autoScroll)
  }, [autoScroll])

  useImperativeHandle(ref, () => ({
    scrollToBottom,
    isDownScroll: () => isDownScroll,
    setAutoScroll: (enabled: boolean) => {
      setShouldAutoScroll(enabled)
      setIsDownScroll(enabled)
    },
  }))

  return (
    <div
      className={ cn(
        'relative overflow-hidden',
        containerClassName,
      ) }
      style={ {
        height,
        width,
        ...style,
      } }
      { ...rest }
    >
      <div
        ref={ containerRef }
        onWheel={ handleWheel }
        onScroll={ handleScroll }
        className={ cn(
          'h-full w-full overflow-y-auto overflow-x-hidden scroll-smooth',
          className,
        ) }
        style={ {
          scrollBehavior: 'smooth',
        } }
      >
        { children }
      </div>

      { fadeInMask && (
        <div
          className="pointer-events-none absolute left-0 right-0 top-0"
          style={ {
            background: `linear-gradient(to bottom, ${fadeInColor} 0%, #0000 100%)`,
            height: fadeInMaskHeight,
          } }
        />
      ) }

      { fadeInMask && (
        <div
          className="pointer-events-none absolute bottom-0 left-0 right-0"
          style={ {
            background: `linear-gradient(to top, ${fadeInColor} 0%, #0000 100%)`,
            height: fadeInMaskHeight,
          } }
        />
      ) }
    </div>
  )
})

export const AutoScrollAnimate = memo(InnerAutoScrollAnimate) as typeof InnerAutoScrollAnimate

AutoScrollAnimate.displayName = 'AutoScrollView'

export type AutoScrollAnimateRef = {
  scrollToBottom: () => void
  isDownScroll: () => boolean
  setAutoScroll: (enabled: boolean) => void
}

export type AutoScrollAnimateProps = {
  children: React.ReactNode
  autoScroll?: boolean
  fadeInMask?: boolean
  fadeInMaskHeight?: number
  fadeInColor?: string
  height?: string | number
  width?: string | number
  scrollBottomThreshold?: number
  delay?: number
  smooth?: boolean
  updateBy?: any
  className?: string
  containerClassName?: string
  style?: React.CSSProperties
}
& React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>

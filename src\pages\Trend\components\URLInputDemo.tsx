/**
 * URL输入框功能演示组件
 * 用于展示和测试URL输入框的各种功能
 */

import React, { useState } from 'react'
import { TrendWelcome } from './TrendWelcome'

export const URLInputDemo: React.FC = () => {
  const [urlValue, setUrlValue] = useState('')
  const [message, setMessage] = useState('')

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="mx-auto max-w-4xl">
        <div className="mb-8 rounded-lg bg-white p-6 shadow-sm">
          <h1 className="mb-4 text-2xl font-bold text-gray-800">
            URL输入框功能演示
          </h1>
          
          <div className="mb-6 space-y-4">
            <div>
              <h2 className="mb-2 text-lg font-semibold text-gray-700">功能说明：</h2>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start">
                  <span className="mr-2 text-blue-500">✓</span>
                  <span><strong>链接验证：</strong>实时验证输入的URL是否有效（支持http、https协议）</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 text-blue-500">✓</span>
                  <span><strong>动态显示：</strong>有效链接自动转换为可点击的超链接样式</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 text-blue-500">✓</span>
                  <span><strong>交互功能：</strong>点击链接在新标签页打开，双击进入编辑模式</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 text-blue-500">✓</span>
                  <span><strong>键盘操作：</strong>按ESC取消编辑，按Enter确认输入</span>
                </li>
              </ul>
            </div>
            
            <div>
              <h2 className="mb-2 text-lg font-semibold text-gray-700">测试用例：</h2>
              <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                <div>
                  <h3 className="font-medium text-green-600">有效URL示例：</h3>
                  <ul className="text-sm text-gray-600">
                    <li>• https://www.example.com</li>
                    <li>• http://example.com/path</li>
                    <li>• https://github.com/user/repo</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-medium text-red-600">无效URL示例：</h3>
                  <ul className="text-sm text-gray-600">
                    <li>• example.com (缺少协议)</li>
                    <li>• ftp://example.com (不支持的协议)</li>
                    <li>• not-a-url (无效格式)</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mb-4">
            <h2 className="mb-2 text-lg font-semibold text-gray-700">当前状态：</h2>
            <div className="rounded bg-gray-100 p-3 text-sm">
              <p><strong>URL值：</strong> {urlValue || '(空)'}</p>
              <p><strong>是否有效：</strong> {
                urlValue ? (
                  (() => {
                    try {
                      const url = new URL(urlValue)
                      return url.protocol === 'http:' || url.protocol === 'https:' 
                        ? <span className="text-green-600">是</span>
                        : <span className="text-red-600">否 (不支持的协议)</span>
                    } catch {
                      return <span className="text-red-600">否 (格式错误)</span>
                    }
                  })()
                ) : <span className="text-gray-500">-</span>
              }</p>
            </div>
          </div>
        </div>

        {/* TrendWelcome组件演示 */}
        <div className="rounded-lg bg-white shadow-sm">
          <TrendWelcome
            welcomeTitle="URL输入框功能测试"
            welcomeDescription="在下方的URL输入框中测试各种功能"
            message={message}
            onMessageChange={setMessage}
            urlInputValue={urlValue}
            onUrlInputChange={setUrlValue}
            inputPlaceholder="输入一些描述文本..."
            agentIcons={[]}
            quickActionButtons={[]}
          />
        </div>
        
        <div className="mt-8 rounded-lg bg-blue-50 p-6">
          <h2 className="mb-3 text-lg font-semibold text-blue-800">使用说明：</h2>
          <ol className="space-y-2 text-sm text-blue-700">
            <li><strong>1.</strong> 在URL输入框中输入一个链接（如：https://www.example.com）</li>
            <li><strong>2.</strong> 点击输入框外部或按Tab键使其失去焦点</li>
            <li><strong>3.</strong> 如果URL有效，它会自动转换为可点击的链接样式</li>
            <li><strong>4.</strong> 点击链接会在新标签页中打开</li>
            <li><strong>5.</strong> 双击链接可以重新进入编辑模式</li>
            <li><strong>6.</strong> 在编辑模式下按ESC键可以取消编辑</li>
          </ol>
        </div>
      </div>
    </div>
  )
}

export default URLInputDemo

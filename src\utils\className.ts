/**
 * 检测 className 字符串中是否包含宽度相关的 Tailwind CSS 类
 * @param className - 要检测的 className 字符串
 * @returns 是否包含宽度相关的类
 */
export function hasWidthClasses(className?: string): boolean {
  if (!className)
    return false

  /** 宽度相关的 Tailwind CSS 类的正则表达式 */
  const widthPatterns = [
    // w-* 类 (w-1, w-2, w-full, w-auto, w-fit, w-screen, w-min, w-max, w-1/2, w-1/3, etc.)
    /\bw-(?:auto|full|screen|min|max|fit|\d+(?:\/\d+)?|\d+(?:\.\d+)?(?:px|rem|em|%|vh|vw)?)\b/,
    // max-w-* 类 (max-w-xs, max-w-sm, max-w-md, max-w-lg, max-w-xl, max-w-2xl, etc.)
    /\bmax-w-(?:none|xs|sm|md|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|full|min|max|fit|prose|screen-\w+|\d+(?:\/\d+)?|\d+(?:\.\d+)?(?:px|rem|em|%|vh|vw)?)\b/,
    // min-w-* 类 (min-w-0, min-w-full, min-w-min, min-w-max, min-w-fit, etc.)
    /\bmin-w-(?:0|full|min|max|fit|\d+(?:\/\d+)?|\d+(?:\.\d+)?(?:px|rem|em|%|vh|vw)?)\b/,
    // flex 相关的宽度类 (flex-1, flex-auto, flex-initial, flex-none)
    /\bflex-(?:1|auto|initial|none)\b/,
    // grid 相关的列宽类 (col-span-*, col-start-*, col-end-*)
    /\bcol-(?:span-(?:1[0-2]|[1-9]|full)|start-(?:1[0-3]|[1-9]|auto)|end-(?:1[0-3]|[1-9]|auto))\b/,
  ]

  return widthPatterns.some(pattern => pattern.test(className))
}

/**
 * 智能合并 className，如果用户没有提供宽度相关的类，则添加默认宽度类
 * @param defaultWidthClass - 默认的宽度类（如 'w-full'）
 * @param userClassName - 用户传入的 className
 * @param otherClasses - 其他需要合并的类
 * @returns 合并后的 className 字符串
 */
export function mergeClassNameWithDefaultWidth(
  defaultWidthClass: string,
  userClassName?: string,
  ...otherClasses: (string | undefined)[]
): string {
  const classes: string[] = []

  /** 添加其他类 */
  otherClasses.forEach((cls) => {
    if (cls)
      classes.push(cls)
  })

  /** 如果用户没有提供宽度相关的类，则添加默认宽度类 */
  if (!hasWidthClasses(userClassName)) {
    classes.push(defaultWidthClass)
  }

  /** 添加用户的 className */
  if (userClassName) {
    classes.push(userClassName)
  }

  return classes.join(' ')
}

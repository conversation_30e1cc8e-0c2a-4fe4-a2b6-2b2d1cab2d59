import type { CSSProperties } from 'react'
import type { KeepAliveOpts } from './useKeepAlive'
import { vShow } from '@/hooks'
import classnames from 'clsx'
import { memo } from 'react'
import { KeepAliveProvider } from './KeepAliveProvider'
import { useKeepAlive } from './useKeepAlive'

function _KeepAliveRoute({
  style,
  className,
  ...rest
}: KeepAliveRouteProps) {
  const {
    pathname,
    componentMap,
  // eslint-disable-next-line react-hooks/rules-of-hooks
  } = useKeepAlive(rest)

  return <KeepAliveProvider>
    {
      Array.from(componentMap).map(([
        key,
        { Outlet, needCache },
      ]) =>
        needCache
          ? <div
              key={ key }
              className={ classnames(
                'size-full',
                className,
              ) }
              style={ {
                ...vShow(pathname === key),
                ...style,
              } }
            >
              { Outlet }
            </div>

          : pathname === key && <div
            key={ pathname }
            style={ style }
            className="relative size-full"
          >
            { Outlet }
          </div>,
      )
    }
  </KeepAliveProvider>
}

export const KeepAliveRoute = memo<KeepAliveRouteProps>(_KeepAliveRoute)
KeepAliveRoute.displayName = 'KeepAliveRoute'

export type KeepAliveRouteProps = {
  className?: string
  style?: CSSProperties
}
& KeepAliveOpts

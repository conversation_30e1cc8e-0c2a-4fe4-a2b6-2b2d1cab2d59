import type {
  PageQuery,
  PagerList,
} from '@/dts'

import { request } from '@/utils'

/**
 * Studio 资源相关 API
 */
export class StudioV3Api {
  /**
   * 不带文字的生图
   * 生图-(背景图片植入)
   */
  static async imgGenImg(params: ImgGenImgParam): Promise<{ resourceIds: string }> {
    return request.post('/ai/reference/object-replacement/bg-implant-image', params)
  }

  /**
   * 带文字的生图
   * 生图-(背景图片参考)
   */
  static async imgGenImgWithText(params: ImgGenImgWithTextParam): Promise<{ resourceIds: string }> {
    return request.post('/ai/reference/object-replacement/bg-reference-image', params)
  }

  /**
   * 历史记录查询（基础信息）
   */
  static async historyList(params: PageQuery<{ resourceIds: string[] }>): Promise<PagerList<ListBasicItem>> {
    return request.get('/app/resource/history/basic-detail', {
      params: {
        ...params,
        resourceIds: params.resourceIds.join(','),
      },
    })
  }

  /**
   * 历史记录分组查询
   */
  static async historyListGroupBy(params: PageQuery<{
    resourceTypes: ResourceType[]
    status?: GenStatusEnum
  }>): Promise<PagerList<GroupByBatchItem[]>> {
    return request.get('/app/resource/history/group-by-batch', {
      params: {
        ...params,
        resourceTypes: params.resourceTypes.join(','),
      },
    })
  }

  /**
   * 根据 id 查详情
   */
  static async detail(id: string): Promise<ImgDetail> {
    return request.get(`/app/resource/related-detail/${id}`)
  }
}

export type ImgGenImgParam = {
  /**
   * 物料id，和产品图只能传入一个
   */
  itemImageUrl?: string

  /**
   * 物料id，和产品图只能传入一个
   */
  materialInfoId?: string
  bgResources: BgResources
  resourceGenerationTypeEnum?: GenerationTypeEnum
}

/**
 * 1:商品主体不变，2:商品细节优先
 */
export enum Mode {
  KeepMain = 1,
  DetailsFirst = 2,
}

export type BgResources = {
  /**
   * 模板名
   */
  name?: string

  /**
   * 模板链接
   */
  bgUrl?: string

  /**
   * 蒙版链接
   */
  maskUrl?: string

  /**
   * 底版链接
   */
  underMaskUrl?: string

  /**
   * 场景  AIDrawing 必送
   */
  scene?: string

  /**
   * 风格  AIDrawing 必送
   */
  style?: string

  /**
   * obj旋转角
   */
  angle?: number

  /**
   * obj俯仰角
   */
  depressionAngle?: number

  /**
   * obj透视
   */
  focalLength?: number

  scale?: number
  offsetX?: number
  offsetY?: number
  lightDimmer?: number
  handleMask?: boolean
}

export type ImgGenImgWithTextParam = {
  /**
   * 物料id，和产品图只能传入一个
   */
  itemImageUrl?: string

  /**
   * 物料id，和产品图只能传入一个
   */
  materialInfoId?: string
  prompt: string
  bgResources?: BgResources
  resourceGenerationTypeEnum?: GenerationTypeEnum
  mode?: Mode
}

export enum GenerationTypeEnum {
  SIMILAR_INFERENCE = 'SIMILAR_INFERENCE',
  MULTI_ANGLE_INFERENCE = 'MULTI_ANGLE_INFERENCE',
  BACKGROUND_REPLACE_INFERENCE = 'BACKGROUND_REPLACE_INFERENCE',
  AT_INFERENCE = 'AT_INFERENCE',
  PIC_EXTEND = 'PIC_EXTEND',
  PRODUCT_CUT_OUT = 'PRODUCT_CUT_OUT',
  PRODUCT_BG_REPLACE_GENERATE = 'PRODUCT_BG_REPLACE_GENERATE',
  PICTURE_EXPAND = 'PICTURE_EXPAND',
  REMOVE_BACKGROUND = 'REMOVE_BACKGROUND',
  ERASE = 'ERASE',
  PARTIAL_REDRAWING = 'PARTIAL_REDRAWING',
  NEW_FREE_MODE = 'NEW_FREE_MODE',
  NEW_FREE_MODE_MODEL = 'NEW_FREE_MODE_MODEL',
  AT_INFERENCE_IMAGE = 'AT_INFERENCE_IMAGE',
}

export type GroupByBatchItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  url: string
  previewUrl: string
  status: GenStatusEnum
  source: string
  width: number
  height: number
  batchId: number
  materialId: number
  materialName: string
  collectionState: CollectionState
}

export type ListBasicItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  previewUrl: string
  watermarkUrl: string
  url: string
  status: GenStatusEnum
  source: string
  width: number
  height: number
  batchId: number
  materialId: string
}

export enum GenStatusEnum {
  FAILED = -1,
  GENERATING = 0,
  SUCCESS = 1,
}

/**
 * 资源类型
 * 4：AT-背景图仅参考
 * 12：背景植入-item图片
 * 13：背景植入-item为模型
 * 14-AT-item图片
 */
export enum ResourceType {
  AT = 4,
  BgReplace = 12,
  BgReplaceModel = 13,
  ATItem = 14,
}

/**
 * 收藏状态（普通：1，收藏：2）
 */
export enum CollectionState {
  UnCollection = 1,
  Collection = 2,
}

export type ImgDetail = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  previewUrl: string
  watermarkUrl: string
  url: string
  status: GenStatusEnum
  source: string
  width: number
  height: number
  batchId: number
  materialDetail: MaterialDetail
  missionDetail: MissionDetail
  materialId: number
  style: string
  type: 'public' | 'personal'
}

export type MissionDetail = {
  bgResourcePath: string
  bgResourceUrl: string
  maskPath: string
  maskUrl: string
  prompt: string
  angle: number
  depressionAngle: number
  focalLength: number
  scale: number
  offsetX: number
  offsetY: number
  lightDimmer: number
  handleMask: boolean
}

export type MaterialDetail = {
  materialId: number
  materialName: string
  historyId: number
  email: string
  brand: string
  designer: string
  coverImageRemoveBackgroundUrl: string
  price: number
}

import type { CSSProperties, ReactNode, Ref } from 'react'
import { LogoLoading } from '@/components/LogoLoading'

import { clamp, FakeProgress as Progress } from '@jl-org/tool'
import classnames from 'clsx'
import { forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react'
import { ProgressBar } from './ProgressBar'

function InnerFakeProgress({
  style,
  className,

  done,
  size = 150,
  onChange: _onChange,
  uniqueKey,
  colors = ['#3276F91A', '#01D0BD'],

  showLogo = true,
  showText = true,
  showBar = true,
  onlyProgressBar,

}: FakeProgressProps, ref: React.Ref<FakeProgressRef>) {
  const [val, setVal] = useState(0)

  const progress = useMemo(() => new Progress({
    autoStart: false,
    timeConstant: 30000, // 减少时间常数，使进度条更快
    initialProgress: uniqueKey
      ? +(localStorage.getItem(uniqueKey) || 0)
      : 0,

    onChange: (val) => {
      setVal(val)
      _onChange?.(val)
      uniqueKey && localStorage.setItem(uniqueKey, val.toString())

      val >= 0.95 && progress.stop()
    },
  }), [_onChange, uniqueKey])

  const clear = useCallback(() => {
    progress.end()
    progress.stop()
    uniqueKey && localStorage.removeItem(uniqueKey)
  }, [progress, uniqueKey])

  /** 将方法暴露给ref */
  useImperativeHandle(ref, () => ({
    getProgress: () => val,
    setProgress: (value: number) => {
      const clampedValue = clamp(value, 0, 1)
      progress.setProgress(clampedValue)
    },
    start: () => progress.start(),
    stop: () => progress.stop(),
    end: () => progress.end(),
    clear,
  }), [clear, val, progress])

  useEffect(() => {
    progress.start()
    return clear
  }, [clear, progress])

  useEffect(() => {
    if (!done)
      return
    clear()
  }, [clear, done])

  if (onlyProgressBar) {
    return <ProgressBar value={ val } />
  }

  return (<div
    className={ classnames(
      'absolute inset-0 flex flex-col items-center justify-center bg-gray-100',
      className,
    ) }
    style={ style }
  >
    { showLogo && <LogoLoading size={ size } /> }

    { showText && <p>
      <span>Estimated 2 minutes, please wait patiently... </span>
      <span className="ml-2 text-[#01D0BD]">
        { ' ' }
        { (val * 100).toString().slice(0, 5) }
        %
      </span>
    </p> }

    { showBar && <ProgressBar
      value={ val }
      colors={ colors }
      className="absolute bottom-0 z-5 w-full"
    /> }
  </div>)
}
InnerFakeProgress.displayName = 'FakeProgress'

export const FakeProgress = memo(forwardRef<FakeProgressRef, FakeProgressProps>(InnerFakeProgress))

export type FakeProgressProps = {
  className?: string
  style?: CSSProperties
  children?: ReactNode

  size?: number | string
  done?: boolean
  onChange?: (val: number) => void

  showLogo?: boolean
  showText?: boolean
  showBar?: boolean
  onlyProgressBar?: boolean
  colors?: string[]

  uniqueKey?: string
}

export type FakeProgressRef = {
  getProgress: () => number
  setProgress: (value: number) => void
  start: () => void
  stop: () => void
  end: () => void
  clear: () => void
}

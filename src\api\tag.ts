import { AxiosRequestConfig } from "axios";

import { request } from "@/utils";
import { PageQuery, PagerList, TagDO } from "@/dts";

const prefix = "/app/tag";

export const API_TAG = {
  // 查询列表
  async getPagerList(
    params: PageQuery<
      Partial<{
        categoryId: TagDO["id"];
        sort: string;
        name: TagDO["name"];
      }>
    >,
    cancelToken?: AxiosRequestConfig["cancelToken"],
  ): Promise<PagerList<TagDO>> {
    return request.get(`${prefix}/page`, { params, cancelToken });
  },
  // 查询列表
  async getList(
    params?: Partial<{
      categoryId: TagDO["id"];
      sort: string;
      name: Tag<PERSON><PERSON>["name"];
    }>,
    cancelToken?: AxiosRequestConfig["cancelToken"],
  ): Promise<TagDO[]> {
    return request.get(`${prefix}/list`, { params, cancelToken });
  },
};

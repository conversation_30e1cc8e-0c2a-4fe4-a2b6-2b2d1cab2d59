import { memo } from 'react'
import { styled } from 'styled-components'
import { Select as SelectComp, SelectProps } from 'antd'
import { useChangeSelectUI } from '@/hooks/useChangeSelectUI'


const Comp = styled(SelectComp)`
  .anticon.anticon-down.ant-select-suffix {
    opacity: 0;
  }
`

function _CusotmSelect(props: SelectProps) {

  /**
   * 修改 Select 箭头样式
   */
  useChangeSelectUI()

  return <Comp {...props}
  >
  </Comp>
}
_CusotmSelect.displayName = 'Select'

export const CusotmSelect = memo(_CusotmSelect)

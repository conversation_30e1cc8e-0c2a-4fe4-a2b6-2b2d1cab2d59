import type { ButtonProps } from 'antd/lib'
import { Button } from 'antd'
import cx from 'clsx'
import { memo } from 'react'
import SvgIcon from '../SvgIcon'

export const CostBtn = memo<CostBtnProps>((
  {
    style,
    className,
    cost,
    children,
    ...rest
  },
) => {
  return <Button
    className={ cx(
      'CostBtnContainer',
      className,
    ) }
    style={ style }
    type="primary"
    { ...rest }
  >
    { children }

    {
      cost != undefined && <div className="flex items-center gap-1">
        <SvgIcon icon="battery"></SvgIcon>
        <span>{ cost }</span>
      </div>
    }
  </Button>
})

CostBtn.displayName = 'CostBtn'

export type CostBtnProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  cost?: number
}
& ButtonProps

import { genShaderMaterial } from '@/shaderLibs'
import { genBall, genBallPosition, genParticles, GUI, loadTexture, ThreeScene } from '@/utils/three'
import { applyAnimation, Clock, DEG_90, DEG_180, DEG_360, getWinHeight, getWinWidth } from '@jl-org/tool'
import { clsx } from 'clsx'
import { memo } from 'react'
import * as THREE from 'three'

import atmosphereFragment from './shaders/atmosphereFragment.shader'
import atmosphereVertex from './shaders/atmosphereVertex.shader'
import fragmentShader from './shaders/fragment.shader'
import vertexShader from './shaders/vertex.shader'

export const GlobeSphere = memo<GlobeSphereProps>((
  {
    style,
    className,
  },
) => {
  const root = useRef<HTMLDivElement>(null)
  const size = { w: 400, h: 400 }
  const ratio = size.w / size.h

  const threeScene = new ThreeScene({
    camera: new THREE.PerspectiveCamera(
      75, /** 视角 */
      ratio, /** 比例 */
      0.1, /** 近平面 */
      100, /** 远平面 */
    ),
    renderer: new THREE.WebGLRenderer({
      antialias: true, /** 抗锯齿 */
      alpha: true,
    }),
  })

  const { camera, renderer } = threeScene
  const cameraPoint = [4, 0.5, 0.5] as const

  /**
   * Textures
   */
  const earthDayTexture = loadTexture('/private/textures/earth/day.jpg')
  const earthNightTexture = loadTexture('/private/textures/earth/night.jpg')
  const earthSpecularCloudsTexture = loadTexture('/private/textures/earth/specularClouds.jpg')

  useEffect(
    () => {
      if (!root.current)
        return

      threeScene.appendTo(root.current)
      threeScene.addOrbitControls()
      threeScene.autoResize()

      camera.position.set(...cameraPoint)

      const { material, mesh, outShadowMaterialUniform } = genEarth()
      const { uniforms } = material as any

      /**
       * 渲染与动画
       */
      const clock = new Clock()
      applyAnimation(() => {
        threeScene.render()

        const { elapsed } = clock
        mesh.rotation.y = elapsed * 0.08
      })

      function genEarth() {
        const { geometry, material, mesh } = genBall(
          genShaderMaterial({
            vertexShader,
            fragmentShader,
            uniforms: {
              uDayTexture: new THREE.Uniform(earthDayTexture),
              uNightTexture: new THREE.Uniform(earthNightTexture),
              uSpecularCloudsTexture: new THREE.Uniform(earthSpecularCloudsTexture),

              uSunDirection: new THREE.Uniform(new THREE.Vector3(0.25, 0, 1)),
              uCloudCount: new THREE.Uniform(0.001),

              /** 白天大气层色 */
              uAtmosphereDayColor: new THREE.Uniform(new THREE.Color('#00aaff')),
              /** 夜晚大气层暮光色 */
              uAtmosphereTwilightColor: new THREE.Uniform(new THREE.Color('#ff6600')),
            },
          }),
          2,
          64,
          64,
        )

        mesh.rotation.x = DEG_180
        threeScene.addToSence(mesh)
        const outShadowMaterialUniform = genOutShadow().uniforms

        return { geometry, material, mesh, outShadowMaterialUniform }

        function genOutShadow() {
          const outShadowMaterial = genShaderMaterial({
            /** 不遮挡地球 */
            side: THREE.BackSide,
            transparent: true,
            fragmentShader: atmosphereFragment,
            vertexShader: atmosphereVertex,

            uniforms: {
              uSunDirection: new THREE.Uniform(new THREE.Vector3(0.25, 0, 1)),
              /** 白天大气层色 */
              uAtmosphereDayColor: new THREE.Uniform(new THREE.Color('#00aaff')),
              /** 夜晚大气层暮光色 */
              uAtmosphereTwilightColor: new THREE.Uniform(new THREE.Color('#ff6600')),
            },
          })
          const mesh = new THREE.Mesh(
            geometry,
            outShadowMaterial,
          )

          const scale = 1.03
          mesh.scale.set(scale, scale, scale)
          threeScene.addToSence(mesh)

          return outShadowMaterial
        }
      }

      return () => {
        threeScene.dispose()
      }
    },
    [],
  )

  return <div
    className={ clsx(
      'GlobeSphereContainer',
      className,
    ) }
    style={ {
      width: 400,
      height: 400,
      ...style,
    } }
    ref={ root }
  >

  </div>
})

GlobeSphere.displayName = 'GlobeSphere'

export type GlobeSphereProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}

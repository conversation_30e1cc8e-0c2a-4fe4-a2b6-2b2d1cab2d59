/**
 * 开发环境性能优化配置
 * 用于在开发阶段提供更快的反馈速度，便于调试和测试功能
 */

export const DevPerformanceConfig = {
  /** 是否启用开发环境优化 */
  enabled: import.meta.env.DEV,

  /** 打字机效果优化 */
  typewriter: {
    /** 字符延迟（毫秒） */
    delayPerChar: 0.1,
    /** 最小延迟（毫秒） */
    minDelay: 0.5,
    /** 最大延迟（毫秒） */
    maxDelay: 2,
    /** 每次输出的字符数 */
    chunkSize: 8,
    /** 打字速度（毫秒/字符） */
    speed: 4,
  },

  /** Thinking 过程优化 */
  thinking: {
    /** 阶段更新间隔（毫秒） */
    stageUpdateInterval: 300,
    /** 步骤间延迟（秒） */
    staggerDelay: 0.02,
    /** 节流延迟（毫秒） */
    throttleDelay: 50,
  },

  /** 动画优化 */
  animation: {
    /** 卡片动画持续时间（秒） */
    cardTransition: 0.05,
    /** 内容动画持续时间（秒） */
    contentTransition: 0.1,
    /** 页面过渡时间（秒） */
    pageTransition: 0.1,
  },

  /** 卡片渲染优化 */
  cardRendering: {
    /** 是否批量创建卡片 */
    batchCreate: true,
    /** 卡片间隔时间（毫秒） */
    cardInterval: 0,
  },

  /** 数据处理优化 */
  dataProcessing: {
    /** 是否跳过不必要的延迟 */
    skipDelays: true,
    /** 是否启用快速模式 */
    fastMode: true,
  },
}

/**
 * 获取优化后的延迟时间
 */
export function getOptimizedDelay(originalDelay: number): number {
  if (!DevPerformanceConfig.enabled) {
    return originalDelay
  }
  return Math.min(originalDelay * 0.1, 50) // 减少到原来的10%，最大50ms
}

/**
 * 获取优化后的动画持续时间
 */
export function getOptimizedDuration(originalDuration: number): number {
  if (!DevPerformanceConfig.enabled) {
    return originalDuration
  }
  return Math.min(originalDuration * 0.25, 0.1) // 减少到原来的25%，最大0.1秒
}

/**
 * 获取优化后的打字机配置
 */
export function getOptimizedTypewriterConfig() {
  if (!DevPerformanceConfig.enabled) {
    return {
      delayPerChar: 1,
      minDelay: 2,
      maxDelay: 10,
      chunkSize: 2,
      speed: 16,
    }
  }
  return DevPerformanceConfig.typewriter
}

/**
 * 是否应该跳过延迟
 */
export function shouldSkipDelay(): boolean {
  return DevPerformanceConfig.enabled && DevPerformanceConfig.dataProcessing.skipDelays
}

/**
 * 获取优化后的 Thinking 配置
 */
export function getOptimizedThinkingConfig() {
  if (!DevPerformanceConfig.enabled) {
    return {
      stageUpdateInterval: 1500,
      staggerDelay: 0.1,
      throttleDelay: 50,
    }
  }
  return DevPerformanceConfig.thinking
}

/**
 * 开发环境性能日志
 */
export function logPerformanceOptimization(action: string, originalValue: number, optimizedValue: number) {
  if (DevPerformanceConfig.enabled && import.meta.env.DEV) {
    console.log(`🚀 [Dev Performance] ${action}: ${originalValue}ms → ${optimizedValue}ms (${Math.round((1 - optimizedValue / originalValue) * 100)}% faster)`)
  }
}

import type { god } from '@/god'
import { request } from '@/utils'
import { nanoid as generateUUID } from 'nanoid'

export class FileAPI {
  /**
   * 上传个人图片
   */
  static async uploadPrivateImg(name: string, path: string) {
    return request.post('app/bg-resource/private/upload-image', {
      name,
      path,
    })
  }

  /**
   * 获取oss的可下载签名链接（base64）
   */
  static async upBase64(data: {
    uploadFileDetails: UpItem[]
  }): Promise<UriResp> {
    const { uploadFileDetails } = data
    for (const item of uploadFileDetails) {
      if (item.imageStr.startsWith('data:')) {
        item.imageStr = item.imageStr.split(',')[1]
      }
    }

    const url = 'oss/base64/download-sign-url'
    return request.post(url, data)
  }

  /**
   * 获取单个 oss 的可下载签名链接（base64）
   */
  static async upSingleBase64(base64: string, name?: string): Promise<DownloadLoadFileDetail> {
    if (base64.startsWith('http')) {
      const newName = `${generateUUID()}.png`
      return {
        name: name || newName,
        url: base64,
        originalName: name || newName,
      }
    }

    const data = await FileAPI.upBase64({
      uploadFileDetails: [
        {
          name: name || (`${generateUUID()}.png`),
          imageStr: base64,
        },
      ],
    })

    return data.downloadLoadFileDetails[0]
  }

  /**
   * 通过上传图片换成url
   */
  static async upFileToUrl(fileList: File[]): Promise<UriResp> {
    const url = `oss/download-sign-url`
    const formData = new FormData()
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i]
      formData.append('uploadFileDetails', file)
    }
    return request.post(url, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  }

  // /**
  //  * 原生阿里云 OSS
  //  */
  // static async uploadOSS(...params: OssParams): Promise<DownloadLoadFileDetail> {
  //   const data = await god.oss.put(...params)
  //   return {
  //     name: params[1].name,
  //     originalName: data.name,
  //     url: data.url,
  //   }
  // }

  // /**
  //  * 批量上传原生阿里云 OSS
  //  */
  // static async uploadMultiOSS(params: OssMultiParams[]): Promise<UriResp> {
  //   const data = await Promise.all(params.map(p => FileAPI.uploadOSS(p.name, p.file, p.options)))
  //   return {
  //     downloadLoadFileDetails: data,
  //   }
  // }
}

type OssParams = Parameters<typeof god.oss.put>
type OssMultiParams = {
  name: OssParams[0]
  file: OssParams[1]
  options?: OssParams[2]
}

export type UpItem = {
  name: string
  imageStr: string
}

export type UriResp = {
  downloadLoadFileDetails: DownloadLoadFileDetail[]
}

export type DownloadLoadFileDetail = {
  /**
   * 文件名
   */
  name: string
  url: string
  /**
   * 你传递的名字
   */
  originalName: string
}

import { onMounted } from '@/hooks'
import { ToolBar } from '@/pages/PhotogV3/Studio/AIDrawing/Components/ToolBar.tsx'
import { addTimestampParam, BRUSH_COLOR, DEFAULT_STROKE_WIDTH } from '@/utils'
import { colorAddOpacity, cutoutImg as cutoutImgFn, cutoutImgToMask, getImg, NoteBoardWithBase64 } from '@jl-org/cvs'
import { useAsyncEffect } from 'ahooks'
import cx from 'clsx'
import { memo } from 'react'
import { Loading } from '../Loading'

/**
 * 抠图组件
 */
export const CutoutImg = memo<CutoutImgProps>((
  {
    style,
    className,

    rawImg,
    cutoutImg,
    toolArr,
    action,
    onToolBarClick,

    size = {
      width: 500,
      height: 500,
    },

    onLoading,
    onChangeMask,
    onChangePreviewImg,
  },
) => {
  const brushColor = colorAddOpacity(BRUSH_COLOR, 1)
  const [loading, setLoading] = useState(false)
  const [maskImg, setMaskImg] = useState('')

  // ======================
  // * 画板
  // ======================
  const previewContainer = useRef<HTMLDivElement>(null)
  const previewNoteboard = useRef<NoteBoardWithBase64>()
  const brushContainer = useRef<HTMLDivElement>(null)
  const brushNoteboard = useRef<NoteBoardWithBase64>()

  /***************************************************
   *                    Fns
   ***************************************************/
  const getPreviewImg = async () => {
    const rawImgCanvas = brushNoteboard.current
    const imgCanvas = previewNoteboard.current
    if (!rawImgCanvas || !imgCanvas)
      return ''

    const rawSrc = addTimestampParam(rawImg)
    const mask = await rawImgCanvas.exportMask({ exportOnlyImgArea: true })
    onChangeMask?.(mask)

    const originImg = await getImg(addTimestampParam(rawSrc), img => img.crossOrigin = 'anonymous') as HTMLImageElement
    const maskImg = await getImg(addTimestampParam(mask), img => img.crossOrigin = 'anonymous') as HTMLImageElement
    const img = await cutoutImgFn(originImg, maskImg)
    return img
  }

  async function drawImg(
    url: string,
    canvas?: NoteBoardWithBase64,
    beforeDraw?: VoidFunction,
  ) {
    if (!canvas)
      return

    const img = await getImg(addTimestampParam(url), img => img.crossOrigin = 'anonymous')
    if (!img) {
      canvas.clear()
    }

    beforeDraw?.()
    canvas.drawImg(img as HTMLImageElement, { autoFit: true, center: true, needClear: false })
  }

  /***************************************************
   *                    Init
   ***************************************************/
  onMounted(() => {
    if (!brushContainer.current! || !previewContainer.current)
      return

    // ======================
    // * 画板
    // ======================
    previewNoteboard.current = new NoteBoardWithBase64({
      el: previewContainer.current!,
      width: size.width as number,
      height: size.height as number,
    })
    previewNoteboard.current.setMode('none')
    previewNoteboard.current.isEnableZoom = false
    previewNoteboard.current.rmEvent()

    // ======================
    // * 绘制
    // ======================
    brushNoteboard.current = new NoteBoardWithBase64({
      el: brushContainer.current!,
      width: size.width as number,
      height: size.height as number,
      lineWidth: DEFAULT_STROKE_WIDTH,
      strokeStyle: brushColor,
      minScale: 0.4,

      async onMouseUp() {
        setLoading(true)
        onLoading?.(true)
        try {
          const previewImg = await getPreviewImg()

          if (previewImg) {
            onChangePreviewImg?.(previewImg)
            /**
             * 绘制变化的图片
             */
            drawImg(previewImg, previewNoteboard.current, () => previewNoteboard.current?.clear())
          }
        }
        finally {
          onLoading?.(false)
          setLoading(false)
        }
      },
    })

    brushNoteboard.current.setMode('draw')
    brushNoteboard.current.isEnableZoom = false
  })

  /***************************************************
   *                    watch
   ***************************************************/

  /**
   * 绘制预览图和涂抹图
   */
  useAsyncEffect(
    async () => {
      drawImg(addTimestampParam(cutoutImg), previewNoteboard.current, () => previewNoteboard.current?.clear())
      drawImg(addTimestampParam(rawImg), brushNoteboard.current)
    },
    [cutoutImg, rawImg],
  )

  /**
   * 设置初始遮罩
   */
  useAsyncEffect(
    async () => {
      const maskImg = await cutoutImgToMask(addTimestampParam(cutoutImg), brushColor)
      setMaskImg(maskImg.base64)
    },
    [cutoutImg],
  )

  /**
   * 画遮罩
   */
  useEffect(
    () => {
      if (!maskImg || !brushNoteboard.current)
        return

      brushNoteboard.current.clear(false, true)
      brushNoteboard.current.drawImg(maskImg, {
        autoFit: true,
        center: true,
        needClear: false,
        context: brushNoteboard.current.canvasList.find(item => item.name === 'brushCanvas')!.ctx,
      })
    },
    [maskImg],
  )

  return <div
    className={ cx(
      'CutoutImgContainer flex gap-4 flex-col bg-white',
      className,
    ) }
    style={ style }
  >
    {/* Toolbar */ }
    <ToolBar
      style={ {
        top: 16,
        left: '50%',
        transform: 'translateX(-50%)',
        width: 100,
        zIndex: 999,
      } }
      tools={ toolArr }
      activeIcon={ action as any }
      onClick={ key => onToolBarClick(key, brushNoteboard.current!) }
      onSetWidth={ (brushWidth) => {
        brushNoteboard.current?.setStyle({
          lineWidth: brushWidth,
        })
        brushNoteboard.current?.setCursor()
      } }
      className="absolute"
    />

    <div className="flex gap-2">
      <div
        className="overflow-hidden border rounded-md border-solid"
        ref={ previewContainer }
        style={ {
          backgroundImage: `url(${new URL('@/assets/transparentBg.png', import.meta.url).href})`,
          ...size,
        } }
      >
      </div>

      <div className="relative">
        <div
          className="overflow-hidden border rounded-md border-solid"
          ref={ brushContainer }
          style={ {
            backgroundImage: `url(${new URL('@/assets/transparentBg.png', import.meta.url).href})`,
            ...size,
          } }
        ></div>
        <Loading loading={ loading } />
      </div>
    </div>
  </div>
})

CutoutImg.displayName = 'CutoutImg'

export type CutoutImgProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode

  /**
   * 抠好的图
   */
  cutoutImg: string
  /**
   * 原图
   */
  rawImg: string
  toolArr: any
  action: string
  onToolBarClick: (key: string, canvas: NoteBoardWithBase64) => void

  size?: React.CSSProperties

  onChangeMask?: (mask: string) => void
  onChangePreviewImg?: (img: string) => void
  onLoading?: (loading: boolean) => void
}

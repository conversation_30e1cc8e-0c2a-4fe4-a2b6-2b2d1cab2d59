import { Drawer, Tabs } from "antd"
import { useEffect, useState } from "react"
import type { FC } from "react"

import { IS_PROD } from "@/config"

import styles from "./index.module.less"
import ThemeVars from "./ThemeVars"
import Hack from "./Hack"

const tabItems = IS_PROD
  ? [{ key: "hack", label: "ID", children: <Hack /> }]
  : [
    {
      key: "theme",
      label: "Antd主题变量",
      children: <ThemeVars />,
    },
  ]

const headerStyle = {
  padding: "6px",
}

const bodyStyle = {
  padding: "0 0 0 6px",
}

const maskStyle = {
  background: "none",
}

const QUICK_KEY = "shiftKey+KeyD"

/**
 * @description shift + D 开启/关闭
 * @returns Drawer
 */
const DTK: FC = () => {
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    const handleKeydown = (event: KeyboardEvent) => {
      const activeKeyStr = getActiveKeyDownStr(event)
      if (activeKeyStr === QUICK_KEY) {
        setVisible((v) => !v)
      }
    }
    document.body.addEventListener("keydown", handleKeydown)
    return () => {
      document.body.removeEventListener("keydown", handleKeydown)
    }
  }, [setVisible])

  return (
    <Drawer
      title={null}
      closable={false}
      placement="right"
      onClose={() => setVisible(false)}
      open={visible}
      size="default"
      styles={{
        header: headerStyle,
        body: bodyStyle,
        mask: maskStyle,
      }}
    >
      <Tabs className={styles.tabs} items={tabItems} />
    </Drawer>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export default DTK

function getActiveKeyDownStr(event: KeyboardEvent) {
  // 按照字母顺序，获取组合键信息
  const combineKeys = ["altKey", "ctrlKey", "metaKey", "shiftKey"]
  for (let j = combineKeys.length - 1; j >= 0; j -= 1) {
    const key = combineKeys[j]
    if (!event[key as keyof KeyboardEvent]) {
      combineKeys.splice(j, 1)
    }
  }
  combineKeys.push(event.code)
  return combineKeys.join("+")
}

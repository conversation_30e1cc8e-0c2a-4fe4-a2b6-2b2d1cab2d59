import { Button, Input, Space, message } from "antd";
import { useCallback, useState } from "react";
import { enableDevtools } from "@/utils";

export const HACK_KEY = "hack_id";

export const HACK_VALUE = "^#geCsdr*hgbN";

export default function Hack() {
  const [hackId, setHackId] = useState<string>("");

  const onHack = useCallback(() => {
    if (!hackId) {
      message.error("请输入密码");
      return;
    }
    
    if (HACK_VALUE !== hackId) {
      message.error("密码错误");
      return;
    }

    // 保存验证状态
    sessionStorage.setItem(HACK_KEY, hackId);
    
    // 启用开发者工具
    enableDevtools();
    
    message.success("开发者模式已启用");
    
    // 不需要刷新页面，直接生效
    // window.location.reload();
  }, [hackId]);

  return (
    <Space.Compact style={{ width: "100%" }}>
      <Input.Password 
        value={hackId} 
        onChange={(e) => setHackId(e.target.value)}
        placeholder="请输入密码"
        onPressEnter={onHack}
      />
      <Button type="primary" onClick={onHack}>
        解锁
      </Button>
    </Space.Compact>
  );
}

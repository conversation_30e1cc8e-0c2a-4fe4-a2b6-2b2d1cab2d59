varying vec2 vUv;
varying vec3 vNormal;
varying vec3 vPosition;

void main() {
    vec3 viewDirection = normalize(vPosition - cameraPosition);
    vec3 normal = normalize(vNormal);
    vec3 color = vec3(vUv, 1.0);

    float sunOrientation = dot(normalize(uSunDirection), normal);

    /**
     * 白天与黑夜
     */
    float dayMix = smoothstep(- 0.1, 0.8, sunOrientation);
    vec3 dayColor = texture(uDayTexture, vUv).rgb;
    vec3 nightColor = texture(uNightTexture, vUv).rgb;
    color = mix(nightColor, dayColor, dayMix);

    /**
     * 云层
     */
    vec2 specularCloudsColor = texture(uSpecularCloudsTexture, vUv).rg;
    float cloudMix = smoothstep(uCloudCount, 1.0, specularCloudsColor.g);
    // 消除夜晚的云层
    cloudMix *= dayMix;
    color = mix(color, vec3(1.0), cloudMix);

    /**
     * 大气层
     */
    float atmosphereDayMix = smoothstep(- 0.5, 1.0, sunOrientation);
    vec3 atmosphereColor = mix(uAtmosphereTwilightColor, uAtmosphereDayColor, atmosphereDayMix);

    // Fresnel，实现透镜效果
    float fresnel = dot(viewDirection, normal) + 1.0;
    // 幂越大，地球外光圈越靠外
    fresnel = pow(fresnel, 5.0);

    color = mix(color, atmosphereColor, fresnel * atmosphereDayMix);

    /**
     * 反射
     */
    vec3 reflection = reflect(-uSunDirection, normal);
    float specular = -dot(reflection, viewDirection);
    specular = max(specular, 0.0);
    // 越大反射光斑越小
    specular = pow(specular, 12.0);
    // 仅仅在海洋反射光斑
    specular *= specularCloudsColor.r;

    // 第一个参数是光斑色
    vec3 specularColor = mix(vec3(1.0), atmosphereColor, fresnel);
    color += specular * specularColor;

    // Final color
    gl_FragColor = vec4(color, 1.0);
    #include <tonemapping_fragment>
    #include <colorspace_fragment>
}
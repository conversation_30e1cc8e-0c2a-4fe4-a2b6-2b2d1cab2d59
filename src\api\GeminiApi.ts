// window.test = () => {
//   GeminiApi.stream({
//     onProgress: (content) => {
//       console.log(GeminiApi.combineAiMsgToStrAndMsg(content))
//     },
//     messages: [
//       {
//         part: {
//           text: '你好吗？',
//         },
//         role: 'user'
//       },
//     ],
//   })
// }


export class GeminiApi {
  static baseUrl = import.meta.env.VITE_API_GEMINI_BASE_URL

  static async stream({
    onProgress,
    messages,
  }: StreamOpts) {
    let loaded = 0

    let data: AiMsg[] = []
    const decoder = new TextDecoder()
    const { promise, reject, resolve } = Promise.withResolvers<AiMsg[]>()

    try {
      const resp = await fetch(`${GeminiApi.baseUrl}/generate-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify({ messages }),
      })

      const reader = resp.body!.getReader()
      const total = resp.headers.get('content-length')
        ? Number(resp.headers.get('content-length'))
        : 0

      let content = ''
      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          resolve(data)
          break
        }

        loaded += value.length
        content += decoder.decode(value)
        data = GeminiApi.parseAiMsg(content)
        onProgress(data, loaded / total)
      }
    }
    catch (error) {
      reject(error)
    }

    return promise
  }

  static parseAiMsg(data: string): AiMsg[] {
    const matches = data.match(/data:([\s\S]*?)(?=\ndata:|$)/g)
    if (!matches)
      return []

    return matches
      .filter(item => item.startsWith('data:{'))
      .map(item => JSON.parse(
        item
          .replace(/^data:/, '')
          .replace(/\n/g, '\\n'),
      ))
  }

  static combineAiMsgToStrAndMsg(msg: AiMsg[]) {
    const content = msg.map(
      item => item.candidates
        .map(
          item => (item.content.parts || [])
            .map(item => item.text)
            .join(''),
        )
        .join(''),
    )
      .join('')

    const imgs = msg.map(
      item => item.candidates
        .map(
          item => (item.content.parts || [])
            .map(item => item.inlineData)
            .filter(item => item && item.mimeType.startsWith('image/'))
            .map(item => item!.data)
            .filter(Boolean),
        ),
    )
      .flat(Infinity)

    return {
      content,
      imgs,
    }
  }
}

type StreamOpts = {
  messages: MsgItem[]
  onProgress: (data: AiMsg[], progress: number) => void
}

export type Role = 'user' | 'model'

export type AiMsg = {
  candidates: Candidate[]
  usageMetadata: UsageMetadata
  modelVersion: string
}

export type MsgItem = {
  role: Role
  part: {
    text: string
    imageUrls?: string[]
  }
}

export type UsageMetadata = {
  promptTokenCount: number
  candidatesTokenCount: number
  totalTokenCount: number
  promptTokensDetails: PromptTokensDetail[]
}

export type PromptTokensDetail = {
  modality: string
  tokenCount: number
}

export type Candidate = {
  content: Content
  index: number
}

export type Content = {
  parts?: Part[]
  role: Role
}

export type Part = {
  text?: string
  inlineData?: InlineData
}

export type InlineData = {
  mimeType: string
  data: string
}

import type { CheckboxChangeEvent } from 'antd/es/checkbox'
import RednoteReferenceList from '@/components/RednoteReferenceList'
import { userStore } from '@/store/userStore'
import { cn } from '@/utils'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { Checkbox } from 'antd'
import clsx from 'clsx'
import { motion } from 'framer-motion'
import { X } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { useSnapshot } from 'valtio'
import './TrendSelectionPage.module.css'

interface TrendTopic {
  id: string
  title: string
  isSelected?: boolean
}

interface TrendDetail {
  id: string
  content: string
  isReference: boolean
}

interface TrendSelectionPageProps {
  planning_report: string
  taskInstanceId?: string
  className: string
  handlerSelectTopic: (topicsId: string) => void
  storeInstance?: any // 传入 store 实例以存储数据
  onClose?: () => void // 添加关闭回调
}

export const TrendSelectionPage: React.FC<TrendSelectionPageProps> = ({
  className,
  handlerSelectTopic = () => { },
  taskInstanceId = '',
  planning_report = '',
  storeInstance,
  onClose,
}) => {
  console.log('[TrendSelectionPage] 组件挂载，接收到的 props:', {
    taskInstanceId,
    planning_report: planning_report
      ? `${planning_report.substring(0, 100)}...`
      : 'empty',
    hasTaskInstanceId: !!taskInstanceId,
    hasPlanningReport: !!planning_report,
    storeInstance: !!storeInstance,
    existingHotpotsData: storeInstance?.stateStore?.hotpotsTopics,
  })

  const token = userStore.token
  
  // 使用 useSnapshot 来监听 store 变化
  const stateSnapshot = useSnapshot(storeInstance?.stateStore || {})
  
  // 初始化时检查是否已有数据
  const initTopics = () => {
    const existingTopics: TrendTopic[] = []
    const existingDetails: Omit<TrendDetail, 'isReference'>[] = []
    
    if (storeInstance?.stateStore?.hotpotsTopics) {
      const hotpotsTopics = storeInstance.stateStore.hotpotsTopics
      for (let i = 1; i <= 5; i++) {
        const topicKey = `topic${i}`
        const detailKey = `topic${i}_detail`
        
        if (hotpotsTopics[topicKey]) {
          existingTopics.push({
            id: topicKey,
            title: hotpotsTopics[topicKey],
          })
          
          existingDetails.push({
            id: topicKey,
            content: hotpotsTopics[detailKey] || 'Loading details...',
          })
        }
      }
    }
    
    return { existingTopics, existingDetails }
  }
  
  const { existingTopics, existingDetails } = initTopics()
  
  const [topics, setTopics] = useState<TrendTopic[]>(existingTopics)
  const [topicsDetails, setTopicsDetails] = useState<Omit<TrendDetail, 'isReference'>[]>(existingDetails)
  const [preview, setPreview] = useState<TrendDetail>(
    existingDetails.length > 0 
      ? { ...existingDetails[0], isReference: false }
      : undefined
  )
  // 使用 store 中的状态，如果存在的话
  const [previewTopicId, setPreviewTopicId] = useState<string>(
    storeInstance?.stateStore?.trendSelectionState?.previewTopicId || 
    (existingDetails.length > 0 ? existingDetails[0].id : '')
  )
  const [selectedTopic, setSelectTopic] = useState<string>(storeInstance?.stateStore?.trendSelectionState?.selectedTopicId || '') // 选择为我的热点话题的ID
  const [selectedRednoteId, setSelectedRednoteId] = useState<string>('') // 选择的小红书笔记ID
  const [showRednoteList, setShowRednoteList] = useState<boolean>(storeInstance?.stateStore?.trendSelectionState?.showRednoteList ?? false) // 默认不显示，等待工作流触发后显示
  const [darenListLoaded, setDarenListLoaded] = useState<boolean>(true) // 默认为 true，让 RednoteReferenceList 立即加载数据
  const requestIdRef = useRef<string>('') // 使用 ref 存储请求标识，避免组件重新渲染导致的重复请求
  const hasInitializedRef = useRef<boolean>(false) // 使用 ref 追踪是否已初始化，组件卸载重新挂载也不会重置
  const hasFetchedDarenList = useRef<boolean>(false) // 标记是否已经请求过 distill_daren_list
  const hasFetchedHotpots = useRef<boolean>(false) // 标记是否已经请求过 hotpots_analysis
  const hasProcessedHotpotsData = useRef<boolean>(false) // 标记是否已经处理过 hotpots 数据
  const handlerPreviewTopic = (topicId: string) => {
    setPreviewTopicId(topicId)
    // 保存到 store
    if (storeInstance?.stateStore?.trendSelectionState) {
      storeInstance.stateStore.trendSelectionState.previewTopicId = topicId
    }
    const item = topicsDetails.find(item => item.id === topicId) as TrendDetail
    setPreview({
      ...item,
    })
    /** 选择话题时，自动滚动到右侧详情区域（移动端） */
    if (window.innerWidth < 1024) {
      setTimeout(() => {
        const detailElement = document.getElementById('trend-detail-section')
        detailElement?.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }, 100)
    }
  }

  /** 检查并更新 Start content creation 按钮显示状态 */
  const checkAndUpdateContentCreationButton = () => {
    // 检查 hotpots_analysis 是否完成
    const hasHotpotsData = storeInstance?.stateStore?.hotpotsTopics && 
      Object.keys(storeInstance.stateStore.hotpotsTopics).length >= 10 // 5个topic + 5个detail
    
    // 检查 distill_daren_list 是否完成
    const hasDistillData = storeInstance?.stateStore?.distillDarenListData &&
      (storeInstance.stateStore.distillDarenListData.KOC?.length > 0 ||
       storeInstance.stateStore.distillDarenListData.KOL?.length > 0 ||
       storeInstance.stateStore.distillDarenListData.Regulars?.length > 0)
    
    const shouldShowButton = hasHotpotsData && hasDistillData
    
    console.log('[TrendSelectionPage] 检查 Start content creation 按钮显示条件:', {
      hasHotpotsData,
      hasDistillData,
      shouldShowButton,
      hotpotsKeys: storeInstance?.stateStore?.hotpotsTopics ? Object.keys(storeInstance.stateStore.hotpotsTopics).length : 0,
      distillDataCounts: {
        KOC: storeInstance?.stateStore?.distillDarenListData?.KOC?.length || 0,
        KOL: storeInstance?.stateStore?.distillDarenListData?.KOL?.length || 0,
        Regulars: storeInstance?.stateStore?.distillDarenListData?.Regulars?.length || 0
      }
    })
    
    // 更新按钮显示状态
    if (shouldShowButton && storeInstance?.stateStore) {
      storeInstance.stateStore.showContentCreationButton = true
      console.log('[TrendSelectionPage] 已设置 showContentCreationButton = true')
      
      // 触发一个事件通知其他组件更新
      window.dispatchEvent(new CustomEvent('contentCreationButtonReady'))
    }
  }

  const fetchHotPots = () => {
    /** 防止重复调用 - 检查本地和全局状态 */
    if (hasFetchedHotpots.current || storeInstance?.stateStore?.hasTriggeredHotpotsAnalysis) {
      console.warn('[fetchHotPots] hotpots_analysis 已经触发过，跳过重复请求', {
        localFlag: hasFetchedHotpots.current,
        globalFlag: storeInstance?.stateStore?.hasTriggeredHotpotsAnalysis
      })
      return
    }

    /** 使用传入的 taskInstanceId 或从 store 获取 */
    const currentTaskInstanceId = taskInstanceId || storeInstance?.stateStore?.taskInstanceId

    if (!currentTaskInstanceId) {
      console.error('[fetchHotPots] 缺少 taskInstanceId')
      return
    }

    /** 标记已触发 - 同时设置本地和全局标记 */
    hasFetchedHotpots.current = true
    if (storeInstance?.stateStore) {
      storeInstance.stateStore.hasTriggeredHotpotsAnalysis = true
    }
    console.log('[fetchHotPots] 开始请求 hotpots_analysis，taskInstanceId:', currentTaskInstanceId)
    console.log('[fetchHotPots] 标记 hasFetchedHotpots = true (本地和全局)')

    fetchEventSource(`http://192.168.112.171:8080/api/app/market/stream/execute-main`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId: currentTaskInstanceId,
        platform: 'rednote',
        workflowName: 'hotpots_analysis',
        parameters: {
          platform: 'rednote',
          planning_report,
        },
      }),
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        const parsed = JSON.parse(ev.data)
        const topicTitleList = ['topic1', 'topic2', 'topic3', 'topic4', 'topic5']
        const topicDetailNodeTitle = ['topic1_detail', 'topic2_detail', 'topic3_detail', 'topic4_detail', 'topic5_detail']

        /** 存储 topics 数据到 store - 只更新 store，让 useEffect 处理渲染 */
        if (storeInstance && parsed.node_title && parsed.content) {
          if (topicTitleList.includes(parsed.node_title) || topicDetailNodeTitle.includes(parsed.node_title)) {
            if (!storeInstance.stateStore.hotpotsTopics) {
              console.error('[TrendSelectionPage] hotpotsTopics 不存在于 stateStore')
              storeInstance.stateStore.hotpotsTopics = {}
            }
            if (!storeInstance.stateStore.hotpotsTopics[parsed.node_title]) {
              storeInstance.stateStore.hotpotsTopics[parsed.node_title] = ''
            }
            storeInstance.stateStore.hotpotsTopics[parsed.node_title] += parsed.content
            console.log(`[fetchHotPots] 实时更新 ${parsed.node_title}:`, parsed.content.substring(0, 50))
            
            // 触发一个自定义事件，通知数据更新
            window.dispatchEvent(new CustomEvent('hotpotsDataUpdate', { 
              detail: { nodeTitle: parsed.node_title, content: parsed.content }
            }))
          }
        }
        
        // 移除直接设置 state 的代码，让 useEffect 统一处理
        // 这样可以避免重复渲染和状态不一致
      },
      onclose() {
        /**
         * 响应完成
         */
        console.log('[fetchHotPots] hotpots_analysis 工作流完成')
        
        /** 打印最终收集的数据 */
        if (storeInstance && storeInstance.stateStore) {
          console.log('[fetchHotPots] 最终 hotpotsTopics 数据:', {
            topics: Object.keys(storeInstance.stateStore.hotpotsTopics || {}),
            topic1_length: storeInstance.stateStore.hotpotsTopics?.topic1?.length,
            topic2_length: storeInstance.stateStore.hotpotsTopics?.topic2?.length,
            topic3_length: storeInstance.stateStore.hotpotsTopics?.topic3?.length,
            topic4_length: storeInstance.stateStore.hotpotsTopics?.topic4?.length,
            topic5_length: storeInstance.stateStore.hotpotsTopics?.topic5?.length,
          })
        }
        
        // 触发完成事件
        window.dispatchEvent(new CustomEvent('hotpotsComplete'))
      },
    })
  }

  // 处理小红书笔记选择
  const handleRednoteSelect = (noteId: string, checked: boolean) => {
    if (checked) {
      setSelectedRednoteId(noteId)
      // 清除话题选择
      setSelectTopic('')
      if (storeInstance?.stateStore?.trendSelectionState) {
        storeInstance.stateStore.trendSelectionState.selectedTopicId = ''
      }
      console.log('[TrendSelectionPage] 选择了小红书笔记，清除话题选择')
    } else {
      setSelectedRednoteId('')
    }
  }
  
  const handleReferenceToggle = (e: CheckboxChangeEvent, id: string) => {
    const { checked } = e.target
    id = checked
      ? id
      : ''
    handlerSelectTopic(id) // 回传给上层
    setSelectTopic(id)
    
    // 如果选择了话题，清除小红书的选择
    if (checked) {
      setSelectedRednoteId('')
      console.log('[TrendSelectionPage] 选择了话题，清除小红书选择')
    }
    
    // 保存到 store
    if (storeInstance?.stateStore?.trendSelectionState) {
      storeInstance.stateStore.trendSelectionState.selectedTopicId = id
    }

    console.log('[TrendSelectionPage] 选择了话题 ID:', id, '勾选状态:', checked)
  }

  useEffect(() => {
    console.log('[TrendSelectionPage] useEffect 触发:', {
      taskInstanceId,
      planning_report: planning_report
        ? `${planning_report.substring(0, 100)}...`
        : 'empty',
      hasTaskInstanceId: !!taskInstanceId,
      hasPlanningReport: !!planning_report,
      hasInitialized: hasInitializedRef.current,
      hasTriggeredTrendWorkflows: storeInstance?.stateStore?.hasTriggeredTrendWorkflows,
      hasTriggeredHotpotsAnalysis: storeInstance?.stateStore?.hasTriggeredHotpotsAnalysis,
      hasHotpotsTopics: !!storeInstance?.stateStore?.hotpotsTopics,
      hotpotsTopicsKeys: storeInstance?.stateStore?.hotpotsTopics ? Object.keys(storeInstance.stateStore.hotpotsTopics) : [],
      hasDistillDarenListData: !!storeInstance?.stateStore?.distillDarenListData,
      distillDarenListDataKOC: storeInstance?.stateStore?.distillDarenListData?.KOC?.length || 0,
      distillDarenListDataKOL: storeInstance?.stateStore?.distillDarenListData?.KOL?.length || 0,
    })

    // 移除这部分重复的逻辑，因为已经在事件监听器中处理了

    /** 只有当有 taskInstanceId 和 planning_report 时才处理 */
    if (taskInstanceId && planning_report) {
      /** 检查是否是第一次触发（通过全局状态判断） */
      const shouldTriggerWorkflows = storeInstance?.stateStore?.hasTriggeredTrendWorkflows && !hasInitializedRef.current

      if (shouldTriggerWorkflows) {
        console.log('[TrendSelectionPage] 第一次显示，检查是否需要触发工作流')
        hasInitializedRef.current = true

        /** 检查 hotpots_analysis 是否已经在 ChatPage 中触发 */
        if (storeInstance?.stateStore?.hasTriggeredHotpotsAnalysis) {
          console.log('[TrendSelectionPage] hotpots_analysis 已在 ChatPage 中触发，跳过')
        } else if (!storeInstance?.stateStore?.hotpotsTopics || Object.keys(storeInstance.stateStore.hotpotsTopics).length === 0) {
          // 只有在没有数据且没有触发过的情况下才调用
          console.log('[TrendSelectionPage] 没有 hotpots 数据且未触发过，调用 fetchHotPots')
          fetchHotPots()
        } else {
          console.log('[TrendSelectionPage] 已有 hotpots 数据，无需重复请求')
        }

        /** RednoteReferenceList 默认就显示，不需要特殊设置 */
        // setShowRednoteList(true) // 不需要，因为默认就是 true
        if (!hasFetchedDarenList.current) {
          // setDarenListLoaded(true) // 不需要，因为默认就是 true
          hasFetchedDarenList.current = true
          console.log('[TrendSelectionPage] 标记 hasFetchedDarenList = true，distill_daren_list 将自动触发')
        }
      }
      else if (hasInitializedRef.current) {
        console.log('[TrendSelectionPage] 已初始化过，恢复显示状态，不重新触发工作流')
        /** RednoteReferenceList 默认就显示，不需要特殊设置 */
        // setShowRednoteList(true) // 不需要，因为默认就是 true
        /** 不再设置 darenListLoaded，因为已经加载过了 */

        /** 如果已有数据，恢复显示 */
        if (storeInstance?.stateStore?.hotpotsTopics?.topic1) {
          /** 恢复 topics 数据 */
          const restoredTopics: TrendTopic[] = []
          const restoredDetails: Omit<TrendDetail, 'isReference'>[] = []

          for (let i = 1; i <= 5; i++) {
            const topicKey = `topic${i}` as keyof typeof storeInstance.stateStore.hotpotsTopics
            const detailKey = `topic${i}_detail` as keyof typeof storeInstance.stateStore.hotpotsTopics

            if (storeInstance.stateStore.hotpotsTopics[topicKey]) {
              restoredTopics.push({
                id: topicKey,
                title: storeInstance.stateStore.hotpotsTopics[topicKey],
              })
            }

            if (storeInstance.stateStore.hotpotsTopics[detailKey]) {
              restoredDetails.push({
                id: topicKey,
                content: storeInstance.stateStore.hotpotsTopics[detailKey],
              })
            }
          }

          if (restoredTopics.length > 0) {
            setTopics(restoredTopics)
            setTopicsDetails(restoredDetails)
            console.log('[TrendSelectionPage] 恢复了', restoredTopics.length, '个话题')
          }
        }
      }
      else {
        console.warn('[TrendSelectionPage] hasTriggeredTrendWorkflows 为 false，等待用户点击 Approve Strategy')
      }
    }
    else {
      console.warn('[TrendSelectionPage] 条件不满足，跳过工作流调用', {
        missingTaskInstanceId: !taskInstanceId,
        missingPlanningReport: !planning_report,
      })
    }
  }, [
    taskInstanceId, 
    planning_report, 
    storeInstance,
  ])

  // 监听 hotpotsTopics 数据变化（通过 useSnapshot）
  useEffect(() => {
    if (stateSnapshot?.hotpotsTopics) {
      const hotpotsTopics = stateSnapshot.hotpotsTopics as any
      const snapTopics: TrendTopic[] = []
      const snapDetails: Omit<TrendDetail, 'isReference'>[] = []
      
      console.log('[TrendSelectionPage] 检测到 hotpotsTopics 快照变化:', {
        topic1: !!hotpotsTopics.topic1,
        topic2: !!hotpotsTopics.topic2,
        topic3: !!hotpotsTopics.topic3,
        topic4: !!hotpotsTopics.topic4,
        topic5: !!hotpotsTopics.topic5,
      })
      
      for (let i = 1; i <= 5; i++) {
        const topicKey = `topic${i}`
        const detailKey = `topic${i}_detail`
        
        if (hotpotsTopics[topicKey]) {
          snapTopics.push({
            id: topicKey,
            title: hotpotsTopics[topicKey],
          })
          
          snapDetails.push({
            id: topicKey,
            content: hotpotsTopics[detailKey] || 'Loading details...',
          })
        }
      }
      
      if (snapTopics.length > 0) {
        console.log('[TrendSelectionPage] 通过 snapshot 更新数据:', {
          topicsCount: snapTopics.length,
          topics: snapTopics.map(t => t.title.substring(0, 30))
        })
        setTopics(snapTopics)
        setTopicsDetails(snapDetails)
        
        // 设置预览
        if (!preview && snapDetails.length > 0) {
          const firstDetail = snapDetails[0]
          setPreview({
            id: firstDetail.id,
            content: firstDetail.content,
            isReference: false,
          })
          setPreviewTopicId(firstDetail.id)
        } else if (preview && previewTopicId) {
          // 更新当前预览
          const currentDetail = snapDetails.find(d => d.id === previewTopicId)
          if (currentDetail && currentDetail.content !== preview.content) {
            setPreview({
              id: currentDetail.id,
              content: currentDetail.content,
              isReference: false,
            })
          }
        }
      }
    }
  }, [
    stateSnapshot?.hotpotsTopics?.topic1,
    stateSnapshot?.hotpotsTopics?.topic2,
    stateSnapshot?.hotpotsTopics?.topic3,
    stateSnapshot?.hotpotsTopics?.topic4,
    stateSnapshot?.hotpotsTopics?.topic5,
    stateSnapshot?.hotpotsTopics?.topic1_detail,
    stateSnapshot?.hotpotsTopics?.topic2_detail,
    stateSnapshot?.hotpotsTopics?.topic3_detail,
    stateSnapshot?.hotpotsTopics?.topic4_detail,
    stateSnapshot?.hotpotsTopics?.topic5_detail,
  ])

  // 监听 hotpots 数据更新事件
  useEffect(() => {
    const handleHotpotsUpdate = (event: CustomEvent) => {
      console.log('[TrendSelectionPage] 收到 hotpotsDataUpdate 事件:', event.detail)
      // 强制触发组件更新
      if (storeInstance?.stateStore?.hotpotsTopics) {
        const hotpotsTopics = storeInstance.stateStore.hotpotsTopics
        const newTopics: TrendTopic[] = []
        const newDetails: Omit<TrendDetail, 'isReference'>[] = []

        for (let i = 1; i <= 5; i++) {
          const topicKey = `topic${i}`
          const detailKey = `topic${i}_detail`

          if (hotpotsTopics[topicKey]) {
            newTopics.push({
              id: topicKey,
              title: hotpotsTopics[topicKey],
            })
            
            newDetails.push({
              id: topicKey,
              content: hotpotsTopics[detailKey] || 'Loading details...',
            })
          }
        }

        if (newTopics.length > 0) {
          setTopics(newTopics)
          setTopicsDetails(newDetails)
          
          // 更新预览内容
          if (previewTopicId) {
            const updatedDetail = newDetails.find(d => d.id === previewTopicId)
            if (updatedDetail) {
              setPreview({
                id: updatedDetail.id,
                content: updatedDetail.content,
                isReference: false,
              })
            }
          } else if (!preview && newDetails.length > 0) {
            const firstDetail = newDetails[0]
            setPreview({
              id: firstDetail.id,
              content: firstDetail.content,
              isReference: false,
            })
            setPreviewTopicId(firstDetail.id)
          }
        }
      }
    }

    const handleHotpotsComplete = () => {
      console.log('[TrendSelectionPage] 收到 hotpotsComplete 事件')
      checkAndUpdateContentCreationButton()
    }

    window.addEventListener('hotpotsDataUpdate', handleHotpotsUpdate as EventListener)
    window.addEventListener('hotpotsComplete', handleHotpotsComplete)

    return () => {
      window.removeEventListener('hotpotsDataUpdate', handleHotpotsUpdate as EventListener)
      window.removeEventListener('hotpotsComplete', handleHotpotsComplete)
    }
  }, [previewTopicId, preview, storeInstance])

  // 监听 store 中 showRednoteList 的变化
  useEffect(() => {
    if (storeInstance?.stateStore?.trendSelectionState?.showRednoteList !== undefined) {
      const shouldShow = storeInstance.stateStore.trendSelectionState.showRednoteList
      console.log('[TrendSelectionPage] 检测到 store 中 showRednoteList 变化:', shouldShow)
      setShowRednoteList(shouldShow)
    }
  }, [storeInstance?.stateStore?.trendSelectionState?.showRednoteList])

  // 监听 distillDarenListData 数据变化，确保数据到达后立即显示小红书列表
  useEffect(() => {
    if (storeInstance?.stateStore?.distillDarenListData) {
      const { KOC, KOL, Regulars, loading } = storeInstance.stateStore.distillDarenListData
      const hasData = (KOC?.length > 0 || KOL?.length > 0 || Regulars?.length > 0)
      
      console.log('[TrendSelectionPage] 检测到 distillDarenListData 变化:', {
        KOC: KOC?.length || 0,
        KOL: KOL?.length || 0,
        Regulars: Regulars?.length || 0,
        loading,
        hasData
      })
      
      // 如果有数据且不在加载中，自动显示小红书列表
      if (hasData && !loading) {
        console.log('[TrendSelectionPage] distillDarenListData 有数据，自动显示小红书列表')
        if (!showRednoteList) {
          setShowRednoteList(true)
        }
        
        // 检查并更新 Start content creation 按钮状态
        checkAndUpdateContentCreationButton()
      }
    }
  }, [
    storeInstance?.stateStore?.distillDarenListData?.KOC?.length,
    storeInstance?.stateStore?.distillDarenListData?.KOL?.length,
    storeInstance?.stateStore?.distillDarenListData?.Regulars?.length,
    storeInstance?.stateStore?.distillDarenListData?.loading
  ])


  return (
    <div className={ cn('w-full max-w-7xl mx-auto p-6 space-y-8 relative', className) }>
      
      {/* 关闭按钮 */}
      {onClose && (
        <button
          onClick={ onClose }
          className="absolute right-4 top-4 rounded-full bg-gray-100 p-2 transition-colors duration-200 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600"
          aria-label="Close"
        >
          <X className="h-5 w-5 text-gray-600 dark:text-gray-300" />
        </button>
      )}

      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-2xl text-gray-900 font-semibold dark:text-gray-100">
          Choose either one trending topic or a reference post
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          We will create your posts based on your choice
        </p>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Left Side - Trending Topics */}
        <div className="space-y-4">

          <div className="space-y-3">
            {topics.length === 0 ? (
              <div className="text-gray-500 text-center py-8">
                Loading trending topics...
              </div>
            ) : topics.map((topic, index) => (
              <motion.div
                key={ topic.id }
                initial={ { opacity: 0, y: 20 } }
                animate={ { opacity: 1, y: 0 } }
                transition={ { duration: 0.3, delay: index * 0.1 } }
                className={ clsx(`[background-clip:padding-box,border-box] [background-origin:padding-box,border-box] relative h-[74px] cursor-pointer overflow-hidden border-[1.5px] border-[#D9D9D9] rounded-[8px] transition-all duration-300 hover:border-[#DD9DFF] hover:shadow-md`, previewTopicId === topic.id && 'border-[#DD9DFF] shadow-md') }
                onClick={ () => handlerPreviewTopic(topic.id) }
              >
                {selectedTopic === topic.id && <span className="absolute right-[12px] top-[12px] items-center rounded-full bg-gray-100 px-2 py-0.5 text-sm text-gray-700">
                  Selected
                </span>}

                <div
                  className="relative ml-[14px] leading-[74px] transition-all duration-300"
                >
                  <h3 className="mb-1 text-gray-900 font-medium dark:text-gray-100">
                    {topic.title}
                  </h3>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Right Side - Trend Detail */}
        <div id="trend-detail-section" className="space-y-4">
          {!preview ? (
            <div className="[background-clip:padding-box,border-box] [background-origin:padding-box,border-box] relative overflow-hidden border-[1.5px] border-[#D9D9D9] rounded-2xl p-[16px] h-[410px] flex items-center justify-center">
              <p className="text-gray-500">Select a topic to preview details</p>
            </div>
          ) : (
            <motion.div
              initial={ { opacity: 0, x: 20 } }
              animate={ { opacity: 1, x: 0 } }
              transition={ { duration: 0.4 } }
              className="[background-clip:padding-box,border-box] [background-image:linear-gradient(to_right,#fff,#fff),linear-gradient(90deg,#DD9DFF_0%,#36D3FF_100%)] [background-origin:padding-box,border-box] relative overflow-hidden border-[1.5px] border-transparent rounded-2xl transition-all duration-300"
            >
              <div
                className="h-[410px] p-[16px] transition-all duration-300 space-y-4"
                style={ {
                  background: preview.id === selectedTopic
                    ? 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)'
                    : 'transparent',
                } }
              >
                <div className="h-[calc(100%-39px)] overflow-auto space-y-3">
                  <p className="text-gray-700 leading-relaxed dark:text-gray-300">
                    {preview.content}
                  </p>
                </div>

                {/* 复选框在面板内部 */}
                <div className="w-full flex items-center justify-end gap-3 dark:border-gray-600">
                  <Checkbox onChange={ e => handleReferenceToggle(e, preview.id) } checked={ preview.id === selectedTopic }>Select as my reference</Checkbox>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* 小红书参考列表 - 始终渲染但可能隐藏 */}
      <motion.div
        initial={ { opacity: 0, y: 20 } }
        animate={ { opacity: showRednoteList
          ? 1
          : 0, y: showRednoteList
          ? 0
          : 20 } }
        transition={ { duration: 0.5 } }
        className="mt-8"
        style={ { display: showRednoteList
          ? 'block'
          : 'none' } }
      >
        <RednoteReferenceList
          handleSelectNote={ (noteId: string, checked: boolean) => {
            console.log('[TrendSelectionPage] 小红书笔记选择变化:', noteId, checked)
            handleRednoteSelect(noteId, checked)
          } }
          selectedNoteId={ selectedRednoteId }
          useDynamicData
          taskInstanceId={ taskInstanceId }
          shouldFetch={ darenListLoaded }
          storeInstance={ storeInstance }
        />
      </motion.div>

    </div>
  )
}

export default TrendSelectionPage

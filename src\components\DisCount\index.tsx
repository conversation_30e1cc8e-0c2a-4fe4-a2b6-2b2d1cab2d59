import { cn } from '@/utils'

interface DiscountPriceProps {
  originalPrice: number
  discountedPrice?: number
  currency?: string
  className?: string
  originalPriceClassName?: string
  discountedPriceClassName?: string
}

export function Discount({
  originalPrice,
  discountedPrice,
  currency = '$',
  className,
  originalPriceClassName,
  discountedPriceClassName,
}: DiscountPriceProps) {
  const discount = discountedPrice
    ? Math.round(((originalPrice - discountedPrice) / originalPrice) * 100)
    : 0

  return (
    <div className={ cn(
      'flex items-center gap-2 font-medium text-gray-900',
      className,
    ) }>

      {/* 原价 */ }
      <div
        className={ cn(
          'relative text-lg text-gray-500 self-end',
          originalPriceClassName,
        ) }
      >
        <div
          className={ cn(
            'relative inline-block',
          ) }
        >
          { currency }
          { originalPrice }

          <div className="absolute left-0 top-1/2 h-[2px] w-full transform bg-current -rotate-20"></div>
        </div>
      </div>

      {/* 折后价 */ }
      { discountedPrice && (
        <div className={ cn(
          'text-lg relative',
          discountedPriceClassName,
        ) }>
          { currency }
          { discountedPrice }

          { discountedPrice && (
            <div className="absolute right-0 rounded bg-red-500 px-1 text-xs text-white -top-3">
              -
              { discount }
              %
            </div>
          ) }
        </div>
      ) }
    </div>
  )
}

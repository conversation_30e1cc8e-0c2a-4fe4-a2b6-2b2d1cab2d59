import React, { useEffect, useMemo, useRef, useState } from 'react'

interface TypewriterTextProps {
  text: string
  speed?: number // 每个字符的延迟（毫秒）
  onComplete?: () => void
  className?: string
  style?: React.CSSProperties
}

export const TypewriterText: React.FC<TypewriterTextProps> = ({
  text,
  speed = 30,
  onComplete,
  className = '',
  style = {},
}) => {
  const [displayedText, setDisplayedText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    /** 重置状态当文本改变时 */
    setDisplayedText('')
    setCurrentIndex(0)
  }, [text])

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayedText(prev => prev + text[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, speed)

      return () => clearTimeout(timer)
    }
    else if (currentIndex === text.length && onComplete) {
      onComplete()
    }
  }, [currentIndex, text, speed, onComplete])

  return (
    <div className={ className } style={ style }>
      {displayedText}
      {currentIndex < text.length && <span className="animate-pulse">|</span>}
    </div>
  )
}

interface TypewriterMessagesProps {
  messages: Array<{ id: string, text: string }>
  speed?: number
  delayBetweenMessages?: number // 消息之间的延迟
  className?: string
  style?: React.CSSProperties
}

export const TypewriterMessages: React.FC<TypewriterMessagesProps> = ({
  messages,
  speed = 30,
  delayBetweenMessages = 500,
  className = '',
  style = {},
}) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0)
  const [displayedMessages, setDisplayedMessages] = useState<Array<{ id: string, text: string, isComplete: boolean }>>([])
  const processedMessagesRef = useRef<Set<string>>(new Set())

  useEffect(() => {
    /** 重置状态当消息改变时 */
    setCurrentMessageIndex(0)
    setDisplayedMessages([])
    processedMessagesRef.current.clear()
  }, [messages])

  useEffect(() => {
    if (currentMessageIndex < messages.length) {
      const currentMessage = messages[currentMessageIndex]
      if (!processedMessagesRef.current.has(currentMessage.id)) {
        processedMessagesRef.current.add(currentMessage.id)
        setDisplayedMessages(prev => [...prev, { ...currentMessage, isComplete: false }])
      }
    }
  }, [currentMessageIndex, messages])

  const handleMessageComplete = (messageId: string) => {
    setDisplayedMessages(prev =>
      prev.map(msg => msg.id === messageId
        ? { ...msg, isComplete: true }
        : msg),
    )

    /** 延迟后显示下一条消息 */
    setTimeout(() => {
      if (currentMessageIndex < messages.length - 1) {
        setCurrentMessageIndex(prev => prev + 1)
      }
    }, delayBetweenMessages)
  }

  return (
    <div className={ className } style={ style }>
      {displayedMessages.map(msg => (
        <TypewriterText
          key={ msg.id }
          text={ msg.text }
          speed={ speed }
          onComplete={ () => handleMessageComplete(msg.id) }
          className="text-gray-600"
          style={ { fontSize: 'clamp(11px, 1.1vw, 13px)', marginBottom: '4px' } }
        />
      ))}
    </div>
  )
}

/** 新增：流式累积文本组件 */
export const StreamingText: React.FC<{
  text: string
  speed?: number
  className?: string
  style?: React.CSSProperties
}> = ({ text, speed = 20, className = '', style = {} }) => {
  const [displayedText, setDisplayedText] = useState('')
  const containerRef = useRef<HTMLDivElement>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const currentIndexRef = useRef(0)
  const targetTextRef = useRef('')

  useEffect(() => {
    /** 当传入新文本时 */
    if (text && text !== targetTextRef.current) {
      console.log('🎯 StreamingText 收到新文本，长度:', text.length)
      targetTextRef.current = text

      /** 如果是第一次或完全不同的文本，重置 */
      if (currentIndexRef.current === 0 || !text.startsWith(displayedText)) {
        console.log('📝 开始打字机效果...')
        setDisplayedText('')
        currentIndexRef.current = 0

        /** 清除之前的定时器 */
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }

        /** 启动打字机效果 */
        intervalRef.current = setInterval(() => {
          if (currentIndexRef.current < text.length) {
            const nextIndex = currentIndexRef.current + 1
            const newText = text.substring(0, nextIndex)
            setDisplayedText(newText)
            currentIndexRef.current = nextIndex

            /** 自动滚动 */
            if (containerRef.current?.parentElement) {
              const parent = containerRef.current.parentElement
              requestAnimationFrame(() => {
                parent.scrollTop = parent.scrollHeight
              })
            }
          }
          else {
            /** 完成后清除定时器 */
            console.log('✅ 打字机效果完成')
            if (intervalRef.current) {
              clearInterval(intervalRef.current)
              intervalRef.current = null
            }
          }
        }, speed)
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [text, speed])

  /** 处理文本格式 */
  const formattedText = useMemo(() => {
    /** 处理换行符 */
    let processed = displayedText
    /** 如果文本中包含 \n（字面量），转换为真正的换行 */
    if (processed.includes('\\n')) {
      processed = processed.replace(/\\n/g, '\n')
    }
    return processed
  }, [displayedText])

  return (
    <div
      ref={ containerRef }
      className={ className }
      style={ {
        ...style,
        whiteSpace: 'pre-wrap',
        wordBreak: 'break-word',
      } }
    >
      {formattedText}
      {currentIndexRef.current < targetTextRef.current.length && (
        <span className="ml-0.5 inline-block animate-pulse">|</span>
      )}
    </div>
  )
}

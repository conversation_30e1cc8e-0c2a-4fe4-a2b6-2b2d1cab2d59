import type { SizeStyle } from '@/types/Component'
import type { VariantProps } from 'class-variance-authority'
import { cn } from '@/utils'
import { cva } from 'class-variance-authority'
import { memo } from 'react'

const sizeStyles: SizeStyle = {
  sm: 'h-4 text-[10px]',
  md: 'h-5',
  lg: 'h-6 text-sm',
}

const badgeVariants = cva(
  'inline-flex items-center justify-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors text-white',
  {
    variants: {
      variant: {
        default: 'bg-blue-500 dark:bg-blue-500',
        secondary: 'bg-gray-500 dark:bg-gray-500',
        tip: 'bg-red-600 dark:bg-red-600',
        outline: 'border border-gray-200 text-gray-700 dark:border-gray-700 dark:text-gray-200',
        success: 'bg-emerald-500 dark:bg-emerald-500',
        warning: 'bg-amber-500 dark:bg-amber-500',
      },
      size: {
        sm: sizeStyles.sm,
        md: sizeStyles.md,
        lg: sizeStyles.lg,
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  },
)

export interface BadgeProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'content'>,
  VariantProps<typeof badgeVariants> {
  count?: number
  dot?: boolean
  maxCount?: number
  showZero?: boolean
  content?: React.ReactNode
}

const Badge = memo(
  ({
    className,
    variant,
    size,
    count,
    dot = false,
    maxCount = 99,
    showZero = false,
    content,
    ...props
  }: BadgeProps) => {
    const displayCount = count && count > maxCount
      ? `${maxCount}+`
      : count

    if (dot) {
      return (
        <div
          className={ cn(
            'relative inline-flex',
            className,
          ) }
          { ...props }
        >
          <div
            className={ cn(
              badgeVariants({ variant, size }),
              'absolute -right-1 -top-1 h-2 w-2 p-0',
            ) }
          />
          { props.children }
        </div>
      )
    }

    if (!count && !showZero && !content) {
      return <>{ props.children }</>
    }

    return (
      <div
        className={ cn(
          'relative inline-flex',
          className,
        ) }
        { ...props }
      >
        { props.children }
        <div
          className={ cn(
            badgeVariants({ variant, size }),
            'absolute -right-2 -top-2',
          ) }
        >
          { content || displayCount }
        </div>
      </div>
    )
  },
)

Badge.displayName = 'Badge'

export { Badge, badgeVariants }

import type { AiChatTaskConditions } from './ChatApi'
import { extractLinks, http, request } from '@/utils'
import { deepClone } from '@jl-org/tool'

export class CozeApi {

  /**
   * 发起会话-CZ
   */
  static async createCZAgent(data?: AiCreateConversionReq): Promise<AiAgentResp> {
    return request.post('/app/agent/create-cz', data)
  }

  /**
   * 通用流式-对话
   */
  static async streamCommonCoze(
    {
      onMsg,
      conversationId,
      contentList,
      imgUrl,
      prompt
    }: AiCreateChatReq,
  ) {
    let _content = ''
    let _reason = ''

    const history: CZContent[] = contentList || []

    if (imgUrl) {
      history.push({
        type: 'image_url',
        url: imgUrl,
      })
    }
    if (prompt) {
      history.push({
        type: 'text',
        prompt,
      })
    }

    const { promise } = await http.fetchSSE('/app/agent/stream/chat-cz', {
      method: 'POST',
      body: {
        conversationId,
        contentList: history
      },
      needParseData: true,
      needParseJSON: true,
      onMessage(data) {
        const { content, reasoning_content } = CozeApi.parseCozeMediaMsg(data.allJson as CozeMediaMsg[])
        onMsg?.({ content, reason: reasoning_content })

        _content = content || ''
        _reason = reasoning_content || ''
      },
    })

    await promise
    console.log({
      content: _content,
      reason: _reason,
    })
    return {
      content: _content,
      reason: _reason,
    }
  }

  /**
   * 提交任务，扣除用户积分
   */
  static async confirmTask(conversationId: string, aiChatTaskConditions: AiChatTaskConditions[]) {
    return request.post('/app/chat-task/condition', { aiChatTaskConditions, conversationId })
  }

  static parseCozeMediaMsg(data: CozeMediaMsg[]) {
    const content = data.map((item) => item.content).join('')
    const reasoning_content = data.map((item) => item.reasoning_content).join('')

    return {
      content,
      reasoning_content,
    }
  }

  /**
   * Coze-执行
   */
  static async streamGenImage(
    data: AiGenImageReq,
    onMsg?: (msg: MsgAndId) => void
  ) {
    const url = '/app/ai-conversation/stream/coze-gen'
    let resp: MsgAndId = {
      content: '',
      id: '',
      msg: [],
      title: '',
      type: -1,
    }

    const { promise } = await http.fetchSSE(url, {
      method: 'POST',
      body: data,
      onMessage(data) {
        resp = CozeApi.getMsgAndId(data.allJson as any)
        onMsg?.(resp)
      },
    })

    await promise
    return resp
  }

  /**
   * 重新-Coze执行
   */
  static async streamResumeGenImage(
    data: AiResumeGenImageReq,
    onMsg?: (msg: MsgAndId & { finalImgData?: FinalImgData }) => void
  ) {
    const urls: string[] = []
    let resp: MsgAndId = {
      content: '',
      id: '',
      msg: [],
      title: '',
      type: -1,
    }

    const { promise } = await http.fetchSSE('/app/ai-conversation/stream/resume-coze-gen', {
      method: 'POST',
      body: data,
      onMessage({ allJson, currentJson }) {
        resp = CozeApi.getMsgAndId(allJson as any)
        const hasFinalData = isFinalData(currentJson)
        console.log(currentJson)

        onMsg?.({
          ...resp,
          ...(hasFinalData && { finalImgData: deepClone(currentJson) as unknown as FinalImgData })
        })
      },
    })

    function isFinalData(data: any) {
      if (Array.isArray(data?.content?.images)) {
        return true
      }
      return false
    }

    await promise
    return {
      ...resp,
      urls,
    }
  }

  static getMsgAndId(data: (CozeId | CozeMsg)[]): MsgAndId {
    const msg = data.filter(item => !('interrupt_data' in item)) as CozeMsg[]
    const idData = data.filter(item => ('interrupt_data' in item)) as CozeId[]

    const content = CozeApi.getContent(msg)
    const title = CozeApi.getTitle(msg)

    return {
      msg,
      id: idData[0]?.interrupt_data?.event_id || '',
      type: idData[0]?.interrupt_data?.type || -1,
      content,
      title,
    }
  }

  static getContent(cozeMsg: CozeMsg[] = []) {
    return cozeMsg.map(item => item?.content || '').join('')
  }

  static getTitle(cozeMsg: CozeMsg[] = []) {
    return cozeMsg.map(item => item?.node_title || '').join('')
  }
}


export type AiCreateConversionReq = {
  /** Prompt和URl分开输入，图片：type=image_url，其他文件：type=file_url */
  contentList: CZContent[]
}

export type AiAgentResp = {
  conversationId: string
}

export type CZContent = {
  type: 'text' | 'image_url' | 'file_url'
  url?: string
  prompt?: string
}

export type AiCreateChatReq = {
  conversationId: string
  /** Prompt和URl分开输入，图片：type=image_url，其他文件：type=file_url */
  contentList?: CZContent[]
  imgUrl?: string
  prompt?: string
  onMsg?: (data: { content: string, reason: string }) => void
}

export type CozeMediaMsg = {
  audio: string
  role: string
  type: string
  content: string
  content_type: string
  meta_data: null
  id: string
  conversation_id: string
  section_id: string
  bot_id: string
  chat_id: string
  created_at: null
  updated_at: null
  reasoning_content: null
  __internal__event: string
}

export type FinalImgData = {
  content: {
    images: string[]
    markdown: string
    video: string
  }
}

export type AiGenImageReq = {
  /**
   * 图片 URL
   */
  imageUrl: string

  /**
   * 描述文本
   */
  prompt: string
}

export type AiResumeGenImageReq = {
  /**
   * 事件 ID
   */
  eventId: string

  /**
   * 中断类型（int32）
   */
  interruptType: number

  /**
   * 恢复数据
   */
  resumeData: string
}

export type CozeMsg = {
  content: string
  node_title: string
  node_seq_id: string
  node_is_finish: boolean
  token: null
  ext: null
}

export type CozeId = {
  interrupt_data: Interruptdata
  node_title: string
}

export type Interruptdata = {
  event_id: string
  type: number
}

export type MsgAndId = {
  msg: CozeMsg[]
  id: string
  type: number
  content: string
  title: string
}
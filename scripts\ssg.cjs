const path = require('node:path')
const { ssg } = require('@jl-org/ssg')
const puppeteer = require('puppeteer-core')

const mode = process.argv[2] || 'development'
const PORT = '4173'
main()

async function main() {
  await ssg({
    port: PORT,
    createBrowser: () => puppeteer.launch({
      executablePath: 'C:/Program Files/Google/Chrome/Application/chrome.exe',
      headless: true,
    }),
    startServerCmd: 'npx',
    startServerArgs: ['vite', 'preview', '--port', PORT, '--mode', mode],
    ssgPages: [
      {
        url: `http://localhost:${PORT}`,
        target: path.resolve(__dirname, '../dist/index.html'),
      },
      {
        url: `http://localhost:${PORT}/event`,
        target: path.resolve(__dirname, '../dist/event.html'),
      },
      {
        url: `http://localhost:${PORT}/blog`,
        target: path.resolve(__dirname, '../dist/blog.html'),
      },
      {
        url: `http://localhost:${PORT}/pricing`,
        target: path.resolve(__dirname, '../dist/pricing.html'),
      },
      {
        url: `http://localhost:${PORT}/about`,
        target: path.resolve(__dirname, '../dist/about.html'),
      },
      {
        url: `http://localhost:${PORT}/news`,
        target: path.resolve(__dirname, '../dist/news.html'),
      },
    ],
    onBeforeOpenPage(page) {
      page.setCookie(genCookies('localhost'))
    },
  })

  process.exit(0)
}

function genCookies(domain) {
  return {
    name: 'no-img',
    value: 'no-img',
    domain,
  }
}

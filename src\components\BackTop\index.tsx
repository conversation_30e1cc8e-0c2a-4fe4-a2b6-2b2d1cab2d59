import type { BackTopProps } from 'antd'
import type { FC } from 'react'
import { FloatButton } from 'antd'

import styles from './index.module.less'

const BackTop: FC<BackTopProps> = (props) => {
  return (
    <FloatButton.BackTop
      className={ styles['back-top'] }
      type="primary"
      tooltip="回到顶部"
      visibilityHeight={ 400 }
      shape="circle"
      { ...props }
    />
  )
}

export default BackTop

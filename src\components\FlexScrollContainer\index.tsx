import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from "react"
import type {
  CSSProperties,
  ReactNode,
  PropsWithRef,
  PropsWithChildren,
} from "react"
import classNames from 'clsx'
import { useMount } from "ahooks"

import styles from "./index.module.less"

export interface Props extends PropsWithChildren {
  wrapperClassName?: string
  wrapperStyle?: CSSProperties
  className?: string
  style?: CSSProperties
  header?: ReactNode
  footer?: ReactNode
  onScrollInfo?: (scrollTop: number, offsetHeight: number) => void
  onLoadMore?: (page: number) => Promise<boolean>
  hasMore?: boolean
  initialLoad?: boolean
}

const scrollThreshold = 2

export const FlexScrollContainer = forwardRef<
  HTMLDivElement,
  PropsWithRef<Props>
>(
  (
    {
      children,
      wrapperClassName,
      wrapperStyle,
      className,
      style,
      header,
      footer,
      onScrollInfo,
      onLoadMore,
      hasMore = false,
      initialLoad = false,
    },
    ref,
  ) => {
    const wrapperRef = useRef(null)
    const innerRef = useRef(null)

    const ticking = useRef<boolean>(false)
    const loading = useRef<boolean>(false)
    const page = useRef<number>(1)
    const lastScrollTop = useRef(0)

    const withExtra = useMemo(
      () => Boolean(header || footer),
      [header, footer],
    )

    useImperativeHandle(
      ref,
      () =>
        (!withExtra
          ? wrapperRef.current
          : innerRef.current) as unknown as HTMLDivElement,
    )

    const onScrollHandle = useCallback(
      (e: Event) => {
        if (!ticking.current) {
          ticking.current = true
          requestAnimationFrame(() => {
            const { scrollHeight, scrollTop, offsetHeight } =
              e.target as HTMLDivElement
            // 离滚动区域底部距离
            const scrollBottom = scrollHeight - offsetHeight - scrollTop

            if (onScrollInfo) {
              onScrollInfo(scrollTop, offsetHeight)
            }

            if (
              hasMore &&
              onLoadMore &&
              !loading.current &&
              scrollThreshold > scrollBottom &&
              lastScrollTop.current < scrollTop
            ) {
              loading.current = true
              onLoadMore(page.current)
                .then((success) => {
                  if (success) {
                    page.current++
                  }
                  loading.current = false
                })
                .catch(() => {
                  loading.current = false
                })
            }

            lastScrollTop.current = scrollTop
            ticking.current = false
          })
        }
      },
      [onScrollInfo, onLoadMore, hasMore],
    )

    useMount(() => {
      if (!onScrollInfo && !onLoadMore) {
        return
      }

      const currentRef = (!withExtra
        ? wrapperRef.current
        : innerRef.current) as unknown as HTMLDivElement

      setTimeout(() => {
        onScrollInfo?.(currentRef.scrollTop, currentRef.offsetHeight)
      }, 1)

      if (onLoadMore) {
        if (initialLoad) {
          loading.current = true
          onLoadMore(page.current)
            .then((success) => {
              if (success) {
                page.current++
              }
              loading.current = false
            })
            .catch(() => {
              loading.current = false
            })
        } else {
          page.current++
        }
      }
    })

    useEffect(() => {
      const currentRef = (!withExtra
        ? wrapperRef.current
        : innerRef.current) as unknown as HTMLDivElement

      currentRef?.addEventListener("scroll", onScrollHandle)

      return () => {
        currentRef?.removeEventListener("scroll", onScrollHandle)
      }
    }, [onScrollHandle, withExtra])

    return (
      <div
        ref={wrapperRef}
        className={classNames(
          "hide-scrollbar",
          styles.flexScrollContainer,
          {
            [styles.withExtra]: withExtra,
          },
          wrapperClassName,
        )}
        style={{
          ...wrapperStyle
        }}
      >
        {withExtra ? (
          <>
            {header}

            <div
              ref={innerRef}
              className={classNames(styles.innerContainer, className)}
              style={style}
            >
              {children}
            </div>

            {footer}
          </>
        ) : (
          children
        )}
      </div>
    )
  },
)

import type {
  <PERSON><PERSON><PERSON><PERSON>,
  PagerList,
  ReferencePO,
  ResourceDO,
  ResourceState,
  TaskStatus,
} from '@/dts'

import type { CollectionState } from './squareAPI'
import { request } from '@/utils'

/**
 * Studio 资源相关 API
 */
export const API_RESOURCE = {

  /**
   * 获取历史记录列表
   */
  async getList(params: ResourceState['query'] & {
    status?: TaskStatus
  }): Promise<PagerList<ResourceDO>> {
    return request.get(`/app/resource/history`, {
      params: {
        ...params,
        resourceType: '4,12',
      },
    })
  },

  /**
   * Picture 列表
   */
  async getListFour(params: PageQuery<{
    resourceTypes: ResourceType[]
  }>): Promise<PagerList<HistoryItem[]>> {
    return request.get('/app/resource/history/group-by-batch', {
      params: {
        ...params,
        resourceTypes: params.resourceTypes.join(','),
      },
    })
  },
  /**
   * Scene 列表
   */
  async sceneListMore(params: PageQuery): Promise<PagerList<BgResourceItem[]>> {
    return request.get(`/app/ai-bg-resource/group-by-batch`, { params })
  },

  /**
   * 历史记录图片下载
   */
  async download(p: {
    resourceIds: ResourceDO['id'][]
  }): Promise<{ url: string }> {
    return request.post(`/app/resource/download`, p)
  },
  /**
   * 历史记录删除
   */
  async delete(p: {
    resourceIds: ResourceDO['id'][]
  }): Promise<{ url: string }> {
    const ids = p.resourceIds.join(',')
    return request.delete(`/app/resource/${ids}`)
  },

  async getById_scene(resourceId: ResourceDO['id']): Promise<BgResourceItem> {
    return request.get(`app/ai-bg-resource/${resourceId}`)
  },

  /**
   * 新增物料获取模板图片
   */
  async getTemplateImg(): Promise<ReferencePO> {
    return request.post(`/app/resource/reference`)
  },

  /**
   * 查询单张图片是否加载完毕
   * @returns
   */
  async getImgStatus(id: string): Promise<ImgStatus> {
    return request.get(`/app/resource/${id}`)
  },

  /**
   * 查询精准模式图片详情页
   */
  async getImgDetail(id: string): Promise<ImgDetail> {
    return request.get(`/app/resource/related-detail/${id}`)
  },

  /**
   * 查询新自由模式图片详情页
   */
  async getFreeImgDetail(id: string): Promise<FreeModeImgDetail> {
    return request.get(`/app/resource/related-detail/new-free-mode/${id}`)
  },

  /**
   * 轮询一批图片是否加载完毕
   */
  async batchImgStatus(resourceIds: string[]): Promise<PagerList<BatchImgStatus>> {
    return request.get('app/resource/history/basic-detail', {
      params: {
        resourceIds: resourceIds.join(','),
      },
    })
  },
}

export type BatchImgStatus = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  previewUrl: string
  watermarkUrl: string
  url: string
  status: number
  source: string
  width: number
  height: number
  batchId: number
  materialId: number
}

export type SourceType = 'A-T_INFERENCE' | 'NEW_FREE_MODE'

export type ImgDetail = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  previewUrl: string
  watermarkUrl: string
  url: string
  status: number
  source: SourceType
  width: number
  height: number
  batchId: number
  materialDetail: MaterialDetail
  missionDetail: MissionDetail
  materialId: number
  type: 'public' | 'personal'
}

export type MissionDetail = {
  bgResourcePath: string
  bgResourceUrl: string
  maskPath: string
  maskUrl: string
  angle: number
  depressionAngle: number
  focalLength: number
  scale: number
  offsetX: number
  offsetY: number
  lightDimmer: number
  handleMask: boolean
}

export type MaterialDetail = {
  materialId: string
  materialName: string
  historyId: number
  email: string
  brand: string
  designer: string
  coverImageRemoveBackgroundUrl: string
  price: number
}

export type ImgStatus = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  previewUrl: string
  watermarkUrl: string
  url: string
  status: TaskStatus
  source: string
  width: number
  height: number
  batchId: number
  materialId: number
  materialName: string
  historyId: number
  email: string
  brand: string
  designer: string
  coverImageRemoveBackgroundUrl: string
  price: number
}

export type BgResourceItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  url: string
  compressPath: string
  compressUrl: string
  batchId: number
  generationType: number
  status: number
  type: string
}

export type HistoryItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  url: string
  previewUrl: string
  status: number
  source: string
  width: number
  height: number
  batchId: number
  materialId: number
  materialName: string
  collectionState: CollectionState
}

export type FreeModeImgDetail = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  previewUrl: string
  watermarkUrl: string
  url: string
  status: number
  source: string
  width: number
  height: number
  batchId: number
  itemUrl: string
  maskUrl: string
  bgResourceUrl: string
}

export enum ResourceType {
  Precise = 4,
  BgReplace = 7,
  Free = 12,
}

/**
 * 旧版
 */
// export enum ResourceType {
//   Precise = 4,
//   BgReplace = 7,
//   Free = 12,
// }

import { TaskStatus, type PageQuery, type PagerList } from '@/dts'
import { request } from '@/utils'


export class MyLifeAPI {

  /**
   * @deprecated 换成 relatedDetail
   */
  static list(params: PageQuery): Promise<PagerList<ListItem>> {
    return request.get('/my-life', { params })
  }

  static detail(id: string): Promise<ListItem> {
    return request.get(`/my-life/${id}`)
  }

  static erase(data: EraseParam): Promise<Ids> {
    return request.post('/my-life/erase', data)
  }

  /** 
   * 家居替换
   */
  static furnitureReplacement(data: ReplaceParam): Promise<Ids> {
    return request.post('/my-life/furniture-replacement', data)
  }

  /**
   * 关联细节查询列表
   */
  static relatedDetail(params: PageQuery): Promise<PagerList<RelatedItem>> {
    return request.get('/my-life/related-detail', { params })
  }

  /**
   * 关联细节查询详情
   */
  static relatedDetailById(id: string): Promise<RelatedDetail> {
    return request.get(`/my-life/related-detail/${id}`)
  }
}


export function pollingById(id: string, duration = 6500) {
  let timer: number
  const clear = () => timer && clearInterval(timer)

  return {
    promise: new Promise<RelatedDetail>((resolve) => {
      clear()

      const run = async () => {
        const data = await MyLifeAPI.relatedDetailById(id)
        if (data.status === TaskStatus.SUCCESS) {
          clear()
          resolve(data)
        }
      }

      run()
      timer = window.setInterval(run, duration)
    }),
    clear
  }
}

export type ListItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  type: number
  status: TaskStatus
  materialId: number
  taskId: string
}

export type EraseParam = {
  bgResource: BgResource
}

export type BgResource = {
  bgUrl: string
  maskUrl: string
}

export type Ids = {
  resourceId: string
}

export type ReplaceParam = {
  materialInfoId?: string
  prompt?: string
  bgResource?: BgResource
}

export type RelatedItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  url: string
  type: number
  status: number
  taskId: string
}

export type RelatedDetail = {
  name: string
  path: string
  url: string
  type: number
  status: TaskStatus
  taskId: string
}
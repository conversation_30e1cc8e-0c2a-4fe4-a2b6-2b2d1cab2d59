import clsx from 'clsx'
import React, { memo, useEffect, useRef, useState } from 'react'
import { LoadingIcon } from '../Loading/LoadingIcon'

function InnerDyVirtualScroll<T>({
  data,
  children,
  beforeChildren,
  itemHeight = 40,
  overscan = 5,

  className,
  contentClassName,
  style,

  hasMore = false,
  immediate = true,
  showLoading = false,
  loadMore,
  ...rest
}: DyVirtualScrollProps<T>) {
  const [renderData, setRenderData] = useState<T[]>([])
  const [startIndex, setStartIndex] = useState(0)
  const [translateY, setTranslateY] = useState(0)
  const [totalHeight, setTotalHeight] = useState(0)
  const [loading, setLoading] = useState(false)

  const scrollRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const itemsRef = useRef<Map<number, HTMLDivElement>>(new Map())
  const itemHeightCacheRef = useRef<Record<number, number>>({})

  const updateTotalHeight = () => {
    let height = 0
    for (let i = 0; i < data.length; i++) {
      height += getItemHeight(i)
    }
    setTotalHeight(height)
  }

  const getItemHeight = (index: number): number => {
    return itemHeightCacheRef.current[index] || itemHeight
  }

  const updateVisibleData = (scrollTop: number) => {
    let currentOffset = 0
    let visibleStartIndex = 0

    for (let i = 0; i < data.length; i++) {
      const height = getItemHeight(i)
      if (currentOffset + height > scrollTop) {
        visibleStartIndex = i
        break
      }
      currentOffset += height
    }

    const start = Math.max(0, visibleStartIndex - overscan)
    const visibleCount = Math.ceil(
      (scrollRef.current?.clientHeight || 0) / itemHeight,
    )
    const end = Math.min(
      data.length,
      visibleStartIndex + visibleCount + overscan,
    )

    setStartIndex(start)
    setRenderData(data.slice(start, end))
    setTranslateY(calculateOffsetForIndex(start))
  }

  const calculateOffsetForIndex = (index: number): number => {
    let offset = 0
    for (let i = 0; i < index; i++) {
      offset += getItemHeight(i)
    }
    return offset
  }

  const updateItemHeightCache = () => {
    itemsRef.current.forEach((element, index) => {
      const actualIndex = startIndex + index
      if (element && !itemHeightCacheRef.current[actualIndex]) {
        itemHeightCacheRef.current[actualIndex] = element.offsetHeight
      }
    })
    updateTotalHeight()
  }

  const handleScroll = () => {
    if (!scrollRef.current)
      return

    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current
    updateVisibleData(scrollTop)
    loadAndCheckHeight()

    function loadAndCheckHeight() {
      if (
        hasMore
        && !loading
        && scrollTop + clientHeight >= scrollHeight - 50
      ) {
        setLoading(true)
        loadMore?.().finally(() => {
          setLoading(false)

          const { clientHeight } = contentRef.current!
          if (clientHeight < scrollRef.current!.clientHeight) {
            loadAndCheckHeight()
          }
        })
      }
    }
  }

  useEffect(() => {
    updateTotalHeight()
    updateVisibleData(scrollRef.current?.scrollTop || 0)
  }, [data])

  useEffect(() => {
    updateItemHeightCache()
  }, [renderData])

  useEffect(
    () => {
      immediate && handleScroll()
    },
    [],
  )

  return (
    <div
      ref={ scrollRef }
      className={ clsx('overflow-auto relative', className) }
      style={ style }
      onScroll={ handleScroll }
      { ...rest }
    >
      <div style={ { height: `${totalHeight}px`, position: 'relative' } }>
        <div
          style={ {
            transform: `translateY(${translateY}px)`,
            position: 'absolute',
            width: '100%',
          } }
          ref={ contentRef }
          className={ contentClassName }
        >
          { beforeChildren }

          { renderData.map((item, index) => (
            <div
              key={ (item as any).id || startIndex + index }
              ref={ (el) => {
                if (el) {
                  itemsRef.current.set(index, el)
                }
                else {
                  itemsRef.current.delete(index)
                }
              } }
              style={ { minHeight: `${itemHeight}px` } }
              className="relative"
            >
              { children(item, startIndex + index) }
            </div>
          )) }

          <div className="absolute bottom-1 left-0 w-full flex items-center justify-center">
            { loading && showLoading && <LoadingIcon size={ 30 } /> }
          </div>
        </div>
      </div>
    </div>
  )
}

export const DyVirtualScroll = memo(InnerDyVirtualScroll) as typeof InnerDyVirtualScroll

export type DyVirtualScrollProps<T> = {
  data: T[]
  children: (item: T, index: number) => React.ReactNode

  beforeChildren?: React.ReactNode

  itemHeight?: number
  overscan?: number

  className?: string
  contentClassName?: string

  style?: React.CSSProperties
  hasMore?: boolean

  showLoading?: boolean
  loadMore?: () => Promise<any>

  immediate?: boolean
}
& Omit<
  React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>,
  'children'
>

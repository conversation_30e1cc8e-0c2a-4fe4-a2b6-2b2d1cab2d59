.catalog-tree-collapse {
  :global {
    .ant-collapse-header {
      align-items: center !important;
      padding: var(--ant-padding-xs) 0 !important;
    }

    .ant-collapse-content-box {
      padding: 0 !important;
    }
  }

  .expand-icon {
    margin-left: var(--ant-size-xxs);
  }
}

.tree {
  .more-btn {
    position: absolute;
    top: 50%;
    right: 0;
    display: none !important;
    transform: translateY(-50%);
  }

  .text {
    position: absolute;
    top: 50%;
    right: 0;
    padding-right: 6px;
    color: rgb(200 200 200);
    transform: translateY(-50%);
  }

  .title-renderer {
    &.drop-active {
      box-shadow: 0 0 0 2px var(--ant-color-primary);
    }

    &.action {
      &:hover {
        .text {
          display: none;
        }

        .more-btn {
          display: block !important;
        }
      }
    }
  }

  .editing-input {
    width: calc(100% + var(--ant-padding-xs));
    margin: 0 calc(0px - var(--ant-padding-xs) / 2);
    border-radius: var(--ant-border-radius) !important;
  }
}

import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rList, TaskStatus } from '@/dts'
import { request } from "@/utils"


export class HdAPI {

  /**
   * 高清放大
   */
  static hd(resourceId: string, processType: Scale = 2) {
    return request.post('/app/resource/special-process/high-definition-enlargement', {
      resourceId: resourceId,
      processType
    })
  }

  /**
   * 高清放大历史记录分页查询
   */
  static list(params: ListParams): Promise<PagerList<ListItem>> {
    return request.get('/app/resource/special-process/page', {
      params: {
        ...params,
        processType: params.processType.join(','),

        ...(params.status && {
          status: params.status.join(',')
        }),
        ...(params.resourceIds && {
          resourceIds: params.resourceIds.join(',')
        })
      }
    })
  }

}


export type ListItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  resourceId: string
  /** 无水印下载链接 */
  url: string
  /** 预览链接 */
  previewUrl: string
  processType: Scale
  status: TaskStatus
  width: number
  height: number
}

type ListParams = PageQuery & {
  processType: Scale[]
  status?: TaskStatus[]
  resourceIds?: string[]
}

export type Scale = 1 | 2
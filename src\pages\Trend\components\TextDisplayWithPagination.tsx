import { cn } from '@/utils'
import { memo, useCallback, useMemo, useState } from 'react'

export interface TextDisplayWithPaginationProps {
  /** 要显示的文本内容 */
  content: string
  /** 组件样式类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 每页显示的字符数 */
  charsPerPage?: number
  /** 是否显示复制按钮 */
  showCopyButton?: boolean
  /** 复制成功回调 */
  onCopySuccess?: () => void
  /** 复制失败回调 */
  onCopyError?: (error: Error) => void
}

export const TextDisplayWithPagination = memo<TextDisplayWithPaginationProps>(({
  content = '',
  className,
  style,
  charsPerPage = 500,
  showCopyButton = true,
  onCopySuccess,
  onCopyError,
}) => {
  const [currentPage, setCurrentPage] = useState(1)

  /** 计算分页数据 */
  const paginationData = useMemo(() => {
    if (!content.trim()) {
      return {
        totalPages: 1,
        pages: [''],
        currentPageContent: '',
      }
    }

    const totalPages = Math.ceil(content.length / charsPerPage)
    const pages: string[] = []

    for (let i = 0; i < totalPages; i++) {
      const startIndex = i * charsPerPage
      const endIndex = startIndex + charsPerPage
      pages.push(content.substring(startIndex, endIndex))
    }

    const currentPageContent = pages[currentPage - 1] || ''

    return {
      totalPages,
      pages,
      currentPageContent,
    }
  }, [content, charsPerPage, currentPage])

  /** 处理复制功能 */
  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(paginationData.currentPageContent)
      console.warn('[TextDisplayWithPagination] 📋 文本复制成功')
      onCopySuccess?.()
    }
    catch (error) {
      console.error('[TextDisplayWithPagination] 复制失败:', error)
      onCopyError?.(error as Error)
    }
  }, [paginationData.currentPageContent, onCopySuccess, onCopyError])

  /** 上一页 */
  const handlePrevPage = useCallback(() => {
    setCurrentPage(prev => Math.max(1, prev - 1))
  }, [])

  /** 下一页 */
  const handleNextPage = useCallback(() => {
    setCurrentPage(prev => Math.min(paginationData.totalPages, prev + 1))
  }, [paginationData.totalPages])

  return (
    <div
      className={ cn('flex flex-col ml-auto', className) }
      style={ {
        marginBottom: 'clamp(12px, 2vh, 16px)',
        width: 'fit-content', // 宽度自适应内容
        ...style,
      } }
    >
      {/* 文本内容区域 - 带边框背景和圆角 */}
      <div
        className="relative mb-4 border border-gray-200 rounded-full bg-gray-50 p-2"
        style={ {
          padding: '16px',
          overflow: 'hidden', // 隐藏翻页时的溢出内容
          width: 'fit-content', // 宽度自适应内容
        } }
      >
        {/* 计算当前页面内容的高度，确保容器高度适应 */}
        <div
          className="transition-transform duration-500 ease-in-out"
          style={ {
            transform: `translateX(-${(currentPage - 1) * 100}%)`,
            display: 'flex',
          } }
        >
          {paginationData.pages.map((pageContent, index) => (
            <div
              key={ `page-${index}` }
              className="flex-shrink-0"
              style={ {
                width: 'max-content', // 宽度根据内容自适应
              } }
            >
              <p
                className="whitespace-pre-wrap break-words text-gray-600 leading-relaxed"
                style={ {
                  fontSize: 'clamp(12px, 1.2vw, 14px)',
                  margin: 0, // 移除默认margin
                  lineHeight: '1.6', // 设置行高提升可读性
                } }
              >
                {pageContent}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* 操作按钮区域 */}
      <div className="flex items-center justify-end gap-2">
        {/* 复制按钮 */}
        {showCopyButton && (
          <button
            className="rounded-lg transition-colors hover:bg-gray-100"
            style={ { padding: 'clamp(6px, 1vw, 8px)' } }
            onClick={ handleCopy }
            title="复制当前页内容"
          >
            <svg
              style={ {
                width: 'clamp(16px, 1.5vw, 18px)',
                height: 'clamp(16px, 1.5vw, 18px)',
              } }
              viewBox="0 0 24 24"
              fill="none"
            >
              <rect
                x="9"
                y="9"
                width="13"
                height="13"
                rx="2"
                stroke="#666"
                strokeWidth="1.5"
              />
              <path
                d="M5 15H4C3 15 2 14 2 13V4C2 3 3 2 4 2H13C14 2 15 3 15 4V5"
                stroke="#666"
                strokeWidth="1.5"
              />
            </svg>
          </button>
        )}

        {/* 分页控制 */}
        <div className="flex items-center gap-1">
          {/* 上一页按钮 */}
          <button
            className="rounded transition-colors disabled:cursor-not-allowed hover:bg-gray-100 disabled:opacity-50"
            style={ { padding: 'clamp(4px, 0.5vw, 6px)' } }
            onClick={ handlePrevPage }
            disabled={ currentPage === 1 }
            title="上一页"
          >
            <svg
              style={ {
                width: 'clamp(14px, 1.3vw, 16px)',
                height: 'clamp(14px, 1.3vw, 16px)',
              } }
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M15 18L9 12L15 6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          {/* 页码显示 */}
          <span
            className="text-gray-500"
            style={ {
              fontSize: 'clamp(12px, 1.2vw, 14px)',
              padding: '0 clamp(4px, 1vw, 8px)',
            } }
          >
            {currentPage}
            {' '}
            /
            {paginationData.totalPages}
          </span>

          {/* 下一页按钮 */}
          <button
            className="rounded transition-colors disabled:cursor-not-allowed hover:bg-gray-100 disabled:opacity-50"
            style={ { padding: 'clamp(4px, 0.5vw, 6px)' } }
            onClick={ handleNextPage }
            disabled={ currentPage === paginationData.totalPages }
            title="下一页"
          >
            <svg
              style={ {
                width: 'clamp(14px, 1.3vw, 16px)',
                height: 'clamp(14px, 1.3vw, 16px)',
              } }
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M9 18L15 12L9 6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  )
})

TextDisplayWithPagination.displayName = 'TextDisplayWithPagination'

/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  getAlphaMask,
  getImageDataMatrix,
  restoreScaledImage,
  scaleDownImage,
} from "@/pages/Photog/BgReplacer/MaskEditor/canvasUtils";
import { ImageMaskSegment } from "@/pages/Photog/BgReplacer/types";
import { request } from "@/utils";

export const API_PHOTOG = {
  bgGenerate(url: string, referImgs: string[], similarity = 0.1) {
    return request.post("/pic-extend/bg-generate", {
      originalImage: url,
      referenceImages: referImgs,
      similarity
    });
  },

  async getImageMaskSegs(url: string) {
    const { blob, scale } = await scaleDownImage(url, {
      maxWidth: 500,
      maxHeight: 500,
    });
    const file = new File([blob], "image.png", { type: "image/png" });
    const formData = new FormData();
    formData.append("files[0]", file);

    const response = (await request.post(
      "/image/mask/image-segmentation-mask",
      formData
    )) as Record<string, any>;

    const segsData = response.imageSegmentationMasks[0];
    const maskSegs: ImageMaskSegment[] = [];

    const { masksBase64 } = segsData;
    const tasks: Promise<void>[] = [];
    masksBase64.forEach((mask: string) => {
      const task = new Promise<void>((resolve, reject) => {
        const b64 = "data:image/png;base64," + mask;
        restoreScaledImage(b64, scale)
          .then(getAlphaMask)
          .then((maskImg) => {
            getImageDataMatrix(maskImg).then((matrix) => {
              maskSegs.push({
                matrix,
                mask: maskImg,
              });
              resolve();
            });
          })
          .catch(reject);
      });
      tasks.push(task);
    });

    await Promise.all(tasks);

    return maskSegs;
  },

  async test(segsData: any) {
    const scale = 0.46296296296296297
    const maskSegs: ImageMaskSegment[] = [];

    const { masksBase64 } = segsData;
    const tasks: Promise<void>[] = [];
    masksBase64.forEach((mask: string) => {
      const task = new Promise<void>((resolve, reject) => {
        const b64 = "data:image/png;base64," + mask;
        restoreScaledImage(b64, scale)
          .then(getAlphaMask)
          .then((maskImg) => {
            getImageDataMatrix(maskImg).then((matrix) => {
              maskSegs.push({
                matrix,
                mask: maskImg,
              });
              resolve();
            });
          })
          .catch(reject);
      });
      tasks.push(task);
    });

    await Promise.all(tasks);

    return maskSegs;
  }
};

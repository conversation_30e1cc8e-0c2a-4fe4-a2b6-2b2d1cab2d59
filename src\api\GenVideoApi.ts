import type { PageQuery, PagerList } from '@/dts'
import type { SortType } from './squareAPI'
import { IS_PROD } from '@/config'
import { request } from '@/utils'
import { timer } from '@jl-org/tool'

export class GenVideoApi {
  /**
   * AI视频生成（二合一)
   */
  static async genVideo(
    params: GenVideoParams,
  ): Promise<{ id: string }> {
    const url = IS_PROD
      ? '/app/video-resource/video-generation'
      : '/app/video-resource/new/video-generation'
    return request.post('/app/video-resource/new/video-generation', params)
  }

  static async pollingVideo(params: GenVideoParams) {
    let stopFn = () => { }
    const { id } = await GenVideoApi.genVideo(params)
    const { promise, resolve, reject } = Promise.withResolvers<string>()

    const polling = async () => {
      await GenVideoApi.detail(id)
        .then((res) => {
          if (res.status === GenVideoTaskStatus.FINISHED) {
            resolve(res.videoUrl)
            stopFn()
          }
          else if (res.status === GenVideoTaskStatus.FAILED) {
            reject()
            stopFn()
          }
        })
        .catch(() => {
          reject()
          stopFn()
        })
    }

    stopFn = timer(polling, 3000)
    return promise
  }

  /**
   * 分页查询列表
   */
  static async list(
    params: PageQuery,
  ): Promise<PagerList<GenVideoResp>> {
    return request.get('/app/video-resource/history', { params })
  }

  /**
   * Video-详情查询
   */
  static async detail(
    id: string,
  ): Promise<VideoDetail> {
    return request.get(`/app/video-resource/single-details/${id}`)
  }

  /**
   * Video-我的收藏(分页查询)
   */
  static async myCollection(
    params: PageQuery<{ status?: GenVideoTaskStatus }>,
  ): Promise<PagerList<VdieoListItem>> {
    return request.get('/app/video-resource/collection/page', { params })
  }

  /**
   * Video-我的历史记录
   */
  static async myHistory(
    params: PageQuery<{ status?: GenVideoTaskStatus }>,
  ): Promise<PagerList<VideoHistoryItem>> {
    return request.get('/app/video-resource/my-history', { params })
  }

  /**
   * （收藏）批量添加
   */
  static async like(
    resourceIds: string[],
    type: AssetEnum = AssetEnum.Video,
  ): Promise<void> {
    return request.post('/app/video-resource/collection/batch-add', {
      resourceIds,
      type,
    })
  }

  /**
   * （收藏）批量删除
   */
  static async unLike(
    resourceIds: string[],
  ): Promise<void> {
    return request.post('/app/video-resource/collection/batch-delete', {
      resourceIds,
    })
  }

  /**
   * (广场视频)-根据指定状态分页查询列表
   */
  static async listBy(
    params: PageQuery<{ sortType: SortType }>,
  ): Promise<PagerList<VideoStatusItem>> {
    return request.get('/app/square/page/video-sort-by-specific-state', { params })
  }
}

export type GenVideoParams = {
  /**
   * 视频首帧-带图必传，图片格式支持.jpg / .jpeg / .png
   */
  imageUrl?: string
  /**
   * 视频尾帧，图片格式支持.jpg / .jpeg / .png
   */
  imageTailUrl?: string
  /**
   * 提示词
   */
  prompt?: string
  /**
   * 时长
   */
  duration: '5' | '10'
  /**
   * 画面纵横比（宽:高))
   */
  aspectRatio?: Rate
  resourceVideoGenerationTypeEnum: GenVideoType
}

export type GenVideoResp = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  prompt: string
  taskId: string
  pathTail: string
  videoUrl: string
  type: number
  status: GenVideoTaskStatus
}

export type VideoDetail = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  imgUrl: string
  prompt: string
  videoPath: string
  videoUrl: string
  email: string
  scale: string
  type: number
  status: GenVideoTaskStatus
  collectionState: CollectionEnum
}

export type VdieoListItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  resourceId: string
  videoPath: string
  videoUrl: string
  type: number
  status: GenVideoTaskStatus
}

export type VideoHistoryItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  videoUrl: string
  videoPath: string
  status: number
  collectionState: CollectionEnum
}

export type VideoStatusItem = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  resourceId: string
  name: string
  videoPath: string
  url: string
  collectionState: number
  stickyOnTop: number
}

export type Duration = 5 | 10
export type Rate = '16:9' | '9:16' | '1:1'
export type GenVideoType = 'TEXT_TO_VIDEO' | 'IMAGE_TO_VIDEO'

export enum CollectionEnum {
  Normal = 1,
  Like = 2,
}

export enum AssetEnum {
  Image = 1,
  Video = 2,
}

/**
 * 1-已提交，2-生成中，3-生成完成，4-生成失败
 */
export enum GenVideoTaskStatus {
  SUBMITTED = 1,
  GENERATING = 2,
  FINISHED = 3,
  FAILED = 4,
}

export enum VideoType {
  TEXT_TO_VIDEO = 1,
  IMAGE_TO_VIDEO = 2,
}

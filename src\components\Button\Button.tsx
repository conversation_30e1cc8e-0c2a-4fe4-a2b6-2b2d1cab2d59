import type { ButtonProps } from './types'
import { cn } from '@/utils'
import React, { Children, forwardRef, memo, useState } from 'react'
import { LoadingIcon } from '../Loading/LoadingIcon'
import { Slot } from '../Slot'
import { getFlatStyles, getGhostStyles, getIconButtonStyles, getNeumorphicStyles, getOutlinedStyles } from './styles'

const InnerButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const {
    children,
    leftIcon,
    rightIcon,
    iconOnly = false,
    loading = false,
    loadingText,
    disabled = false,
    designStyle = 'flat',
    variant = 'default',
    size = 'md',
    rounded = 'md',
    block = false,
    as: Component = 'button',
    className,
    iconClassName,
    hoverClassName,
    activeClassName,
    disabledClassName,
    loadingClassName,
    asChild,
    onClick,
    ...rest
  } = props

  const [isActive, setIsActive] = useState(false)
  const [isHover, setIsHover] = useState(false)
  const noChild = Children.toArray(children).length <= 0 || iconOnly

  const getStylesByDesign = () => {
    switch (designStyle) {
      case 'neumorphic':
        return getNeumorphicStyles(props)
      case 'outlined':
        return getOutlinedStyles(props)
      case 'ghost':
        return getGhostStyles(props)
      case 'flat':
      default:
        return getFlatStyles(props)
    }
  }

  const iconButtonSize = noChild
    ? getIconButtonStyles(size!)
    : ''

  const buttonStyles = cn(
    getStylesByDesign(),
    block && 'w-full',
    noChild && [iconButtonSize, 'p-0'],
    disabled && disabledClassName,
    loading && loadingClassName,
    isActive && activeClassName,
    isHover && hoverClassName,
    className,
  )

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (loading || disabled) {
      e.preventDefault()
      return
    }

    onClick?.(e)
  }

  const handleMouseDown = () => {
    if (!disabled && !loading) {
      setIsActive(true)
    }
  }

  const handleMouseUp = () => {
    setIsActive(false)
  }

  const handleMouseEnter = () => {
    if (!disabled && !loading) {
      setIsHover(true)
    }
  }

  const handleMouseLeave = () => {
    setIsHover(false)
    setIsActive(false)
  }

  const getButtonContent = () => {
    const color = variant === 'primary'
      ? '#fff'
      : undefined

    if (loading) {
      return (
        <div className="flex items-center justify-center gap-2">
          <LoadingIcon
            size={ size === 'lg'
              ? 'md'
              : 'sm' }
            color={ color }
          />
          { !iconOnly && loadingText
            ? loadingText
            : children }
        </div>
      )
    }

    if (noChild && (leftIcon || rightIcon)) {
      return leftIcon || rightIcon
    }

    return (
      <>
        { leftIcon && (
          <span className={ cn('mr-2', (noChild) && 'mr-0', iconClassName) }>
            { leftIcon }
          </span>
        ) }
        { children }
        { rightIcon && (
          <span className={ cn('ml-2', (noChild) && 'ml-0', iconClassName) }>
            { rightIcon }
          </span>
        ) }
      </>
    )
  }

  const finalProps = {
    ref,
    className: buttonStyles,
    disabled: disabled || loading,
    onClick: handleClick,
    onMouseDown: handleMouseDown,
    onMouseUp: handleMouseUp,
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    ...rest,
  }

  if (asChild) {
    return (
      <Slot
        { ...finalProps }
      >
        { children }
      </Slot>
    )
  }

  return (
    <Component
      { ...finalProps }
    >
      { getButtonContent() }
    </Component>
  )
})

InnerButton.displayName = 'Button'

export const Button = memo(InnerButton)

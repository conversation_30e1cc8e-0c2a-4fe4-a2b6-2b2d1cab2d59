import type { PageQuery, PagerList } from '@/dts'
import { request } from '@/utils'

export class BlogArticleApi {
  /**
   * 查询文章详情（前台）
   */
  static async publicDetail(
    id: string,
  ): Promise<BlogArticleDetailResp> {
    return request.get(`/public/blog/article/${id}`)
  }

  /**
   * 分页查询已发布文章（前台）
   */
  static async publicList(
    params: BlogArticlePageQuery,
  ): Promise<PagerList<BlogArticleResp>> {
    return request.get('/public/blog/article/page', { params })
  }

  /**
   * 发布文章
   */
  static async create(
    data: BlogArticleReq,
  ): Promise<number> {
    return request.post('/blog/article', data)
  }

  /**
   * 查询文章详情（后台）
   */
  static async detail(
    id: number,
  ): Promise<BlogArticleDetailResp> {
    return request.get(`/blog/article/${id}`)
  }

  /**
   * 上传博客图片
   */
  static async uploadImage(
    formData: FormData,
  ): Promise<BlogImageUploadResp> {
    return request.post('/blog/article/image/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  /**
   * 分页查询文章列表（后台）
   */
  static async list(
    params: BlogArticlePageQuery,
  ): Promise<PagerList<BlogArticleResp>> {
    return request.get('/blog/article/page', { params })
  }

  // 获取博客详情

  static async getBlogDetail(
    id: number,
  ): Promise<BlogArticleDetailResp> {
    return request.get(`/public/blog/article/${id}`)
  }
}

export type BlogArticleReq = {
  /** 标题 */
  title: string
  /** 封面图片 */
  coverImage?: string
  /** 正文内容 */
  content: string
  /** 状态 */
  status?: string
  /** 发布时间 */
  publishTime?: string
}

export type BlogArticleDetailResp = {
  /** ID */
  id: number
  /** 创建人 */
  createUserString: string
  /** 创建时间 */
  createTime: string
  /** 是否禁用修改 */
  disabled: boolean
  /** 修改人 */
  updateUserString: string
  /** 修改时间 */
  updateTime: string
  /** 标题 */
  title: string
  /** 封面图片OSS key */
  coverImage: string
  /** 封面图片URL */
  coverImageUrl: string
  // 视频链接
  coverImageOrVideoUrl:string
  /** 正文内容 */
  content: string
  /** 状态 */
  status: string
  /** 发布时间 */
  publishTime: string
}

export type BlogArticleResp = {
  /** ID */
  id: number
  /** 创建人 */
  createUserString: string
  /** 创建时间 */
  createTime: string
  /** 是否禁用修改 */
  disabled: boolean
  /** 修改人 */
  updateUserString: string
  /** 修改时间 */
  updateTime: string
  /** 标题 */
  title: string
  /** 封面图片URL */
  coverImageUrl: string
  /** 状态 */
  status: string
  /** 发布时间 */
  publishTime: string
  /** 内容摘要 */
  summary: string
  label: string;

}

export type BlogImageUploadResp = {
  /** 图片OSS key */
  imageKey: string
  /** 图片访问URL */
  imageUrl: string
  /** 原始文件名 */
  originalName: string
}

export type BlogArticlePageQuery = PageQuery<{
  /** 标题 */
  title?: string
  /** 状态 */
  status?: string
  /** 类型 
   * 1. Blogs
   * 
   * */ 
  type: 1 | 2 | 3 | 4
}>


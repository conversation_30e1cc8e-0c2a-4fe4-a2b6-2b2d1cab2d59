/** 引入项目中的图片 */
import img1 from '@/assets/image/home/<USER>/1.webp'
import img2 from '@/assets/image/home/<USER>/2.webp'
import img3 from '@/assets/image/home/<USER>/3.webp'

import img4 from '@/assets/image/home/<USER>/4.webp'
import img5 from '@/assets/image/home/<USER>/5.webp'
import cn from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import { Send } from 'lucide-react'
import React, { useCallback, useState } from 'react'

interface ImagePreviewCardProps {
  isOpen: boolean
  onClose: () => void
}

export const ImagePreviewCard: React.FC<ImagePreviewCardProps> = ({
  isOpen,
  onClose,
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isEditing, setIsEditing] = useState(false)
  const [editText, setEditText] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [tags, setTags] = useState(['Tag name', 'Tag name 2', 'Tag name 3'])
  const [newTag, setNewTag] = useState('')
  const [title, setTitle] = useState('Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod')
  const [bodyText, setBodyText] = useState('Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.')

  /** 使用项目中的真实图片 */
  const images = [img1, img2, img3, img4, img5]

  const handleDownload = useCallback(() => {
    const link = document.createElement('a')
    link.href = images[selectedIndex]
    link.download = `image-${selectedIndex + 1}.jpg`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, [images, selectedIndex])

  const handleDelete = useCallback(() => {
    console.log('Deleting image:', selectedIndex)
    setShowDeleteConfirm(false)
  }, [selectedIndex])

  const handleSendEdit = useCallback(() => {
    console.log('Sending edit text:', editText)
    setEditText('')
    setIsEditing(false)
  }, [editText])

  const handleAddTag = useCallback(() => {
    if (newTag.trim()) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }, [newTag, tags])

  const handleRemoveTag = useCallback((index: number) => {
    setTags(tags.filter((_, i) => i !== index))
  }, [tags])

  const handlePrevImage = useCallback(() => {
    setSelectedIndex(prev => (prev > 0
      ? prev - 1
      : images.length - 1))
    /** 切换图片时退出编辑模式 */
    setIsEditing(false)
    setEditText('')
  }, [images.length])

  const handleNextImage = useCallback(() => {
    setSelectedIndex(prev => (prev < images.length - 1
      ? prev + 1
      : 0))
    /** 切换图片时退出编辑模式 */
    setIsEditing(false)
    setEditText('')
  }, [images.length])

  if (!isOpen)
    return null

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={ { x: '100%' } }
          animate={ { x: 0 } }
          exit={ { x: '100%' } }
          transition={ { type: 'spring', damping: 25, stiffness: 300 } }
          className="h-full flex flex-col overflow-hidden rounded-lg bg-white shadow-xl"
          style={ { width: 'calc(50% - 8px)' } }
        >
          {/* Header */}
          <div className="flex items-center justify-between border-b bg-white px-5 py-4">
            <div className="flex items-center gap-2">
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={ 2 }
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h2 className="text-base font-semibold">Post Content</h2>
            </div>
            <button
              onClick={ onClose }
              className="rounded-full p-1 transition-colors hover:bg-gray-100"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content - 横向布局 */}
          <div className="flex flex-1 overflow-hidden">
            {/* Left: Thumbnail List - 纵向排列 */}
            <div className="w-28 flex-shrink-0 overflow-y-auto border-r border-gray-200 p-4">
              <div className="space-y-2">
                {images.map((img, index) => (
                  <button
                    key={ index }
                    onClick={ () => {
                      setSelectedIndex(index)
                      /** 切换图片时退出编辑模式 */
                      setIsEditing(false)
                      setEditText('')
                    } }
                    className={ cn(
                      'relative w-full aspect-square rounded-lg overflow-hidden border-2 transition-all',
                      selectedIndex === index
                        ? 'border-blue-500 ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-gray-300',
                    ) }
                  >
                    <img src={ img } alt={ `Thumbnail ${index + 1}` } className="h-full w-full object-cover" />
                    {index === 0 && (
                      <div className="absolute left-1 top-1 rounded bg-white/90 px-1 text-[9px] text-gray-700 font-medium">
                        Cover
                      </div>
                    )}
                  </button>
                ))}

                {/* Upload button */}
                <button className="aspect-square w-full flex flex-col items-center justify-center border-2 border-gray-300 rounded-lg border-dashed transition-all hover:border-gray-400 hover:bg-gray-100">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M12 4v16m8-8H4" />
                  </svg>
                  <span className="mt-1 text-[9px] text-gray-500">Upload</span>
                </button>
              </div>
            </div>

            {/* Right: Main Content - White Background */}
            <div className="flex-1 overflow-y-auto bg-white">
              <div className="p-5 space-y-5">
                {/* Title Section */}
                <div>
                  <h3 className="mb-2 text-lg font-semibold">Post Content Review</h3>
                  <p className="text-sm text-gray-600">
                    I've created post content based on your brand strategy. Review and edit the title and copy to match your voice.
                  </p>
                </div>

                {/* Main Image - Exact Size: 799x687 */}
                <div className="relative overflow-hidden rounded-lg bg-white shadow-sm" style={ { height: '687px', margin: '0 auto' } }>
                  <img
                    src={ images[selectedIndex] }
                    alt="Main preview"
                    className="h-full w-full object-contain"
                  />

                  {/* Image Counter - Top Right */}
                  <div className="absolute right-3 top-3 rounded-lg bg-black/60 px-3 py-1 text-sm text-white font-medium">
                    {selectedIndex + 1}
                    /
                    {images.length}
                  </div>

                  {/* Navigation Arrows */}
                  <button
                    onClick={ handlePrevImage }
                    className="absolute left-3 top-1/2 rounded-full bg-white/80 p-1.5 shadow-md transition-all -translate-y-1/2 hover:bg-white"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <button
                    onClick={ handleNextImage }
                    className="absolute right-3 top-1/2 rounded-full bg-white/80 p-1.5 shadow-md transition-all -translate-y-1/2 hover:bg-white"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M9 5l7 7-7 7" />
                    </svg>
                  </button>

                  {/* Action Icons - Bottom Left - Vertical Layout - 36x36 with 16x16 icons */}
                  <div className="absolute bottom-3 left-3 flex flex-col gap-2">
                    {/* Edit Button */}
                    <button
                      onClick={ () => setIsEditing(true) }
                      className="rounded-full bg-white/90 shadow-md transition-all hover:bg-white"
                      style={ { width: '36px', height: '36px', display: 'flex', alignItems: 'center', justifyContent: 'center' } }
                      title="Edit"
                    >
                      <svg style={ { width: '16px', height: '16px' } } className="text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={ 2 }
                          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                        />
                      </svg>
                    </button>

                    {/* Download Button */}
                    <button
                      onClick={ handleDownload }
                      className="rounded-full bg-white/90 shadow-md transition-all hover:bg-white"
                      style={ { width: '36px', height: '36px', display: 'flex', alignItems: 'center', justifyContent: 'center' } }
                      title="Download"
                    >
                      <svg style={ { width: '16px', height: '16px' } } className="text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={ 2 }
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                        />
                      </svg>
                    </button>

                    {/* Delete Button - Red Color */}
                    <button
                      onClick={ () => setShowDeleteConfirm(true) }
                      className="rounded-full bg-white/90 shadow-md transition-all hover:bg-white"
                      style={ { width: '36px', height: '36px', display: 'flex', alignItems: 'center', justifyContent: 'center' } }
                      title="Delete"
                    >
                      <svg style={ { width: '16px', height: '16px' } } className="text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={ 2 }
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>

                  {/* Edit Input - Centered Below Image */}
                  {isEditing && (
                    <motion.div
                      initial={ { opacity: 0, y: 20 } }
                      animate={ { opacity: 1, y: 0 } }
                      className="absolute bottom-3 left-0 right-0 flex justify-center"
                    >
                      <div
                        className="flex items-center gap-3 shadow-xl"
                        style={ {
                          width: '521px',
                          height: '69px',
                          padding: '2px',
                          background: 'linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)',
                          borderRadius: '16px',
                        } }
                      >
                        <div className="h-full w-full flex items-center gap-3 bg-white px-6" style={ { borderRadius: '14px' } }>
                          <input
                            type="text"
                            value={ editText }
                            onChange={ e => setEditText(e.target.value) }
                            placeholder="Ask what's on your mind"
                            className="flex-1 bg-transparent text-center text-base outline-none placeholder-gray-400"
                            autoFocus
                            onKeyDown={ (e) => {
                              if (e.key === 'Escape') {
                                setIsEditing(false)
                                setEditText('')
                              }
                              else if (e.key === 'Enter') {
                                handleSendEdit()
                              }
                            } }
                          />
                          <button
                            onClick={ handleSendEdit }
                            className="rounded-full p-2 transition-colors hover:bg-gray-100"
                          >
                            <Send size={ 20 } className="text-gray-500" />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </div>

                {/* Post Content Form */}
                <div className="rounded-lg bg-white p-5 shadow-sm space-y-4">
                  <h4 className="text-gray-900" style={ { fontWeight: 700, fontSize: '16px' } }>Post content</h4>

                  {/* Title */}
                  <div>
                    <label className="mb-1 block text-gray-700" style={ { fontWeight: 700, fontSize: '16px' } }>Title</label>
                    <input
                      type="text"
                      className="w-full border border-gray-200 rounded-lg bg-gray-50 p-2.5 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={ title }
                      onChange={ (e) => {
                        if (e.target.value.length <= 100) {
                          setTitle(e.target.value)
                        }
                      } }
                      maxLength={ 100 }
                    />
                    <div className="mt-1 text-right text-xs text-gray-400">
                      {title.length}
                      {' '}
                      / 100
                    </div>
                  </div>

                  {/* Body copy */}
                  <div>
                    <label className="mb-1 block text-gray-700" style={ { fontWeight: 700, fontSize: '16px' } }>Body copy</label>
                    <textarea
                      className="w-full resize-none border border-gray-200 rounded-lg bg-gray-50 p-2.5 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={ 5 }
                      value={ bodyText }
                      onChange={ e => setBodyText(e.target.value) }
                    />
                    <div className="mt-1 text-right text-xs text-gray-400">
                      {bodyText.length}
                      {' '}
                      / 500
                    </div>
                  </div>

                  {/* Tags */}
                  <div>
                    <label className="mb-2 block text-gray-700" style={ { fontWeight: 700, fontSize: '16px' } }>Tags</label>
                    <div className="mb-3 flex flex-wrap gap-2">
                      {tags.map((tag, index) => (
                        <span key={ index } className="inline-flex items-center gap-1 rounded-full bg-blue-50 px-3 py-1 text-xs text-blue-600">
                          <span>{tag}</span>
                          <button
                            onClick={ () => {
                              setTags(tags.filter((_, i) => i !== index))
                            } }
                            className="hover:text-blue-800"
                          >
                            <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </span>
                      ))}
                    </div>
                    <div className="flex gap-2" style={ { width: '400px' } }>
                      <input
                        type="text"
                        placeholder="Enter tag name (without #)"
                        value={ newTag }
                        onChange={ e => setNewTag(e.target.value) }
                        onKeyDown={ (e) => {
                          if (e.key === 'Enter' && newTag.trim()) {
                            setTags([...tags, newTag.trim()])
                            setNewTag('')
                          }
                        } }
                        className="flex-1 border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <button
                        onClick={ () => {
                          if (newTag.trim()) {
                            setTags([...tags, newTag.trim()])
                            setNewTag('')
                          }
                        } }
                        className="border border-gray-300 rounded-lg px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-50"
                      >
                        Add
                      </button>
                    </div>
                  </div>
                </div>

                {/* Apply Button - Bottom Right Corner */}
                <div className="flex justify-end">
                  <button
                    className="rounded-full px-8 py-2.5 font-medium font-semibold transition-all hover:opacity-90"
                    style={ {
                      background: 'linear-gradient(white, white) padding-box, linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%) border-box',
                      border: '2px solid transparent',
                      color: '#333',
                    } }
                  >
                    Apply Post
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Delete Confirmation Modal */}
          <AnimatePresence>
            {showDeleteConfirm && (
              <motion.div
                initial={ { opacity: 0 } }
                animate={ { opacity: 1 } }
                exit={ { opacity: 0 } }
                className="absolute inset-0 z-50 flex items-center justify-center bg-black/30"
                onClick={ () => setShowDeleteConfirm(false) }
              >
                <motion.div
                  initial={ { scale: 0.9 } }
                  animate={ { scale: 1 } }
                  exit={ { scale: 0.9 } }
                  className="mx-4 max-w-sm rounded-xl bg-white p-6 shadow-xl"
                  onClick={ e => e.stopPropagation() }
                >
                  <div className="mb-4 flex items-start justify-between">
                    <h3 className="text-lg font-semibold">Delete this image?</h3>
                    <button
                      onClick={ () => setShowDeleteConfirm(false) }
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <p className="mb-6 text-gray-600">
                    Once deleted, this image cannot be recovered.
                  </p>
                  <div className="flex gap-3">
                    <button
                      onClick={ handleDelete }
                      className="flex-1 border border-gray-300 rounded-lg bg-white px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50"
                    >
                      Delete
                    </button>
                    <button
                      onClick={ () => setShowDeleteConfirm(false) }
                      className="flex-1 rounded-lg bg-gray-100 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-200"
                    >
                      Cancel
                    </button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default ImagePreviewCard

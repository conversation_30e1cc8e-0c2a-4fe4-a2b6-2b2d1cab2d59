import { request } from "@/utils"


export class API_LOGIN {

  static async getCaptcha(): Promise<{ img: string; uuid: string }> {
    const url = `/captcha/img`
    return request.get(url)
  }

  /**
   * 邮箱验证码
   */
  static async getCaptchaMail(params: { email: string }): Promise<unknown> {
    return request.get(`/captcha/mail`, { params })
  }

  /**
   * 发送短信验证码
   */
  static async sendSmsCaptcha(
    phone: string
  ): Promise<void> {
    return request.get('/captcha/phone/sms', { params: { phone } })
  }
}

const fs = require('node:fs')
const path = require('node:path')
/**
 * pnpm i sitemap -D
 */
const { SitemapStream, streamToPromise } = require('sitemap')

const links = [
  { url: '/', changefreq: 'weekly', priority: 1.0, lastmod: new Date().toISOString() },
  { url: '/event', changefreq: 'weekly', priority: 1.0, lastmod: new Date().toISOString() },
  { url: '/blog', changefreq: 'weekly', priority: 1.0, lastmod: new Date().toISOString() },
  { url: '/pricing', changefreq: 'weekly', priority: 1.0, lastmod: new Date().toISOString() },
  { url: '/about', changefreq: 'weekly', priority: 1.0, lastmod: new Date().toISOString() },
  { url: '/news', changefreq: 'weekly', priority: 1.0, lastmod: new Date().toISOString() },
]

generateSitemap()

async function generateSitemap() {
  const smStream = new SitemapStream({
    hostname: 'https://photog.art',
  })

  links.forEach(link => smStream.write(link))
  smStream.end()

  const sitemapXML = await streamToPromise(smStream)

  fs.writeFileSync(
    path.resolve(path.resolve(__dirname, '../dist'), 'sitemap.xml'),
    sitemapXML,
  )

  console.log('Sitemap 已生成')
}

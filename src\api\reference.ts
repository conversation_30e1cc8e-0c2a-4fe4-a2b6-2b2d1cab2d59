import { GenerateImageParamPO, MaterialDO, StartModelPO, TrainingPO, type PageQuery, type PagerList, type TaskType } from "@/dts"
import { request } from "@/utils"


export const API_REFERENCE = {
  /** 训练图片 */
  async trainingImg(file: File, keyWords: string): Promise<TrainingPO> {
    const url = `/ai/reference/generate-similar-image`
    const formData = new FormData()
    formData.append("image", file)
    formData.append("keyWords", keyWords)
    return request.post(url, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    })
  },

  /** 家具替换生成图片 */
  async replacement(params: {
    /** 模型id */
    materialInfoId: MaterialDO["id"]
    /** 本地上传的图片 */
    resourcePaths: string[]
    /** 模板图 */
    bgResources: {
      bgUrl: string
      maskUrl: string
    }[]
  }): Promise<unknown> {
    return request.post(`/ai/reference/replacement`, params)
  },


  /** 通过上传图片换成蒙版 */
  async uploadMask(fileList: []): Promise<unknown> {
    const url = `image/mask/image-segmentation-mask`
    const formData = new FormData()
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i]
      formData.append("files", file)
    }
    return request.post(url, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    })
  },

  /**
   * 上传模型
   * @param params 
   * @returns 
   */
  async startModelTask(params: StartModelPO): Promise<{ taskId: string }> {
    const url = 'ai/reference/object-replacement/3d/model-training'
    return request.post(url, params)
  },

  /**
   * 精准模式生图
   */
  async generateImageTask(params: GenerateImageParamPO) {
    const url = '/ai/reference/object-replacement/image-generation'
    return request.post(url, params)
  },
  /**
   * 自由模式生图
   */
  async generateFreeImageTask(params: FreeModeParams) {
    const url = '/ai/reference/object-replacement/image-generation/new-free'
    return request.post(url, params)
  },

  /**
   * 多角度图上传
   */
  async uploadMultiImg(params: MultiImgParams): Promise<{ taskId: string }> {
    const url = '/ai/reference/object-replacement/multi-angle-images/model-training'
    return request.post(url, params)
  },

  /**
   * 训练任务进度批量查询
   */
  async batchModelProcess(params: PageQuery<{ taskType?: TaskType }>): Promise<PagerList<TrainingModel>> {
    const url = '/ai/model/group-by-batch'
    return request.get(url, {
      params
    })
  },

  /**
   * 视频截图
   */
  async cutVideo(params: {
    videoPath: string
    num: number
  }): Promise<{ id: string }> {
    const url = '/ai/reference/video/screenshot'
    return request.post(url, params)
  },
  /**
   * 视频截图
   */
  async getVideoScreenshot(id: string): Promise<{
    status: VideoStatus
    imagesList: string[]
  }> {
    const url = `/ai/reference/video/screenshot/${id}`
    return request.get(url)
  },
}

export type ImgPosition =
  | 'frontImage'
  | 'leftImage'
  | 'rightImage'
  | 'backImage'

export type MultiImgParams = {
  coverImage?: string
  name: string
  frontImage?: string
  leftImage?: string
  rightImage?: string
  backImage?: string
  brand: string
  designer: string
  price: number
  directoryId?: string
  taskType: number
  typeId: number
  /** 
   * 1:private 
   * 2:public
   */
  viewArea: number
  /**
   * uuid/图片名
   */
  multiAngleImages: string[]
  type: string
}

export type TrainingModel = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  coverImageUrl: string
  status: number
  taskType: TaskType
}

/**
 * 	状态 -1：失败 1：处理中 2：分析成功
 */
export enum VideoStatus {
  FAILED = -1,
  PROCESSING = 1,
  SUCCESS = 2,
}

export type FreeModeParams = {
  itemImageUrl: string
  aiDrawingBgResources: AiDrawingBgResource[]
  uploadBgResources: AiDrawingBgResource[]
  libraryBgResources: AiDrawingBgResource[]
  reRequestBgResources: AiDrawingBgResource[]
}

export type AiDrawingBgResource = {
  bgUrl: string
  maskUrl: string
}
export function setIframe(
  iframeEl: HTMLIFrameElement,
  externalHtml: string,
  styleTxt: string,
): void {
  const parser = new DOMParser()
  const doc = parser.parseFromString(externalHtml, 'text/html')

  const styleElement = document.createElement('style')
  styleElement.textContent = styleTxt
  doc.head.appendChild(styleElement)

  const processedHtml = doc.documentElement.outerHTML

  const blob = new Blob([processedHtml], { type: 'text/html' })
  const blobUrl = URL.createObjectURL(blob)
  iframeEl.src = blobUrl

  iframeEl.onload = () => {
    URL.revokeObjectURL(blobUrl)
  }
}

import { nodeRequest, request } from "@/utils";

const prefix = "ffmpeg";
// const transferPrefix = "/transfer";

export const API_NODE = {
  async cutVideo(file: File): Promise<string[]> {
    const url = prefix;

    const formData = new FormData();
    formData.append("file", file);

    return nodeRequest.post(url, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  },

  async askSubmit(params: SubmitPO): Promise<unknown> {
    return request.post(`/app/collect-information/info`, params);
  },
};

interface SubmitPO {
  firstName: string;
  lastName: string;
  email: string;
  jobTitle: string;
  companyName: string;
  companySize: number;
  remark?: string;
}

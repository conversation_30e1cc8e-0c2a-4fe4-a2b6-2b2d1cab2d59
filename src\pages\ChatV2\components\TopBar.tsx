/**
 * TopBar - 顶部栏组件
 * 包含项目选择器、AI代理图标和下拉菜单
 * 高度可配置，支持自定义项目数据和事件处理
 */

import SvgIcon from '@/components/SvgIcon'
import cn from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import React, { Fragment, useCallback, useEffect, useRef, useState } from 'react'
import { ProjectSidebar } from './ProjectSidebar'

// ==================== 类型定义 ====================

/**
 * 项目数据配置
 */
export interface ProjectData {
  id: string
  name: string
  icon?: React.ReactNode
  description?: string
  metadata?: Record<string, any>
}

/**
 * AI代理配置
 */
export interface AIAgent {
  id: string
  name: string
  icon?: string // 图片路径
  customIcon?: React.ReactNode // 自定义图标组件
}

/**
 * 顶部栏配置Props
 */
export interface TopBarProps {
  // ========== 项目数据配置 ==========
  currentProject?: ProjectData
  projects?: ProjectData[]
  onProjectSelect?: (project: ProjectData) => void
  onNewProject?: () => void

  // ========== AI代理配置 ==========
  agents?: AIAgent[]
  showAgents?: boolean
  hideAgentsInWelcome?: boolean // 在 Welcome 状态下隐藏 AI 代理图标
  agentIconSize?: string
  onAgentClick?: (agent: AIAgent) => void

  // ========== 下拉菜单配置 ==========
  dropdownExpanded?: boolean
  onDropdownToggle?: (expanded: boolean) => void
  dropdownContent?: React.ReactNode
  dropdownWidth?: string | number
  dropdownMaxHeight?: string | number
  enableClickOutside?: boolean

  // ========== 样式配置 ==========
  projectButtonClassName?: string
  projectButtonStyle?: React.CSSProperties
  gradientColors?: {
    start: string
    end: string
  }
  iconGradientId?: string

  // ========== 布局配置 ==========
  containerClassName?: string
  containerStyle?: React.CSSProperties
  padding?: string

  // ========== 其他配置 ==========
  setMessages?: (messages: any) => void
  customIcon?: React.ReactNode
  showIcon?: boolean
  showDropdownArrow?: boolean
}

// ==================== 默认配置 ====================

const defaultGradientColors = {
  start: '#DD9DFF',
  end: '#36D3FF',
}

const defaultAgents: AIAgent[] = [
  { id: 'research-analyst', name: 'Research Analyst', icon: '/src/assets/image/home/<USER>' },
  { id: 'brand-strategist', name: 'Brand Strategist', icon: '/src/assets/image/home/<USER>' },
  { id: 'creative-director', name: 'Creative Director', icon: '/src/assets/image/home/<USER>' },
  { id: 'operations-manager', name: 'Operations Manager', icon: '/src/assets/image/home/<USER>' },
]

const DefaultProjectIcon: React.FC<{ gradientId: string }> = ({ gradientId }) => (
  <svg width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4">
    <path d="M7.33301 1.66667H10.9997C12.6565 1.66667 13.9997 3.00981 13.9997 4.66667M8.06811 2.49581L7.59791 1.83752C7.22248 1.31193 6.61634 1 5.97044 1H4.66634C2.82539 1 1.33301 2.49238 1.33301 4.33333V9.66667C1.33301 11.5076 2.82539 13 4.66634 13H11.333C13.174 13 14.6663 11.5076 14.6663 9.66667V6.66667C14.6663 4.82572 13.174 3.33333 11.333 3.33333H9.69558C9.04967 3.33333 8.44353 3.0214 8.06811 2.49581Z" stroke={ `url(#${gradientId}1)` } />
    <path d="M8 9.99984H12" stroke={ `url(#${gradientId}2)` } strokeLinecap="round" strokeLinejoin="round" />
    <defs>
      <linearGradient id={ `${gradientId}1` } x1="1.33301" y1="7" x2="14.6663" y2="7" gradientUnits="userSpaceOnUse">
        <stop stopColor={ defaultGradientColors.start } />
        <stop offset="1" stopColor={ defaultGradientColors.end } />
      </linearGradient>
      <linearGradient id={ `${gradientId}2` } x1="8" y1="9.99984" x2="12" y2="9.99984" gradientUnits="userSpaceOnUse">
        <stop stopColor={ defaultGradientColors.start } />
        <stop offset="1" stopColor={ defaultGradientColors.end } />
      </linearGradient>
    </defs>
  </svg>
)

// ==================== 主组件 ====================

/**
 * TopBar 主组件
 */
export const TopBar: React.FC<TopBarProps> = ({
  /** 项目数据 */
  currentProject = { id: '1', name: 'Project Name Goese Here' },
  projects = [],
  onProjectSelect,
  onNewProject,

  // AI代理
  agents = defaultAgents,
  showAgents = true,
  hideAgentsInWelcome = false,
  agentIconSize = '38px',
  onAgentClick,

  /** 下拉菜单 */
  dropdownExpanded: dropdownExpandedProp,
  onDropdownToggle,
  dropdownContent,
  dropdownWidth = '320px',
  dropdownMaxHeight = '400px',
  enableClickOutside = true,

  /** 样式 */
  projectButtonClassName = '',
  projectButtonStyle = {},
  gradientColors = defaultGradientColors,
  iconGradientId = 'topbar-gradient',

  /** 布局 */
  containerClassName = '',
  containerStyle = {},
  padding = '16px',

  /** 其他 */
  setMessages,
  customIcon,
  showIcon = true,
  showDropdownArrow = true,
}) => {
  // ========== 状态管理 ==========
  const [isDropdownExpanded, setIsDropdownExpanded] = useState(dropdownExpandedProp ?? false)
  const projectSidebarRef = useRef<any>(null)

  // ========== 副作用 ==========

  /** 同步外部下拉状态 */
  useEffect(() => {
    if (dropdownExpandedProp !== undefined) {
      setIsDropdownExpanded(dropdownExpandedProp)
    }
  }, [dropdownExpandedProp])

  // ========== 事件处理 ==========

  const handleDropdownToggle = useCallback(() => {
    const newExpanded = !isDropdownExpanded
    setIsDropdownExpanded(newExpanded)
    onDropdownToggle?.(newExpanded)
  }, [isDropdownExpanded, onDropdownToggle])

  const handleClickOutside = useCallback((e: MouseEvent) => {
    if (!enableClickOutside)
      return

    const target = e.target as HTMLElement
    if (isDropdownExpanded
      && !target.closest('.project-dropdown')
      && !target.closest('.project-toggle-button')) {
      setIsDropdownExpanded(false)
      onDropdownToggle?.(false)
    }
  }, [isDropdownExpanded, enableClickOutside, onDropdownToggle])

  useEffect(() => {
    if (isDropdownExpanded && enableClickOutside) {
      document.addEventListener('click', handleClickOutside)
      return () => {
        document.removeEventListener('click', handleClickOutside)
      }
    }
  }, [isDropdownExpanded, handleClickOutside, enableClickOutside])

  const handleItemClick = useCallback(() => {
    setIsDropdownExpanded(false)
    onDropdownToggle?.(false)
  }, [onDropdownToggle])

  const handleNewProject = useCallback(() => {
    onNewProject?.()
    setIsDropdownExpanded(false)
    onDropdownToggle?.(false)
  }, [onNewProject, onDropdownToggle])

  // ========== 渲染 ==========

  return (
    <div
      className={ cn('border-b border-gray-200 bg-white relative flex-shrink-0', containerClassName) }
      style={ containerStyle }
    >
      <div style={ { padding } }>
        <div className="flex flex-wrap items-center gap-4">
          {/* 项目选择器按钮 */}
          <div
            className={ cn(
              'inline-flex items-center gap-2 px-4 py-2 rounded-lg cursor-pointer transition-all relative overflow-hidden project-toggle-button',
              projectButtonClassName,
            ) }
            style={ projectButtonStyle }
            onClick={ handleDropdownToggle }
          >
            {/* 渐变背景层 - 10%透明度 */}
            <div
              className="absolute inset-0 opacity-10"
              style={ {
                background: `linear-gradient(90deg, ${gradientColors.start} 0%, ${gradientColors.end} 100%)`,
              } }
            />

            {/* 内容层 */}
            <div className="relative flex flex-1 items-center gap-2">
              {showIcon && (
                customIcon || <DefaultProjectIcon gradientId={ iconGradientId } />
              )}
              <span
                className="text-gray-700"
                style={ { fontSize: '14px', fontWeight: 700 } }
              >
                {currentProject.name}
              </span>
            </div>

            {showDropdownArrow && (
              <SvgIcon
                icon="down"
                className={ cn(
                  'w-4 h-4 transition-transform duration-200 relative text-gray-600',
                  isDropdownExpanded && 'rotate-180',
                ) }
              />
            )}
          </div>

          {/* AI代理图标 */}
          {showAgents && agents.length > 0 && !hideAgentsInWelcome && (
            <div className="flex flex-wrap items-center">
              {agents.map((agent, index) => (
                <React.Fragment key={ agent.id }>
                  {/* 分隔符 - 第一个元素前不显示 */}
                  {index > 0 && (
                    <img
                      src="/src/assets/svg/rightIcon.svg"
                      alt="separator"
                      className="mx-4"
                      style={ { width: '24px', height: '44px' } }
                    />
                  )}

                  {/* AI代理项 */}
                  <div
                    className="flex cursor-pointer items-center transition-opacity hover:opacity-80"
                    style={ { gap: '16px' } }
                    onClick={ () => onAgentClick?.(agent) }
                  >
                    {agent.customIcon
                      ? (
                          agent.customIcon
                        )
                      : agent.icon
                        ? (
                            <img
                              src={ agent.icon }
                              alt={ agent.name }
                              className="object-contain"
                              style={ {
                                width: '38px',
                                height: '40px',
                              } }
                            />
                          )
                        : (
                            <div
                              className="rounded bg-gray-200"
                              style={ {
                                width: '38px',
                                height: '40px',
                              } }
                            />
                          )}
                    <span
                      className="text-black"
                      style={ {
                        fontSize: '14px',
                        fontWeight: 600,
                      } }
                    >
                      {agent.name}
                    </span>
                  </div>
                </React.Fragment>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 下拉卡片区域 */}
      <AnimatePresence>
        {isDropdownExpanded && (
          <motion.div
            initial={ { height: 0, opacity: 0 } }
            animate={ { height: 'auto', opacity: 1 } }
            exit={ { height: 0, opacity: 0 } }
            transition={ { duration: 0.3 } }
            className="project-dropdown absolute left-4 top-full z-50 mt-2 overflow-hidden border border-gray-200 rounded-lg bg-white shadow-xl"
            style={ {
              width: dropdownWidth,
              maxHeight: dropdownMaxHeight,
            } }
          >
            {dropdownContent || (
              <ProjectSidebar
                ref={ projectSidebarRef }
                onItemClick={ handleItemClick }
                setMessages={ setMessages }
                onNewProject={ handleNewProject }
              />
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default TopBar

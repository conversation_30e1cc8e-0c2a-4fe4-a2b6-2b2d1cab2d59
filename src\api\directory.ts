import { request } from "@/utils"
import { CatalogDO } from "@/dts"

const prefix = "/app/directory"

export const API_DIRECTORY = {

  async getDirectories(params: GetDirectoriesPO): Promise<CatalogDO[]> {
    return request.get(`${prefix}/list`, {
      params,
    })
  },

  /**
   * 添加
   * @param params 
   * @returns 
   */
  async addDirectory(params: AddDirectoryRO): Promise<CatalogDO["id"]> {
    return request.post(prefix, params)
  },

  /**
   * 重命名
   * @param params 
   * @returns 
   */
  async renameDirectory(params: {
    dirId: CatalogDO["id"]
    name: string
  }): Promise<number> {
    return request.put(`${prefix}/${params.dirId}`, { name: params.name })
  },
  
  /**
   * 删除目录
   * @param params 
   * @returns 
   */
  async deleteDirectory(params: {
    dirId: CatalogDO["id"]
    /** 是否删除子文件 */
    nested?: boolean
  }): Promise<boolean> {
    return request.delete(`${prefix}/${params.dirId}`, {
      params: { nested: params.nested },
    })
  },
}

interface GetDirectoriesPO {
  /** parentDirectoryId */
  parentDirectoryId?: CatalogDO["id"]
  /** createUser */
  createUser?: string
  /** 排序条件,示例值(createTime,desc) */
  sort?: string
}

interface AddDirectoryRO {
  /** 姓名 */
  name: string
  /** 所属目录id */
  parentDirectoryId?: CatalogDO["id"]
}

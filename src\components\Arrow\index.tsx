import type { CSSProperties } from 'react'
import classnames from 'clsx'
import { memo } from 'react'

function InnerArrow({
  style,
  className,

  color = 'black',
  size = 6,
  thickness = 1,
  rotate = 0,
}: ArrowProps) {
  return <div
    className={ classnames(className) }
    style={ {
      borderTop: 'none',
      borderRight: 'none',
      borderStyle: 'solid',
      borderWidth: `${thickness}px`,
      borderLeftColor: color,
      borderTopColor: color,
      borderRightColor: 'transparent',
      borderBottomColor: 'transparent',

      transform: `rotate(${rotate + 135}deg)`,
      transformOrigin: 'left left',
      width: size,
      height: size,
      transition: '.3s',
      ...style,
    } }
  >
  </div>
}

export const Arrow = memo<ArrowProps>(InnerArrow)
Arrow.displayName = 'Arrow'

export type ArrowProps = {
  className?: string
  style?: CSSProperties

  color?: string
  size?: number
  thickness?: number
  rotate?: number
}

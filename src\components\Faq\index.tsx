import type { DropdownProps } from '@/components/Dropdown'
import { Dropdown } from '@/components/Dropdown'
import { isMobileDevice } from '@/config'
import { clsx } from 'clsx'
import { memo } from 'react'

export const Faq = memo<FaqProps>((
  {
    style,
    className,
    items,
  },
) => {
  return <div
    className={ clsx(
      'FaqContainer',
      isMobileDevice
        ? 'py-8'
        : 'py-16',
      className,
    ) }
    style={ style }
  >
    <h3 className={ clsx(
      'text-center text-white font-medium',
      isMobileDevice
        ? 'text-2xl mb-8 mt-8'
        : 'text-4xl mb-16 mt-16 md:text-5xl',
    ) }>
      FAQ
    </h3>

    <div className={ clsx(
      'mx-auto max-w-7xl',
      isMobileDevice
        ? 'px-4 py-4'
        : 'py-8',
    ) }>
      <Dropdown
        items={ items }
        className={ `
        text-white space-y-4
        ` }
        sectionHeaderClassName={ clsx(
          '!text-white',
          isMobileDevice
            ? 'text-base'
            : 'text-lg',
        ) }
        itemTitleClassName="!text-gray-400"
        itemDescClassName="!text-gray-400"
        itemInactiveClassName="hover:bg-none"
        itemClassName={ clsx(
          `border border-zinc-600/50 space-y-3
          transition-colors duration-300 hover:border-zinc-500/50
          rounded-xl`,
          isMobileDevice
            ? 'py-2 px-3 text-sm'
            : 'py-3',
        ) }
      ></Dropdown>
    </div>
  </div>
})

Faq.displayName = 'Faq'

export type FaqProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  items: DropdownProps['items']
}

import {
  LoraTrainTaskPO,
  MaterialDO,
  ModelsQueryPO,
  PagerList,
  CustomTrainTaskPO,
  formTrainPO,
  PromtTrainTaskPO
} from "@/dts"
import { request } from "@/utils"


export const API_MATERIAL = {
  /**
   * 获取物料模型列表
   * @param params
   * @returns
   */
  async getList(params: ModelsQueryPO): Promise<PagerList<MaterialDO>> {
    return request.get(`/app/material`, { params })
  },
  /**
   * @deprecated
   * 创建lora训练任务
   * @param params
   * @returns
   */
  async createLoraTrainTask(p: LoraTrainTaskPO): Promise<unknown> {
    return request.post(`/app/material`, p)
  },
  /**
   * 单个获取
   * @param params
   * @returns
   */
  async getById(id: MaterialDO["id"]): Promise<MaterialDO> {
    return request.get(`/app/material/${id}`)
  },

  /**
   * @deprecated
   * 新增物料上传表单
   * @param params
   * @returns
   */
  async trainInfo(file: File, values: formTrainPO): Promise<string[]> {
    const url = `/app/material/ai`
    const formData = new FormData()
    formData.append("image", file)
    formData.append("name", `${values.name == undefined ? "" : values.name}`)
    formData.append("type", `${values.type == undefined ? "" : values.type}`)
    formData.append(
      "brand",
      `${values.brand == undefined ? "" : values.brand}`,
    )
    formData.append(
      "designer",
      `${values.designer == undefined ? "" : values.designer}`,
    )
    formData.append(
      "price",
      `${values.price == undefined ? "" : Number(values.price)}`,
    )
    formData.append("directoryId", values.folderId)
    return request.post(url, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    })
  },


  /**
   * AI Drawing Customize 预选图
   * @returns
   */
  async getModelList(): Promise<RoomData> {
    return request.get(`/app/ai-bg-resource-style/all-hierarchical-detail`)
  },

  /**
   * AI模板生图（三合一）
   * @returns
   */
  async trainTask(params: TrainTask): Promise<ResourceIds> {
    /**
     * @test 测试数据 16:9 的图
     */
    // return {
    //   resourceIds: [
    //     "636968480729470015",
    //     "636968480742052928",
    //     "636968480746247233",
    //     "636968480750441538"
    //   ]
    // }

    const formData = new FormData()
    params.images?.forEach(file => {
      formData.append("images", file)
    })

    for (const k in params) {
      const key = k as keyof TrainTask
      if (key === "images") continue

      const item = params[key]
      item && formData.append(key, item)
    }

    return request.post('/app/ai-bg-resource/image-generation', formData)
  },

  async checkTask(resourceIds: string[], duration = 3000) {
    if (!resourceIds.length) {
      return
    }

    const res: string[] = []
    const { promise, resolve } = Promise.withResolvers<string[]>()
    const req = async () => {
      const data = await API_MATERIAL.get64ById({ resourceIds })
      if (!data.imagesBase64?.length) {
        return
      }

      clear()

      /**
       * 初始化大小
       */
      for (let i = 0; i < data.imagesBase64.length; i++) {
        const item = data.imagesBase64[i]
        res[i] = item.compressedImageBase64
      }

      resolve(res)
    }

    const id = window.setInterval(req, duration)
    const clear = () => clearInterval(id)

    return promise
  },

  /**
   * @deprecated
   * AI风格生图
   */
  async customTrainTask(params: CustomTrainTaskPO): Promise<ResourceIds> {
    return request.post(`/app/ai-bg-resource/style-to-image`, params)
  },

  /**
   * @deprecated
   * AI文生图
   */
  async promptTrainTask(params: PromtTrainTaskPO): Promise<ResourceIds> {
    /**
     * @test 测试数据 16:9 的图
     */
    // return {
    //   resourceIds: [
    //     "636968480729470015",
    //     "636968480742052928",
    //     "636968480746247233",
    //     "636968480750441538"
    //   ]
    // }

    /**
     * @test 测试数据 1:1 的图
     */
    // return {
    //   resourceIds: [
    //     "636970048585471059",
    //     "636970048598053972",
    //     "636970048602248277",
    //     "636970048606442582"
    //   ]
    // }

    return request.post(`/app/ai-bg-resource/text-to-image`, params)
  },

  /**
   * @deprecated
   * AI图生图（图像融合）
   */
  async transferTrainTask(frameScale: string, fileList: File[], type?: string | null): Promise<ResourceIds> {
    /**
     * @test 测试数据
     */
    // 生产环境数据
    // return { "resourceIds": ["622444417931804962", "622444417940193571", "622444417940193572", "622444417944387877"] }

    // 测试环境数据
    // return { "resourceIds": ['624909972803273791', '624909972799079486', '624909972790690877', '624909972778107964',] }


    const url = `/app/ai-bg-resource/image-to-image`
    const formData = new FormData()
    // @ts-ignore
    formData.append('type', type == undefined ? null : type)
    formData.append('frameScale', frameScale)

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i]
      formData.append("images", file)
    }
    return request.post(url, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    })
  },


  /**
   * 根据 resourceIds 拿到 base64
   */
  async get64ById(resourceIds: ResourceIds): Promise<ImgBase64Data> {
    return request.post(`/app/ai-bg-resource/imageBase64`, resourceIds)
  },

}


export type RoomData = {
  id: number
  createUserString: string
  createTime: string
  disabled: boolean
  sceneHierarchicalResp: SceneHierarchicalResp[]
}

export type SceneHierarchicalResp = {
  sceneName: string
  sceneCoverImage: string
  styleHierarchicalResp: StyleHierarchicalResp[]
}

export type StyleHierarchicalResp = {
  modelName: string
  styleName: string
  styleCoverImage: string
}

export type ResourceIds = {
  resourceIds: string[]
}

export type ImgBase64Data = {
  imagesBase64: ImagesBase64[]
}

export type ImagesBase64 = {
  originalImageBase64: string
  compressedImageBase64: string
}

export type TrainTask = Partial<CustomTrainTaskPO> & { images?: File[] }
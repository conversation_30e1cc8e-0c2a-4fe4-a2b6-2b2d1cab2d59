import type { BiJiL<PERSON> } from '@/api/marketTestData/NewType'
import type { Step8Result, TrendApiResult } from '@/api/TrendApi'
import type { ChatMessage, StepNum } from '../types'
import { god } from '@/god'
import { createPublishTagEl } from '@/utils'
import { filterKeys } from '@jl-org/tool'
import PostView from '../components/PostView'
import { ReportCard } from '../components/ReportComponents/ReportCard'
import { DistributionEvent, eventBus, stepButtonConfig, trendApi } from '../constants'
import { createThinkingExtractor, handleTitle } from '../tool'
import { addMessageByTitle, addReportByTitle, createReportCard } from './addChat'
import { cacheExecuteAndStream } from './chatActions'
import { addReportItem, ButtonStateManager, createAgentTask, createCardMessage, createLoading, createThink, removeMessage, thinkEnd, updateAgentTask, updateAgentTaskStatus, updateMessageById } from './create'
import { addDataToStateAiCache, bijiListToCardData, getNextStep } from './fns'
import { trendAg } from './index'

/**
 * 创建Research Analyst的结果卡片 - 创建两个卡片
 */
export function createResearchAnalystCard(curStep: StepNum, store = trendAg) {
  const { stateStore } = store

  /** 根据当前流程步骤动态选择左侧图标 */
  const getLeftIconByStep = (currentStep: StepNum): string => {
    switch (currentStep) {
      case 'step1':
        return 'researchAnalyst' // 研究分析师
      case 'step2':
        return 'brandStrategist' // 品牌策略师
      case 'step3':
        return 'creativeDirector' // 创意总监
      case 'step4':
        return 'operationsManager' // 运营经理
      default:
        return 'Bot' // 默认使用 Bot 图标
    }
  }

  const currentLeftIcon = getLeftIconByStep(curStep)

  /** 创建第一个报告项 - 对应流式数据的research_content */
  const industryReportItem = addReportItem({
    type: 'markdown',
    title: '_internal_research_content', // 使用内部标题，避免被createReportCard处理
    content: 'Analyzing industry trends and market dynamics...',
    meta: {
      step: curStep,
      canTransformCode: true,
    },
  }, true, true, store) // 自动打开报告面板，显示第一个tab

  /** 创建第二个报告项 - 对应流式数据的research_content_1 */
  const researchReportItem = addReportItem({
    type: 'markdown',
    title: '_internal_research_content_1', // 使用内部标题，避免被createReportCard处理
    content: 'Analyzing competitor strategies and positioning opportunities...',
    meta: {
      step: curStep,
      canTransformCode: true,
    },
  }, true, false, store) // 不自动打开，避免覆盖第一个tab

  /** 创建第三个内部报告项 - 对应brand_market_report */
  const brandReportItem = addReportItem({
    type: 'markdown',
    title: '_internal_brand_market_report', // 使用内部标题，避免被createReportCard处理
    content: 'Generating comprehensive brand market analysis...',
    meta: {
      step: curStep,
      canTransformCode: true,
    },
  }, true, false, store) // 不自动打开，避免覆盖其他tab

  /** 手动设置映射，使addReportByTitle能够找到正确的报告项进行更新 */
  const { mapperStore } = store
  mapperStore.nodeTitleToReportId.research_content = industryReportItem.id
  mapperStore.nodeTitleToReportId.research_content_1 = researchReportItem.id
  mapperStore.nodeTitleToReportId.brand_market_report = brandReportItem.id

  /** 为step1中的brand_report和industry_report也创建映射，避免创建额外卡片 */
  mapperStore.nodeTitleToReportId.brand_report = industryReportItem.id // 复用第一个报告项
  mapperStore.nodeTitleToReportId.industry_report = researchReportItem.id // 复用第二个报告项

  /** 第一个卡片 - Industry Analysis */
  createCardMessage(
    {
      title: 'Industry Analysis',
      description: 'Comprehensive analysis of market trends and industry dynamics for strategic positioning.',
      variant: 'success',
      onClick: () => {
        stateStore.isReportOpen = true
        store.taskStore.currentStep = curStep
        eventBus.emit(DistributionEvent.SetActiveTab, industryReportItem.id)
      },
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon1', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'Industry Analysis',
          description: 'Comprehensive analysis of market trends and industry dynamics for strategic positioning.',
        },
        layout: 'simple' as const,
      },
    },
    {
      meta: {
        step: curStep,
        cardId: 'industry-analysis',
      },
    },
    store,
  )

  /** 第二个卡片 - Competitive Analysis */
  createCardMessage(
    {
      title: 'Competitive Analysis',
      description: 'In-depth analysis of market competitors and identification of positioning opportunities.',
      variant: 'success',
      onClick: () => {
        stateStore.isReportOpen = true
        store.taskStore.currentStep = curStep
        eventBus.emit(DistributionEvent.SetActiveTab, researchReportItem.id)
      },
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon2', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'Competitive Analysis',
          description: 'In-depth analysis of market competitors and identification of positioning opportunities.',
        },
        layout: 'simple' as const,
      },
    },
    {
      meta: {
        step: curStep,
        cardId: 'competitive-analysis',
      },
    },
    store,
  )
}

/**
 * 创建Brand Strategist的结果卡片
 */
export function createBrandStrategistCards(curStep: StepNum, store = trendAg) {
  const { stateStore } = store

  /** 创建占位卡片，数据将在API完成后填充 */
  const reportItem = addReportItem({
    type: 'markdown',
    title: 'Brand Strategy Analysis',
    content: 'Analyzing market trends and competitor strategies...',
    meta: {
      step: curStep,
    },
  }, true, false, store) // 不自动打开报告面板

  createCardMessage(
    {
      title: 'Brand Strategy Analysis',
      description: 'Analyzing market trends and competitor strategies...',
      variant: 'success',
      onClick: () => {
        stateStore.isReportOpen = true
        store.taskStore.currentStep = curStep
        eventBus.emit(DistributionEvent.SetActiveTab, reportItem.id)
      },
    },
    {
      meta: {
        step: curStep,
      },
    },
    store,
  )
}

/**
 * 创建Creative Director的结果卡片
 */
export function createCreativeDirectorCard(curStep: StepNum, store = trendAg) {
  const { stateStore } = store

  /** 创建占位卡片 */
  const reportItem = addReportItem({
    type: 'markdown',
    title: 'Creative Director Results',
    content: 'Creating content strategy and creative direction...',
    meta: {
      step: curStep,
    },
  }, true, false, store) // 不自动打开报告面板

  createCardMessage(
    {
      title: 'Content Creation Strategy',
      description: 'Creating content strategy and creative direction...',
      variant: 'success',
      onClick: () => {
        stateStore.isReportOpen = true
        store.taskStore.currentStep = curStep
        eventBus.emit(DistributionEvent.SetActiveTab, reportItem.id)
      },
    },
    {
      meta: {
        step: curStep,
      },
    },
    store,
  )
}

/**
 * 创建Content Manager的结果卡片
 */
export function createContentManagerCard(curStep: StepNum, store = trendAg) {
  const { stateStore } = store
  console.warn('[createContentManagerCard] 创建Content Manager卡片，步骤:', curStep)

  /** 创建占位卡片 */
  const reportItem = addReportItem({
    type: 'markdown',
    title: 'Content Manager Results',
    content: 'Preparing content for posting and distribution...',
    meta: {
      step: curStep,
    },
  }, true, false, store) // 不自动打开报告面板

  /** 手动设置时间戳，确保卡片在按钮之后 */
  const baseTimestamp = Date.now()
  const cardMessage = createCardMessage(
    {
      title: 'Content Ready for Posting',
      description: 'Preparing content for posting and distribution...',
      variant: 'success',
      onClick: () => {
        stateStore.isReportOpen = true
        store.taskStore.currentStep = curStep
        eventBus.emit(DistributionEvent.SetActiveTab, reportItem.id)
      },
    },
    {
      meta: {
        step: curStep,
      },
    },
    store,
  )

  /** 手动调整时间戳，确保卡片在按钮之后显示 */
  cardMessage.timestamp = baseTimestamp + 1000 // 增加1秒确保排序正确

  console.warn('[createContentManagerCard] 卡片已创建，ID:', cardMessage.id, '时间戳:', cardMessage.timestamp, '步骤:', cardMessage.meta?.step)
}

/**
 * 创建五个策略展示卡片 - 在第四阶段完成后显示
 */
export function createStrategyShowcaseCards(curStep: StepNum, store = trendAg) {
  const { stateStore, taskStore } = store

  /** 根据当前流程步骤动态选择左侧图标 */
  const getLeftIconByStep = (currentStep: StepNum): string => {
    switch (currentStep) {
      case 'step1':
        return 'TrendingUp' // 研究分析师
      case 'step2':
        return 'Users' // 品牌策略师
      case 'step3':
        return 'DollarSign' // 创意总监
      case 'step4':
        return 'BarChart3' // 运营经理
      default:
        return 'BarChart3' // 默认使用 BarChart3 图标
    }
  }

  const currentLeftIcon = getLeftIconByStep(curStep)

  /** 生成 5 张策略展示卡片 */
  const strategyCards = [
    {
      id: 'competitive-analysis',
      title: 'Competitive Analysis',
      description: 'Analyze market competitors and identify positioning opportunities for your brand.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon1', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'Competitive Analysis',
          description: 'Analyze market competitors and identify positioning opportunities for your brand.',
        },
        layout: 'simple' as const,
      },
    },
    {
      id: 'trend-report',
      title: 'The Trend Report and Reference Posts',
      description: 'Reference posts curated to match your brand positioning with social media insights.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon2', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'The Trend Report and Reference Posts',
          description: 'Reference posts curated to match your brand positioning.',
        },
        socialPost: {
          image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
          description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
          author: {
            name: 'Milla',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
          },
          stats: {
            likes: 30.1,
          },
        },
        button: {
          text: 'Select my preference',
          variant: 'gradient-border' as const,
          position: 'bottom-left' as const,
        },
        layout: 'social' as const,
      },
    },
    {
      id: 'post-plan',
      title: 'RedNote Post Launch Plan',
      description: 'Strategic content planning for RedNote platform with engagement optimization.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon3', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'RedNote Post Launch Plan',
          description: 'Strategic content planning for RedNote platform with engagement optimization.',
        },
        socialPost: {
          image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
          description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
          author: {
            name: 'Milla',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
          },
          stats: {
            likes: 30.1,
          },
        },
        layout: 'social' as const,
      },
    },
    {
      id: 'trend-analysis',
      title: 'The Trend Report',
      description: 'Review the trend report based on your choice with detailed market insights.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon4', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'The Trend Report',
          description: 'Review the trend report based on your choice',
          customContent: 'TrendTopicBox',
        },
        button: {
          text: 'Start content creation',
          variant: 'gradient-border' as const,
          position: 'bottom' as const,
          // 根据数据加载状态动态显示按钮 - 始终显示，让组件自己控制
          show: true,
          onClick: async () => {
            console.log('[The Trend Report] Start content creation 按钮被点击')
            
            // 只通过事件触发，避免重复调用
            // ChatPage 中会监听这个事件并处理
            const { ChatEventBus } = await import('@/pages/ChatV2/constants')
            ChatEventBus.emit('startOriginalWork')
            
            console.log('[The Trend Report] 已触发 startOriginalWork 事件')
          }
        },
        layout: 'report' as const,
      },
    },
    {
      id: 'strategy-result',
      title: 'Result Card for Text Based Report',
      description: 'Comprehensive strategy results with actionable recommendations for implementation.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'rightIcon', // 使用 rightIcon.svg
          size: 'md' as const,
        },
        content: {
          title: 'Result Card for Text Based Report',
          description: 'Comprehensive strategy results with actionable recommendations for implementation.',
        },
        button: {
          text: 'Approve Strategy',
          variant: 'gradient-border' as const,
          position: 'bottom-left' as const,
        },
        layout: 'simple' as const,
      },
    },
  ]

  /** 为每张策略卡片创建消息 - 立即创建，无延迟 */
  strategyCards.forEach((card, index) => {
    const cardClick = () => {
      taskStore.currentStep = curStep
      stateStore.isReportOpen = true
      /** 显示默认内容或创建新的报告项 */
      console.log(`Strategy card clicked: ${card.title}`)
    }

    /** 如果是 trend-analysis 卡片，设置按钮点击处理 */
    if (card.id === 'trend-analysis' && card.cardConfig?.button) {
      card.cardConfig.button.onClick = async () => {
        console.log('[trend-analysis] Start content creation button clicked')

        // 直接触发 original_work 工作流，具体的检查在 ChatPage 中进行
        const { ChatEventBus } = await import('../../ChatV2/constants')
        ChatEventBus.emit('startOriginalWork')
      }
    }

    createCardMessage(
      {
        title: card.title,
        description: card.description,
        variant: 'success',
        onClick: cardClick,
        /** 添加新的 cardConfig 属性 */
        cardConfig: card.cardConfig,
        /** 为第二个卡片（索引为1）添加自定义样式 */
        cardClassName: index === 1
          ? 'w-106.5 h-106.5'
          : undefined,
      },
      {
        meta: {
          step: curStep,
          cardId: card.id,
        },
      },
      store,
    )
  })
}

async function executeStep(
  step: StepNum,
  onStream: (res: TrendApiResult) => void,
  extraData: {
    params?: any
  } = {},
) {
  const { data } = await cacheExecuteAndStream(
    {
      workflowStep: step,
      params: extraData.params,
    },
    onStream,
  )

  return data
}

function finishBeforeAgent(store = trendAg) {
  const lastStep = store.getLastStep()
  updateAgentTaskStatus(lastStep, 'complete', store)
  updateAgentTask(lastStep, {
    actions: [],
  }, store)
}

function createNewAgent(stepNum?: StepNum, store = trendAg) {
  const curStep = createAgentTask(undefined, stepNum, store)
  if (curStep === -1) {
    return
  }
  updateAgentTaskStatus(curStep, 'in-progress', store)
}

/** 移除 finishCurrentAgent 函数，改为使用内联按钮消息 */

/**
 * md json
 */
export async function handleStep3(store = trendAg) {
  console.warn('[handleStep3] 函数开始执行')
  const { stateStore, messageStore } = store

  try {
    console.warn('[handleStep3] 进入 try 块')
    finishBeforeAgent(store)
    createNewAgent(undefined, store)

    const curStep = getNextStep(store).curStep
    let isFirst = true
    const loading = createLoading({ meta: { step: curStep } }, store)

    const titles: string[] = [
      // 'brand_data',
      // 'industry_data',
      // 'product_data',
      // 'competitor_data',
    ]
    const data = await executeStep(
      'step3',
      (res) => {
        const addData = filterKeys(res.data, titles)
        if (res.data.biji_data && isFirst) {
          isFirst = false
          removeMessage(loading.id, store)
          updateAgentTaskStatus(curStep, 'in-progress', store)
        }

        addMessageByTitle(addData, { meta: { step: curStep } }, store)
        addDataToStateAiCache(addData, store)
      },
      {
        params: {},
      },
    )

    const jsons = filterKeys(data as any, ['biji_data', 'daren_data']) as any

    if (!jsons.biji_data) {
      throw new Error('biji_data is required')
    }
    if (!jsons.daren_data) {
      throw new Error('daren_data is required')
    }

    /** 处理笔记和达人数�? */
    Object.entries(jsons).forEach(([k, v]) => {
      const json = JSON.parse((v as any).content.trim())
      if (
        (Array.isArray(json) && json.length <= 0)
        || !Array.isArray(json)
      ) {
        throw new Error(`${k} is required`)
      }
      stateStore.cacheAiData[k] = json

      if (k === 'biji_data' && Array.isArray(json) && json.length > 0) {
        // const reportItem = addReportItem({
        //   type: 'biji_data',
        //   meta: {
        //     step: curStep,
        //     bijiData: json,
        //   },
        //   content: '',
        //   title: 'Hot Notes',
        // })

        // /** 创建符合设计图的笔记卡片 */
        // const topBijiData = json.slice(0, 4) as BiJiData[]

        // /** 创建一个卡片，展示前四条笔记数�?*/
        // createCardMessage(
        //   {
        //     title: 'Rednote Reference List',
        //     variant: 'success',
        //     content: (
        //       <ReportCard data={ topBijiData } />
        //     ),
        //     onClick: () => {
        //       stateStore.isReportOpen = true
        //       taskStore.currentStep = curStep
        //       eventBus.emit(DistributionEvent.SetActiveTab, reportItem.id)
        //     },
        //   },
        //   {
        //     meta: {
        //       step: curStep,
        //     },
        //   },
        // )
      }

      // if (k === 'daren_data' && Array.isArray(json) && json.length > 0) {
      //   addMessageByTitle(
      //     {
      //       daren_data: {
      //         content: JSON.stringify(json),
      //       },
      //     },
      //     {
      //       meta: {
      //         step: curStep,
      //       },
      //     },
      //   )
      // }
    })

    await handleStep4(store)
  }
  catch (error: any) {
    stateStore.hasError = true
    stateStore.errorMsg = error?.message || 'System Error, please try again later'
    console.error(error)
  }
}

async function handleStep4(store = trendAg) {
  const { stateStore, mdToCodePreview, messageStore, reportStore } = store

  try {
    const curStep = getNextStep(store).curStep

    const reportTitle = 'brand_market_report'
    mdToCodePreview[reportTitle].mdIsProcessing = true

    const modifyParams = {
      brand_data: stateStore.cacheAiData.brand_data || '',
      brand_report: stateStore.cacheAiData.brand_report || '',
      competitor_data: stateStore.cacheAiData.competitor_data || '',
      industry_data: stateStore.cacheAiData.industry_data || '',
      industry_report: stateStore.cacheAiData.industry_report || '',
      product_data: stateStore.cacheAiData.product_data || '',
    }

    const extractors = new Map<string, ReturnType<typeof createThinkingExtractor>>()
    const mainExtractor = createThinkingExtractor()
    const thinkMessage = createThink(mainExtractor.getResult().thinking, { meta: { step: curStep } })
    messageStore.messages.push(thinkMessage)

    const contents: Record<string, string> = {}
    let u: ReturnType<typeof addReportByTitle>['update']

    await executeStep(
      'step4_1' as any,
      (res) => {
        Object.entries(res.data).forEach(([k, v]) => {
          if (!extractors.has(k)) {
            extractors.set(k, createThinkingExtractor())
            contents[k] = ''
          }

          const extractor = extractors.get(k)!
          const lastLen = contents[k].length
          contents[k] = v.content

          const cutContent = contents[k].slice(lastLen)
          extractor.add(cutContent)
        })

        const results = Array.from(extractors.values()).map(ext => ext.getResult())
        const mergedResult = createThinkingExtractor.mergeResults(results)

        updateMessageById(thinkMessage.id, {
          content: mergedResult.thinking,
        }, store)

        const c = mergedResult.hasThinking && !mergedResult.text
          ? mergedResult.thinking
          : mergedResult.text

        u = addReportByTitle(
          { [reportTitle]: {
            type: 'success',
            content: c,
            msg: null,
            finished: true,
          } },
          {
            type: 'markdown',
            meta: {
              step: curStep,
              canTransformCode: true,
            },
          },
          true,
          store,
        ).update
      },
      {
        params: {
          biji_data: JSON.stringify(stateStore.cacheAiData.biji_data),
          daren_data: JSON.stringify(stateStore.cacheAiData.daren_data),
          ...modifyParams,
        },
      },
    )

    if (
      !contents.research_content
      || !contents.research_content_1
    ) {
      throw new Error('research_content and research_content1 are required')
    }

    await executeStep(
      'step4_2' as any,
      (res) => {
        Object.entries(res.data).forEach(([k, v]) => {
          if (!extractors.has(k)) {
            extractors.set(k, createThinkingExtractor())
            contents[k] = ''
          }

          const extractor = extractors.get(k)!
          const lastLen = contents[k].length
          contents[k] = v.content

          const cutContent = contents[k].slice(lastLen)
          extractor.add(cutContent)
        })

        const results = Array.from(extractors.values()).map(ext => ext.getResult())
        const mergedResult = createThinkingExtractor.mergeResults(results)

        updateMessageById(thinkMessage.id, {
          content: mergedResult.thinking,
        }, store)

        const c = mergedResult.hasThinking && !mergedResult.text
          ? mergedResult.thinking
          : mergedResult.text

        u(c, reportTitle)
      },
      {
        params: {
          biji_data: JSON.stringify(stateStore.cacheAiData.biji_data),
          daren_data: JSON.stringify(stateStore.cacheAiData.daren_data),
          research_content: contents.research_content,
          research_content_1: contents.research_content_1,
          ...modifyParams,
        },
      },
    )

    const allContent = Object.values(contents).join('')
    if (!allContent) {
      throw new Error(`${reportTitle} is required`)
    }
    else {
      /** 使用全局按钮状态管理器正确结束thinking状态 */
      const globalButtonManager = (store as any).globalButtonManager || new ButtonStateManager(store)
      globalButtonManager.endThinking(undefined, curStep)

      /**
       * 删除createReportCard调用，避免创建多余卡片
       * 第一阶段的卡片已经在createResearchAnalystCard中创建，且能正确关联reportItems
       */
      mdToCodePreview[reportTitle].mdIsProcessing = false

      const finalResults = Array.from(extractors.values()).map(ext => ext.getResult())
      const finalMerged = createThinkingExtractor.mergeResults(finalResults)
      stateStore.cacheAiData[reportTitle] = finalMerged.text
    }

    // Research Analyst 完成，按钮已在 handleFormSubmit 中创建
    console.warn('[handleStep3] Research Analyst 完成，按钮已在 handleFormSubmit 中创建')
  }
  catch (error: any) {
    stateStore.hasError = true
    stateStore.errorMsg = error?.message || 'System Error, please try again later'
    console.error(error)
  }
}

/**
 * json
 */
export async function handleStep6(store = trendAg) {
  const { stateStore, messageStore } = store

  try {
    finishBeforeAgent(store)
    createNewAgent(undefined, store)

    const curStep = getNextStep(store).curStep
    let isFirst = true
    const loading = createLoading({ meta: { step: curStep } }, store)

    /** 使用全局按钮状态管理器 */
    const buttonManager = (store as any).globalButtonManager || new ButtonStateManager(store)
    let thinkMessage: ChatMessage | null = null
    const modifyParams = {
      brand_market_report: stateStore.cacheAiData.brand_market_report || '',
    }

    let cardsCreated = false
    const data = await executeStep(
      'step6',
      (res) => {
        if (Object.keys(res.data).length > 0 && isFirst) {
          isFirst = false
          removeMessage(loading.id, store)
          updateAgentTaskStatus(curStep, 'in-progress', store)
        }

        if (res.data.biji_cot && !thinkMessage) {
          thinkMessage = buttonManager.startThinking(res.data.biji_cot.content, curStep)

          /** Thinking开始后立即创建卡片 */
          if (!cardsCreated) {
            cardsCreated = true
            setTimeout(() => {
              createBrandStrategistCards(curStep, store)
            }, 100) // 短暂延迟确保thinking消息已创建
          }
        }
        else if (res.data.biji_cot && thinkMessage) {
          buttonManager.updateThinking(res.data.biji_cot.content)
        }
      },
      {
        params: {
          biji_data: JSON.stringify(stateStore.cacheAiData.biji_data),
          daren_data: JSON.stringify(stateStore.cacheAiData.daren_data),
          ...modifyParams,
        },
      },
    )

    /** 结束思考状态，同时禁用按钮 */
    if (thinkMessage) {
      const globalButtonManager = (store as any).globalButtonManager || new ButtonStateManager(store)
      globalButtonManager.endThinking(undefined, curStep)
    }

    const jsons = filterKeys(data as any, ['biji_list', 'daren_list']) as any
    if (!jsons.biji_list) {
      throw new Error('biji_list is required')
    }
    if (!jsons.daren_list) {
      throw new Error('daren_list is required')
    }

    /** 处理数据并更新已创建的卡片 */
    Object.entries(jsons).forEach(([k, v]) => {
      const json = JSON.parse((v as any).content.trim())
      stateStore.cacheAiData[k] = json

      if (k === 'biji_list') {
        if (
          (Array.isArray(json) && json.length <= 0)
          || !Array.isArray(json)
        ) {
          throw new Error('biji_list is required')
        }
        else {
          const bijiList = json[0] as BiJiList
          /** 更新报告项数据 */
          const reportItem = addReportItem({
            type: 'biji_list',
            title: 'Benchmarking notes',
            content: '',
            meta: {
              step: curStep,
              bijiList,
            },
          }, true, true, store)
        }
      }
    })

    // Brand Strategist 完成，在消息流中显示 Continue to Content Creation 按钮
    const nextButtonLabel = stepButtonConfig.step2?.nextButtonLabel
    if (nextButtonLabel) {
      /** 使用全局按钮状态管理器 */
      const globalButtonManager = (store as any).globalButtonManager || new ButtonStateManager(store)

      globalButtonManager.createManagedButton(
        {
          label: nextButtonLabel,
          type: 'primary',
          onClick: async () => {
            /** 执行下一步：Creative Director */
            await handleStep7(store)
          },
        },
        {
          meta: {
            step: curStep, // 使用当前步骤，确保按钮显示在正确位置
          },
        },
      )
    }
  }
  catch (error: any) {
    stateStore.hasError = true
    stateStore.errorMsg = error?.message || 'System Error, please try again later'
    console.error(error)
  }
}

/**
 * md json
 */
export async function handleStep7(store = trendAg) {
  const { stateStore } = store

  try {
    const biji_url = stateStore.cacheAiData.selected_biji_url || ''
    if (!biji_url) {
      god.messageWarn('Please select a note first')
      return
    }

    finishBeforeAgent(store)
    createNewAgent(undefined, store)

    const curStep = getNextStep(store).curStep

    /** 直接创建Creative Director卡片，不需要thinking过程 */
    createCreativeDirectorCard(curStep, store)

    /** 模拟数据处理，直接设置完成状态 */
    updateAgentTaskStatus(curStep, 'complete', store)

    /** 短暂延迟后手动结束按钮loading状态 */
    setTimeout(() => {
      const globalButtonManager = (store as any).globalButtonManager
      if (globalButtonManager) {
        /** 结束当前按钮的loading状态 */
        const buttonId = `step2-Continue to Content Creation`
        globalButtonManager.setButtonState(buttonId, { loading: false, disabled: true })
      }
    }, 100)

    /** 设置模拟数据 */
    stateStore.cacheAiData.biji_content = 'Creative content strategy and direction for brand positioning...'
    stateStore.cacheAiData.biji_title = 'Content Creation Strategy'

    /** 更新报告项 */
    addReportItem({
      type: 'markdown',
      title: 'Creative Director Results',
      content: `# Content Creation Strategy\n\nCreative content strategy and direction for brand positioning...`,
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, true, store) // 打开报告面板显示最终结果

    // Creative Director 完成，设置任务状态并显示 Continue to Content Posting 按钮
    const nextButtonLabel = stepButtonConfig.step3?.nextButtonLabel
    if (nextButtonLabel) {
      /** 使用全局按钮状态管理器 */
      const globalButtonManager = (store as any).globalButtonManager || new ButtonStateManager(store)

      console.warn('[handleStep7] 创建按钮，步骤:', curStep, '按钮标签:', nextButtonLabel)
      globalButtonManager.createManagedButton(
        {
          label: nextButtonLabel,
          type: 'primary',
          onClick: async () => {
            /** 执行下一步：Content Manager */
            await handleStep8(store)
          },
        },
        {
          meta: {
            step: curStep, // 使用当前步骤，确保按钮显示在正确位置
          },
        },
      )
    }
  }
  catch (error: any) {
    stateStore.hasError = true
    stateStore.errorMsg = error?.message || 'System Error, please try again later'
    console.error(error)
  }
}

/**
 * media
 */
export async function handleStep8(store = trendAg) {
  const { stateStore, taskStore } = store

  try {
    finishBeforeAgent(store)
    createNewAgent(undefined, store)

    const curStep = getNextStep(store).curStep
    console.warn('[handleStep8] 创建卡片，步骤:', curStep)

    /** 延迟创建Content Manager卡片，确保它在按钮之后显示 */
    setTimeout(() => {
      createContentManagerCard(curStep, store)
    }, 200) // 增加延迟时间到200ms

    /** 模拟数据处理，直接设置完成状态 */
    updateAgentTaskStatus(curStep, 'complete', store)

    /** 短暂延迟后手动结束按钮loading状态 */
    setTimeout(() => {
      const globalButtonManager = (store as any).globalButtonManager
      if (globalButtonManager) {
        /** 结束当前按钮的loading状态 */
        const buttonId = `step3-Continue to Content Posting`
        globalButtonManager.setButtonState(buttonId, { loading: false, disabled: true })
      }
    }, 100)

    /** 模拟最终结果数据 */
    const mockResults = {
      title: 'Ready to Post: Brand Content Strategy',
      content: 'Your content is ready for posting across social media platforms with optimized engagement strategies.',
      images: ['https://fal.media/files/tiger/sjiNBmz2HWz4xsoSUaSC-_d458f79f5c0f40f4bdefb652da52c1f9.png'],
    }

    const text = `${mockResults.title}\n\n${mockResults.content}`

    /** 更新报告项数据 */
    const reportItem = addReportItem({
      type: 'phone_preview',
      content: text,
      title: 'RedNote Post',
      meta: {
        step: curStep,
        phonePreview: {
          imageUrl: mockResults.images,
          author: 'PhotoG',
          authorAvatar: 'https://fal.media/files/tiger/sjiNBmz2HWz4xsoSUaSC-_d458f79f5c0f40f4bdefb652da52c1f9.png',
          likes: 100,
          shares: 100,
          comments: 100,
          scale: 0.7,
        },
      },
    }, true, true, store)

    /** 创建最终的RedNote Post卡片 */
    createCardMessage(
      {
        title: 'RedNote Post',
        content: <PostView
          title={ mockResults.title }
          content={ mockResults.content }
          imageUrl={ mockResults.images }
          author="PhotoG"
          authorAvatar="https://fal.media/files/tiger/sjiNBmz2HWz4xsoSUaSC-_d458f79f5c0f40f4bdefb652da52c1f9.png"
        />,

        onClick: () => {
          stateStore.isReportOpen = true
          taskStore.currentStep = curStep
          eventBus.emit(DistributionEvent.SetActiveTab, reportItem.id)
        },
      },
      { meta: { step: curStep } },
      store,
    )

    createPublishTagEl(
      mockResults.title,
      mockResults.content,
      mockResults.images,
    )

    // Content Manager 完成，流程结束（无需按钮）
    /** 最后一步完成，不需要创建内联按钮 */

    /** 在第四阶段完成后，显示五个策略展示卡片 */
    setTimeout(() => {
      createStrategyShowcaseCards(curStep, store)
    }, 500) // 稍微延迟一下，确保RedNote Post卡片先显示
  }
  catch (error: any) {
    stateStore.hasError = true
    stateStore.errorMsg = error?.message || 'System Error, please try again later'
    console.error(error)
  }
}

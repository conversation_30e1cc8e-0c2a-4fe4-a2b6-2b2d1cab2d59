import { primaryColor } from '@/styles/variable'
import { cn } from '@/utils'
import { memo, useEffect, useState } from 'react'

export const Border = memo((props: BorderProps) => {
  const {
    dashLength = 10,
    dashGap = 12,
    strokeColor = '#bbb',
    hoverStrokeColor = primaryColor,
    strokeWidth = 2,
    animated = true,
    enterAnimate = true,
    animationSpeed = 50,
    borderRadius = 20,
    className,
    children,
    style,
  } = props

  const [dashOffset, setDashOffset] = useState(0)
  const [isEnter, setIsEnter] = useState(false)

  useEffect(() => {
    if (!animated)
      return
    if (enterAnimate && !isEnter)
      return

    const interval = setInterval(() => {
      setDashOffset(prev => (prev + 1))
    }, animationSpeed)

    return () => clearInterval(interval)
  }, [animated, animationSpeed, dashLength, dashGap, enterAnimate, isEnter])

  return (
    <div
      className={ cn('relative w-full h-full') }
      onMouseEnter={ () => setIsEnter(true) }
      onMouseMove={ () => setIsEnter(true) }
      onMouseLeave={ () => setIsEnter(false) }
      onMouseOut={ () => setIsEnter(false) }
      style={ style }
    >
      <svg
        className="absolute inset-0 h-full w-full"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x={ strokeWidth / 2 }
          y={ strokeWidth / 2 }
          width={ `calc(100% - ${strokeWidth}px)` }
          height={ `calc(100% - ${strokeWidth}px)` }
          rx={ borderRadius }
          ry={ borderRadius }
          fill="none"
          stroke={
            isEnter
              ? hoverStrokeColor
              : strokeColor
          }
          strokeWidth={ strokeWidth }
          strokeDasharray={ `${dashLength},${dashGap}` }
          strokeDashoffset={ animated
            ? dashOffset
            : 0 }
          className="transition-all duration-300"
        />
      </svg>

      <div
        className={ cn('h-full w-full', className) }
        style={ {
          padding: `${strokeWidth}px`,
          borderRadius: `${borderRadius}px`,
        } }
      >
        { children }
      </div>
    </div>
  )
})

Border.displayName = 'Border'

export type BorderProps = {
  dashLength?: number
  dashGap?: number
  strokeColor?: string
  hoverStrokeColor?: string
  strokeWidth?: number
  animated?: boolean
  enterAnimate?: boolean
  animationSpeed?: number
  borderRadius?: number
}
& React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLDivElement>, HTMLDivElement>

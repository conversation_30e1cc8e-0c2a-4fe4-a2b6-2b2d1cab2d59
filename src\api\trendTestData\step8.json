{"data": {"content": {"content": " 姐妹们！是不是每次买粉底液都纠结？💣\n黄一白？粉二白？橄榄皮？柜姐说的、博主推的，到自己脸上就翻车？\n今天教你用这张「肤色自测卡」10秒搞定肤色判断！ #肤色自测[话题]# #底妆技巧[话题]# #美妆干货[话题]#\n\n🌟 你需要：\n自然光线（靠近窗户！☀）\n素颜！素颜！素颜！\n好用的「肤色自测卡」\n\n🚀 10秒自测步骤 (超简单！)：\n【图片右滑 已附上色卡及自测方法】\n\n🔍 结果揭秘：\n冷皮/粉皮 ✅ 适合银饰，粉调粉底更贴合，涂玫红、浆果色口红超显白！\n暖皮/黄皮 ✅ 适合金饰，黄调粉底是亲妈！橘调、南瓜色口红就是你的本命！\n橄榄皮/中性皮 ✅ 冷暖调都能驾驭但可能都不完美，选带点橄榄调或中性调的粉底更自然！低饱和豆沙、玫瑰色口红很气质！\n\n感觉都差不多？ 恭喜！可能是 中性皮！🎉 选粉底看深浅就好，口红限制少！\n\n⭐️小Tips：\n实在纠结？看看戴金饰好看还是银饰好看！(暖皮金，冷皮银)\n粉橘色口红显气色？暖皮概率大！玫红色更衬你？冷皮没跑了！\n橄榄皮姐妹注意！阳光下可能更显黄绿调，室内光可能偏灰，认准\"橄榄调\"粉底！\n\n💁‍♀️快拿出色卡试试！👇告诉我你是冷皮？暖皮？还是橄榄皮？\n\n#脸部肤色自测[话题]# #美学[话题]# #肤色怎么测[话题]# #找对肤色[话题]# #肤色自测卡[话题]# #变美[话题]# #脸部肤色自测[话题]# #春日妆容[话题]# #冷暖皮[话题]# #黄黑皮[话题]##自测肤色[话题]# #普通人的变美思路[话题]# #冷皮[话题]# #暖皮[话题]# #形象美学[话题]# #底妆[话题]# #粉底液[话题]# #色彩诊断[话题]#"}, "title": {"content": "💥10秒自测！别再买错粉底液啦！"}, "image": {"content": "[\"https://fal.media/files/tiger/sjiNBmz2HWz4xsoSUaSC-_d458f79f5c0f40f4bdefb652da52c1f9.png\", \"https://fal.media/files/tiger/sjiNBmz2HWz4xsoSUaSC-_d458f79f5c0f40f4bdefb652da52c1f9.png\"]"}}, "allJson": [{"processInstanceId": "1942185758874013696", "executionId": "1942441532976140289", "__internal__event": "meta"}, {"content": " ", "node_title": "content", "node_seq_id": "0", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "姐妹们！是不是每次买粉底液都", "node_title": "content", "node_seq_id": "1", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "纠结？💣\n黄一白？粉二", "node_title": "content", "node_seq_id": "2", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "白？橄榄皮？柜姐说的、", "node_title": "content", "node_seq_id": "3", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "博主推的，到自己脸上就翻车？", "node_title": "content", "node_seq_id": "4", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\n今天教你用这张「肤色自测卡", "node_title": "content", "node_seq_id": "5", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "」10秒搞定肤色判断！ #", "node_title": "content", "node_seq_id": "6", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "肤色自测[话题]# #底妆技巧", "node_title": "content", "node_seq_id": "7", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "[话题]# #美妆干货[话题]#", "node_title": "content", "node_seq_id": "8", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\n\n🌟 你需要：\n自然光线（靠", "node_title": "content", "node_seq_id": "9", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "近窗户！☀）\n素颜！素颜", "node_title": "content", "node_seq_id": "10", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "！素颜！\n好用的「肤色自测卡", "node_title": "content", "node_seq_id": "11", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "」\n\n🚀 10秒自测步骤 (超简", "node_title": "content", "node_seq_id": "12", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "单！)：\n【图片右滑 已附上色", "node_title": "content", "node_seq_id": "13", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "卡及自测方法】\n\n🔍 结果", "node_title": "content", "node_seq_id": "14", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "揭秘：\n冷皮/粉皮 ", "node_title": "content", "node_seq_id": "15", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "✅ 适合银饰，粉调粉底", "node_title": "content", "node_seq_id": "16", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "更贴合，涂玫红、浆果色口", "node_title": "content", "node_seq_id": "17", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "红超显白！\n暖皮/黄皮 ", "node_title": "content", "node_seq_id": "18", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "✅ 适合金饰，黄调粉", "node_title": "content", "node_seq_id": "19", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "底是亲妈！橘调、南瓜色", "node_title": "content", "node_seq_id": "20", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "口红就是你的本命！\n橄榄皮", "node_title": "content", "node_seq_id": "21", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "/中性皮 ✅ 冷暖调", "node_title": "content", "node_seq_id": "22", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "都能驾驭但可能都不完美，选带", "node_title": "content", "node_seq_id": "23", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "点橄榄调或中性调的粉底更自", "node_title": "content", "node_seq_id": "24", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "然！低饱和豆沙、玫瑰", "node_title": "content", "node_seq_id": "25", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "色口红很气质！\n\n感觉都差不多？", "node_title": "content", "node_seq_id": "26", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 恭喜！可能是 中性皮！", "node_title": "content", "node_seq_id": "27", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "🎉 选粉底看深浅就好，口红", "node_title": "content", "node_seq_id": "28", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "限制少！\n\n⭐️小Tips：\n实在纠结？看看", "node_title": "content", "node_seq_id": "29", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "戴金饰好看还是银饰好看！(", "node_title": "content", "node_seq_id": "30", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "暖皮金，冷皮银)\n粉", "node_title": "content", "node_seq_id": "31", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "橘色口红显气色？暖皮概率大！", "node_title": "content", "node_seq_id": "32", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "玫红色更衬你？冷皮没跑", "node_title": "content", "node_seq_id": "33", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "了！\n橄榄皮姐妹注意！", "node_title": "content", "node_seq_id": "34", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "阳光下可能更显黄绿调，室内", "node_title": "content", "node_seq_id": "35", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "光可能偏灰，认准\"橄榄调", "node_title": "content", "node_seq_id": "36", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"粉底！\n\n💁‍♀️快拿", "node_title": "content", "node_seq_id": "37", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "出色卡试试！👇告诉我你是", "node_title": "content", "node_seq_id": "38", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "冷皮？暖皮？还是橄榄", "node_title": "content", "node_seq_id": "39", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "皮？\n\n#脸部肤色自", "node_title": "content", "node_seq_id": "40", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "测[话题]# #美学[话题]# #", "node_title": "content", "node_seq_id": "41", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "肤色怎么测[话题]# #找对肤", "node_title": "content", "node_seq_id": "42", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "色[话题]# #肤色自测卡[话", "node_title": "content", "node_seq_id": "43", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "题]# #变美[话题]# #脸部", "node_title": "content", "node_seq_id": "44", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "肤色自测[话题]# #春日妆容[", "node_title": "content", "node_seq_id": "45", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "话题]# #冷暖皮[话题]#", "node_title": "content", "node_seq_id": "46", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " #黄黑皮[话题]##自测肤", "node_title": "content", "node_seq_id": "47", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "色[话题]# #普通人的变美思路[", "node_title": "content", "node_seq_id": "48", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "话题]# #冷皮[话题]# #", "node_title": "content", "node_seq_id": "49", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "暖皮[话题]# #形象美学[话题", "node_title": "content", "node_seq_id": "50", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "]# #底妆[话题]# #粉底液", "node_title": "content", "node_seq_id": "51", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "[话题]# #色彩诊断[话题]", "node_title": "content", "node_seq_id": "52", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "#", "node_title": "content", "node_seq_id": "53", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "", "node_title": "content", "node_seq_id": "54", "node_is_finish": true, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "💥10秒自测！别再买错粉底液啦！", "node_title": "title", "node_seq_id": "0", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "", "node_title": "title", "node_seq_id": "1", "node_is_finish": true, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "[\"https://fal.media/files/tiger/sjiNBmz2HWz4xsoSUaSC-_d458f79f5c0f40f4bdefb652da52c1f9.png\", \"https://fal.media/files/tiger/sjiNBmz2HWz4xsoSUaSC-_d458f79f5c0f40f4bdefb652da52c1f9.png\"]", "node_title": "image", "node_seq_id": "0", "node_is_finish": true, "token": null, "ext": null, "__internal__event": "Message"}, {"debugUrl": {"debug_url": "https://www.coze.cn/work_flow?execute_id=7524564201609134120&space_id=7509711081779593253&workflow_id=7516667315544621083&execute_mode=2"}, "__internal__event": "Done"}, "[DONE]"], "hasError": false}
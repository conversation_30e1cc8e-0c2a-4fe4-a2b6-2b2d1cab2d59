import type { BaseChatParams } from './ChatApi'
import { userStore } from '@/store/userStore'
import { http, request } from '@/utils'
import { Role } from './ChatGPTApi'

export class DeepSeekApi {
  /**
   * deepseek ai 问答
   */
  static async deepSeek(content: string): Promise<string> {
    const res = await request.post('/app/ai-conversation/ai-conversation', {
      content,
    }) as any
    return res.msg
  }

  /**
   * deepseek ai 深度思考问答
   */
  static async deepSeekThink(messagesList: {
    role: Role
    content: string
  }[]): Promise<{
    content: string
    reasoningContent: string
  }> {
    return request.post('/app/ai-conversation/chat', {
      messagesList,
    })
  }

  /**
   * deepseek ai 深度思考问答
   */
  static async deepSeekV3WithStream(
    {
      messages,
      onMsg,
      content,
      imgUrl,
    }: DeepSeekParams,
  ): Promise<{
    content: string
    reason: string
  }> {
    const finalData = {
      content: '',
      reason: '',
    }

    const body = { messages: messages || [] }
    body.messages.push({
      role: Role.User,
      content: [
        {
          type: 'text',
          text: content,
        },
      ],
    })
    if (imgUrl) { }

    const { promise, reject, resolve } = Promise.withResolvers<{
      content: string
      reason: string
    }>()

    try {
      const { promise: ssePromise } = await http.fetchSSE(`/app/ai-conversation/stream/chat-deep-v3`, {
        method: 'POST',
        needParseData: true,
        needParseJSON: true,
        body,
        onMessage(data) {
          const content = DeepSeekApi.fromRespGetContent(data.allJson as any[])
          content && (finalData.content = content)
          onMsg?.(finalData)
          console.log({ finalData, j: data.allJson })
        },
      })

      await ssePromise
      resolve(finalData)
    }
    catch (error) {
      reject(error)
    }

    return promise
  }

  /**
   * 流式 deepseek ai 深度思考问答
   */
  static async deepSeekThinkWithStream(
    {
      messages,
      onMsg,
      content,
      imgUrl,
    }: DeepSeekParams,
  ) {
    const finalData = {
      content: '',
      reason: '',
    }

    const body = { messages: messages || [] }
    body.messages.push({
      role: Role.User,
      content: [
        {
          type: 'text',
          text: content,
        },
      ],
    })
    if (imgUrl) { }

    const { promise, reject, resolve } = Promise.withResolvers<{
      content: string
      reason: string
    }>()

    try {
      const { promise: ssePromise } = await http.fetchSSE(`/app/ai-conversation/stream/chat-deep`, {
        method: 'POST',
        needParseData: true,
        needParseJSON: true,
        body,
        onMessage(data) {
          const content = DeepSeekApi.fromRespGetContent(data.allJson as any[])
          const reason = DeepSeekApi.fromRespGetReason(data.allJson as any[])

          content && (finalData.content = content)
          reason && (finalData.reason = reason)
          onMsg?.(finalData)
        },
      })

      await ssePromise
      resolve(finalData)
    }
    catch (error) {
      reject(error)
    }

    return promise
  }

  static fromRespGetContent(aiResps: DeepSeekResp[]) {
    let res = ''
    aiResps.forEach((obj) => {
      const delta = obj?.choices?.[0]?.delta
      if (delta && typeof delta.content === 'string') {
        res += delta.content
      }
    })
    return res
  }

  static fromRespGetReason(aiResps: DeepSeekResp[]) {
    let res = ''
    aiResps.forEach((obj) => {
      const delta = obj?.choices?.[0]?.delta
      if (delta && typeof delta.reasoning_content === 'string') {
        res += delta.reasoning_content
      }
    })
    return res
  }
}

export type DeepSeekMessage = {
  role: Role
  content: {
    type: 'text'
    text: string
  }[]
}

export type DeepSeekParams = BaseChatParams & {
  onMsg?: (data: { content: string, reason: string }) => void
  messages?: DeepSeekMessage[]
}

export type DeepSeekResp = {
  id: string
  object: string
  created: number
  model: string
  choices: Choice[]
  usage: null
}

export type Choice = {
  index: number
  delta: Delta
  logprobs: null
  finish_reason: null
  matched_stop: null
}

export type Delta = {
  role: Role
  content: string
  reasoning_content: string | null
  tool_calls: null
}

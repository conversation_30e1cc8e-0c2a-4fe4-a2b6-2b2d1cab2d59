/**
 * Trend 模块错误边界组件
 *
 * 功能：
 * - 捕获组件树中的 JavaScript 错误
 * - 显示友好的错误提示界面
 * - 提供错误恢复机制
 * - 记录错误信息用于调试
 */

import type { ErrorInfo, ReactNode } from 'react'
import { But<PERSON> } from '@/components/Button'
import { motion } from 'framer-motion'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { Component } from 'react'

interface Props {
  children: ReactNode
  /** 自定义错误标题 */
  title?: string
  /** 自定义错误描述 */
  description?: string
  /** 是否显示错误详情 */
  showDetails?: boolean
  /** 错误回调 */
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    })

    /** 调用错误回调 */
    this.props.onError?.(error, errorInfo)

    /** 在开发环境下打印错误详情 */
    if (import.meta.env.DEV) {
      console.error('ErrorBoundary caught an error:', error, errorInfo)
    }
  }

  /** 重置错误状态 */
  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  /** 刷新页面 */
  handleRefresh = () => {
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      const {
        title = '出现了一些问题',
        description = '抱歉，页面遇到了错误。您可以尝试刷新页面或重新开始。',
        showDetails = import.meta.env.DEV,
      } = this.props

      return (
        <motion.div
          className="min-h-[400px] flex flex-col items-center justify-center p-8 text-center"
          initial={ { opacity: 0, y: 20 } }
          animate={ { opacity: 1, y: 0 } }
          transition={ { duration: 0.3 } }
        >
          <motion.div
            className="mb-6 h-16 w-16 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20"
            initial={ { scale: 0 } }
            animate={ { scale: 1 } }
            transition={ { delay: 0.1, type: 'spring', stiffness: 200 } }
          >
            <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
          </motion.div>

          <motion.h2
            className="mb-4 text-xl text-gray-900 font-semibold dark:text-gray-100"
            initial={ { opacity: 0 } }
            animate={ { opacity: 1 } }
            transition={ { delay: 0.2 } }
          >
            {title}
          </motion.h2>

          <motion.p
            className="mb-8 max-w-md text-gray-600 dark:text-gray-400"
            initial={ { opacity: 0 } }
            animate={ { opacity: 1 } }
            transition={ { delay: 0.3 } }
          >
            {description}
          </motion.p>

          <motion.div
            className="flex gap-4"
            initial={ { opacity: 0 } }
            animate={ { opacity: 1 } }
            transition={ { delay: 0.4 } }
          >
            <Button
              onClick={ this.handleReset }
              variant="primary"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              重试
            </Button>

            <Button
              onClick={ this.handleRefresh }
              variant="secondary"
              className="flex items-center gap-2"
            >
              刷新页面
            </Button>
          </motion.div>

          {showDetails && this.state.error && (
            <motion.details
              className="mt-8 max-w-2xl w-full"
              initial={ { opacity: 0 } }
              animate={ { opacity: 1 } }
              transition={ { delay: 0.5 } }
            >
              <summary className="cursor-pointer text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                查看错误详情
              </summary>
              <div className="mt-4 rounded-lg bg-gray-100 p-4 text-left dark:bg-gray-800">
                <h4 className="mb-2 text-red-600 font-medium dark:text-red-400">
                  错误信息：
                </h4>
                <pre className="mb-4 overflow-auto text-xs text-gray-700 dark:text-gray-300">
                  {this.state.error.message}
                </pre>

                {this.state.errorInfo && (
                  <>
                    <h4 className="mb-2 text-red-600 font-medium dark:text-red-400">
                      组件堆栈：
                    </h4>
                    <pre className="overflow-auto text-xs text-gray-700 dark:text-gray-300">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </>
                )}
              </div>
            </motion.details>
          )}
        </motion.div>
      )
    }

    return this.props.children
  }
}

/** 高阶组件：为组件添加错误边界 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>,
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary { ...errorBoundaryProps }>
      <Component { ...props } />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`

  return WrappedComponent
}

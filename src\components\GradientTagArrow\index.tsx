import type { CSSProperties, ReactNode } from 'react'

import arrow from '@/assets/image/tagArrow.webp'
import yellowTagArrow from '@/assets/image/yellowTagArrow.webp'
import classnames from 'clsx'
import styles from './styles.module.scss'

function InnerGradientTagArrow({
  style,
  className,
  children,
  type = 'purple',
  height = 32,
}: GradientTagArrowProps) {
  return (<div
    className={ classnames(styles.wrap, className) }
    style={ style }
  >
    <div
      className={ styles.button }
      style={ {
        background: type === 'purple'
          ? 'linear-gradient(to right, #8181F3, #B378DA)'
          : 'linear-gradient(90deg, #E59A56 0%, #EBB46E 100%)',
      } }>
      {children}
    </div>
    <img
      src={ type === 'purple'
        ? arrow
        : yellowTagArrow }
      alt=""
      style={ {
        height,
      } } />
  </div>)
}
InnerGradientTagArrow.displayName = 'GradientTagArrow'

export const GradientTagArrow = memo(InnerGradientTagArrow)

export type GradientTagArrowProps = {
  className?: string
  style?: CSSProperties
  children?: ReactNode
  height?: number

  type?: 'yellow' | 'purple'
}

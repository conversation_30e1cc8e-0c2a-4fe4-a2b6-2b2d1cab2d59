import { request } from "@/utils"


export class OAuthApi {

  /**
   * 获取微信二维码url
   */
  static async getWXQRCode(): Promise<{ authorizeUrl: string }> {
    return request.get(`/oauth/WECHAT_OPEN`)
  }

  /**
   * 登录微信
   */
  static async loginWX(data: OAuthParams): Promise<{ token: string }> {
    return request.post(`/oauth/WECHAT_OPEN`, data)
  }

}

type OAuthParams = {
  code: string
  state: string
}
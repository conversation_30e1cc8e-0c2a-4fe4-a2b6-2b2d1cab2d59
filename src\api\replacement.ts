import { TemplateImageDO } from "@/dts";
import { request } from "@/utils";

const prefix = "/ai/reference";
const prefixApp = "/app/resource";

export const API_REPLACEMENT = {
  // 背景替换
  async bgReplacement(params: FormData): Promise<{ resourceIds: string[] }> {
    return request.post(`${prefix}/replacement`, params);
  },
  // 获取背景库
  async getBgLib(): Promise<{ images: TemplateImageDO[] }> {
    return request.post(`${prefixApp}/bg-lib`);
  },
};

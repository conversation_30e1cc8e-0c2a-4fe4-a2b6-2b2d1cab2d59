import type { CSSProperties, FC } from 'react'
import classNames from 'clsx'

import styles from './index.module.less'

interface Props {
  className?: string
  style?: CSSProperties
  size?: number
}
const CardLoading: FC<Props> = ({ className, style, size = 10 }) => {
  const blockStyle: CSSProperties = {
    width: 3 * size,
    height: 3 * size,
  }

  const ballStyle: CSSProperties = {
    width: size,
    height: size,
  }

  const ladderStyle: CSSProperties = {
    width: size,
    height: 2,
  }

  return (
    <div className={ classNames(styles['loader-box'], className) } style={ style }>
      <div className={ styles['loader-block'] } style={ blockStyle }>
        <div style={ ballStyle }></div>
        <div style={ ladderStyle }></div>
        <div style={ ladderStyle }></div>
        <div style={ ladderStyle }></div>
      </div>
    </div>
  )
}

export default CardLoading

import type { MotionProps } from 'framer-motion'
import { PreviewImg } from '@/components/PreviewImg'
import { cn } from '@/utils'
import { motion } from 'framer-motion'
import { memo, useEffect, useRef, useState } from 'react'

const observerMap = new WeakMap<HTMLImageElement, { src: string }>()

const ob = new IntersectionObserver(
  (entries) => {
    entries.forEach((entry) => {
      if (!entry.isIntersecting)
        return

      const imgEl = entry.target as HTMLImageElement
      const data = observerMap.get(imgEl)

      if (data) {
        imgEl.src = data.src

        ob.unobserve(imgEl)
        observerMap.delete(imgEl)
      }
    })
  },
  {
    threshold: 0.01,
    rootMargin: '20px',
  },
)

export const LazyImg = memo<LazyImgProps>((
  {
    style,
    imgStyle,
    className,
    imgClassName,
    children,

    lazy = true,
    src,
    loadingSrc = new URL('@/assets/svg/loadingSvg.svg', import.meta.url).href,
    errorSrc = new URL('@/assets/image/image-error.webp', import.meta.url).href,

    errorText,
    loadingText = '',
    keepAspect = true,
    previewable = false,

    ...rest
  },
) => {
  const imgRef = useRef<HTMLImageElement>(null)
  const [previewVisible, setPreviewVisible] = useState(false)

  const [showLoading, setShowLoading] = useState(true)
  const [showError, setShowError] = useState(false)
  const [showImg, setShowImg] = useState(false)

  const handleLoad = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const imgEl = event.target as HTMLImageElement
    setShowLoading(false)
    setShowError(false)
    setShowImg(true)

    rest.onLoad?.(event)
  }

  const handleError = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    if (lazy && imgRef.current) {
      ob.unobserve(imgRef.current)
      observerMap.delete(imgRef.current)
    }
    setShowLoading(false)
    setShowError(true)
    setShowImg(false)
    imgRef.current!.style.display = 'none'

    rest.onError?.(event)
  }

  // --- 副作用 ---
  useEffect(() => {
    setShowLoading(true)
    setShowError(false)
    setShowImg(false)

    if (imgRef.current) {
      imgRef.current.style.filter = 'none'
      imgRef.current.style.transition = 'none'
    }

    const imgElement = imgRef.current
    if (!imgElement)
      return

    if (!src) {
      setShowError(true)
      setShowLoading(false)
      return
    }

    if (!lazy) {
      imgElement.src = src
    }
    else {
      imgElement.removeAttribute('src')

      observerMap.set(imgElement, { src })
      ob.observe(imgElement)
    }

    return () => {
      if (imgElement) {
        ob.unobserve(imgElement)
        observerMap.delete(imgElement)
      }
    }
  }, [src, lazy])

  return (<>
    <motion.div
      className={ cn(
        'lazy-img-container size-full relative overflow-hidden select-none',
        className,
      ) }
      layout={ rest.layout }
      layoutId={ rest.layoutId }
      style={ style }
    >
      <div
        className={ cn(`flex justify-center items-center
          w-full h-full relative overflow-hidden`, { 'aspect-padding': keepAspect }) }
        style={ {
          ...(keepAspect && {
            paddingBottom: keepAspect
              ? '100%'
              : undefined,
            height: keepAspect
              ? 0
              : '100%',
          }),
        } }
      >
        { showLoading && (
          <div className="absolute inset-0 z-5 flex flex-col items-center justify-center">
            <img
              src={ loadingSrc }
              alt="Loading..."
              decoding="async"
              className={ cn(
                'w-10 h-10 opacity-50',
              ) }
              style={ imgStyle }
              { ...rest }
            />
            { loadingText && (
              <span className="mt-1 text-xs text-gray-400">{ loadingText }</span>
            ) }
          </div>
        ) }

        { showError && (
          <div className="absolute inset-0 z-5 flex flex-col items-center justify-center text-center">
            <img
              src={ errorSrc }
              alt="Error"
              decoding="async"
              className={ cn(
                'w-12 h-12',
              ) }
              style={ imgStyle }
              { ...rest }
            />
            { errorText && (
              <span className="mt-1 px-2 text-xs text-red-400">{ errorText }</span>
            ) }
          </div>
        ) }

        <img
          ref={ imgRef }
          alt={ rest.alt }
          decoding="async"
          className={ cn(
            'absolute top-0 left-0 z-1 object-cover w-full h-full transition-all duration-300',
            { 'hover:scale-105': showImg },
            { 'cursor-zoom-in': previewable && showImg },
            imgClassName,
          ) }
          style={ imgStyle }
          onClick={ () => {
            if (previewable && showImg)
              console.log('previewable', previewable)
            setPreviewVisible(true)
          } }
          onLoad={ handleLoad }
          onError={ handleError }
          { ...rest }
        />

        { children }
      </div>
    </motion.div>

    { previewVisible && <PreviewImg
      src={ src }
      onClose={ () => setPreviewVisible(false) }
    /> }
  </>)
})

LazyImg.displayName = 'LazyImg'

export type LazyImgProps = {
  className?: string
  imgClassName?: string
  style?: React.CSSProperties
  imgStyle?: React.CSSProperties
  children?: React.ReactNode
  lazy?: boolean
  src: string
  loadingSrc?: string
  errorSrc?: string
  errorText?: string
  loadingText?: string
  keepAspect?: boolean
  previewable?: boolean
}
& Omit<React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>, 'src'>
& MotionProps

import type { CheckmarkProps } from './Checkmark'
import { useFormField } from '@/components/Form'
import { primaryColor } from '@/styles/variable'
import { cn } from '@/utils'
import { memo } from 'react'
import { Checkmark } from './Checkmark'

export const Checkbox = memo<CheckboxProps>((props) => {
  const {
    checked = false,
    onChange,
    disabled = false,
    className,
    size = 24,
    strokeWidth = 6,
    borderColor = primaryColor,
    checkedBackgroundColor = primaryColor,
    uncheckedBackgroundColor = 'transparent',
    checkmarkColor = '#fff',
    label,
    labelPosition = 'right',
    labelClassName,
    indeterminate = false,
    required = false,
    name,
    ...rest
  } = props

  const {
    actualValue,
    handleChangeVal,
    handleBlur,
  } = useFormField<boolean, React.MouseEvent | React.KeyboardEvent>({
    name,
    value: checked,
    defaultValue: false,
    onChange,
  })

  const isChecked = actualValue ?? checked

  const backgroundColor = checked
    ? checkedBackgroundColor
    : uncheckedBackgroundColor

  const handleClick = (e: React.MouseEvent) => {
    if (!disabled) {
      handleChangeVal(!isChecked, e)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!disabled && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault()
      handleChangeVal(!isChecked, e as unknown as React.MouseEvent)
    }
  }

  const checkboxElement = (
    <Checkmark
      size={ size }
      strokeWidth={ strokeWidth }
      borderColor={ borderColor }
      backgroundColor={ backgroundColor }
      checkmarkColor={ checkmarkColor }
      show={ isChecked || indeterminate }
      showCircle={ false }
      animationDuration={ 0.6 }
      className={ cn(
        disabled
          ? 'opacity-50 cursor-not-allowed'
          : '',
        className,
      ) }
      role="checkbox"
      aria-checked={ indeterminate
        ? 'mixed'
        : isChecked }
      aria-disabled={ disabled }
      aria-required={ required }
      tabIndex={ disabled
        ? -1
        : 0 }
      { ...rest }
      onClick={ handleClick }
      onKeyDown={ handleKeyDown }
      onBlur={ handleBlur }
    />
  )

  if (label) {
    return (
      <label
        className={ cn(
          'flex items-center gap-2',
          labelPosition === 'left'
            ? 'flex-row-reverse'
            : '',
          'cursor-pointer',
          disabled
            ? 'opacity-50 cursor-not-allowed'
            : '',
          labelClassName,
        ) }
        onClick={ e => !disabled && handleChangeVal(!isChecked, e) }
      >
        { checkboxElement }
        <span className={ cn(
          required
            ? 'before:content-["*"] before:mr-1 before:text-red-500'
            : '',
        ) }>
          { label }
        </span>
      </label>
    )
  }

  return checkboxElement
})

Checkbox.displayName = 'Checkbox'

export type CheckboxProps = {
  checked?: boolean
  checkedBackgroundColor?: string
  uncheckedBackgroundColor?: string

  onChange?: (checked: boolean, e: React.MouseEvent | React.KeyboardEvent) => void
  disabled?: boolean
  label?: React.ReactNode
  labelPosition?: 'left' | 'right'
  labelClassName?: string
  indeterminate?: boolean
  required?: boolean
  name?: string
}
& Omit<CheckmarkProps, 'show' | 'onChange' | 'disabled' | 'showCircle' | 'backgroundColor'>

import type { MarketStep1Params } from '@/api/MarketApi'
import type { TrendStep1Params } from '@/api/TrendApi'
import type { WorkflowStep } from '../../ChatV2/components/ChatWorkflow'

import type { MdToCodePreviewType, MessageStoreType, ReportStoreType, StateStoreType, StepStateType, TaskStoreType } from '../stores'
import type { ParsedStreamData } from '../stores/cozeStreamApi'
import { FileAPI } from '@/api/FileAPI'
import { AnimateShow } from '@/components/Animate/AnimateShow'
import { SelectableGradientCard } from '@/components/Card/SelectableGradientCard'
import { LoadingIcon } from '@/components/Loading/LoadingIcon'
import { useBindWinEvent } from '@/hooks'
import { cn, request } from '@/utils'
import classNames from 'clsx'
import { motion } from 'framer-motion'

import { ChevronDown, Sparkles } from 'lucide-react'
import { memo, useCallback, useEffect, useInsertionEffect, useMemo, useRef, useState } from 'react'
import TopBar from '../../ChatV2/components/TopBar'
import { ChatEventBus } from '../../ChatV2/constants'
import TrendSelectionPage from '../../TrendSelection/components/TrendSelectionPage'
import { DistributionEvent, eventBus } from '../constants'

import { trendAg } from '../stores'
import { addReportItem, createCardMessage, removeMessage } from '../stores/create'
import { ChatHistory } from './ChatHistory'
import ChatWorkflow from './ChatWorkflow'
import { MessageItem } from './MessageItem'
import { ReportPreview } from './ReportComponents/ReportPreview'
import { STREAM_IDS, streamingDataManager } from './StreamingDataManager'
import { StreamingThinkingStream } from './StreamingThinkingStream'
import { TextDisplayWithPagination } from './TextDisplayWithPagination'

/** Strategy卡片渲染组件 - 专门渲染Strategy卡片 */
const StrategyCardRenderer = memo<{
  messageStore: any
  stateStore: any
}>(({ messageStore, stateStore }) => {
  const { messages } = messageStore.useAndDispose()

  /** 找到Strategy卡片 */
  const strategyCard = useMemo(() => {
    return messages.find((msg: any) => msg.meta?.isStrategyCard && msg.type === 'card')
  }, [messages])

  if (!strategyCard) {
    return null
  }

  return (
    <motion.div
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.5 } }
      className="mt-4"
    >
      <MessageItem
        message={ strategyCard }
        onDelete={ () => {} }
        className="w-full"
        stateStore={ stateStore }
      />
    </motion.div>
  )
})

StrategyCardRenderer.displayName = 'StrategyCardRenderer'

/** Implementation卡片渲染组件 - 专门渲染Implementation卡片 */
const ImplementationCardRenderer = memo<{
  messageStore: any
  stateStore: any
  className?: string
}>(({ messageStore, stateStore, className }) => {
  const { messages } = messageStore.useAndDispose()

  /** 找到Implementation卡片 */
  const implementationCard = useMemo(() => {
    return messages.find((msg: any) => msg.meta?.isImplementationCard && msg.type === 'card')
  }, [messages])

  if (!implementationCard) {
    return null
  }

  return (
    <motion.div
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.5 } }
      className={ cn('mt-4', className) }
    >
      <MessageItem
        message={ implementationCard }
        onDelete={ () => {} }
        className="w-full"
        stateStore={ stateStore }
      />
    </motion.div>
  )
})

ImplementationCardRenderer.displayName = 'ImplementationCardRenderer'

/** OriginalWork卡片渲染组件 - 使用 SelectableGradientCard 组件 */
const OriginalWorkCardRenderer = memo<{
  messageStore: any
  stateStore: any
  className?: string
}>(({ messageStore, stateStore, className }) => {
  const { messages } = messageStore.useAndDispose()

  // 创建一个虚拟的卡片数据用于渲染（不添加到 messages 中）
  const originalWorkCard = useMemo(() => {
    // 先查找已存在的卡片
    const existingCard = messages.find((msg: any) => msg.meta?.cardId === 'original-work-report' && msg.type === 'card')
    if (existingCard) {
      return existingCard
    }
    
    // 如果没有，创建一个虚拟卡片用于渲染
    return {
      id: 'original-work-virtual-card',
      type: 'card',
      meta: {
        cardId: 'original-work-report',
        isVirtual: true
      },
      originalWorkData: stateStore.originalWorkData || {}
    }
  }, [messages, stateStore.originalWorkData])

  // 处理卡片点击事件 - 必须在所有条件判断之前定义
  const handleCardClick = useCallback(() => {
    // 点击卡片时，先将卡片中的数据同步到 stateStore
    if (originalWorkCard && originalWorkCard.originalWorkData) {
      stateStore.originalWorkData = { ...originalWorkCard.originalWorkData }
      console.log('[Trend Report Card] 从卡片恢复数据:', stateStore.originalWorkData)
    }
    // 显示 ImagePreviewCard
    ChatEventBus.emit('showImagePreview', { isUserAction: true })
  }, [originalWorkCard, stateStore])

  // 获取图标组件
  const getIconComponent = (iconName: string) => {
    if (iconName === 'creativeDirector') {
      return (
        <img 
          src={ new URL('@/assets/image/home/<USER>', import.meta.url).href }
          alt="Creative Director"
          className="w-10 h-10 object-contain"
        />
      )
    }
    if (iconName === 'rightArrow') {
      return (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    }
    return null
  }

  return (
    <motion.div
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.5 } }
      className={ className }
    >
      <SelectableGradientCard
        selected={ false }
        onSelectedChange={ handleCardClick }
        onClick={ handleCardClick }
        cardConfig={{
          leftIcon: {
            show: true,
            icon: getIconComponent('creativeDirector'),
            size: 'lg'
          },
          rightIcon: {
            show: true,
            icon: getIconComponent('rightArrow'),
            size: 'md',
            className: 'text-gray-400'
          },
          content: {
            title: originalWorkCard.card?.title || 'The Trend Report',
            description: originalWorkCard.card?.description || 'Content creation based on your selection'
          },
          // 不显示按钮
          layout: 'simple'
        }}
        className="cursor-pointer transition-transform hover:scale-[1.02]"
      />
    </motion.div>
  )
})

OriginalWorkCardRenderer.displayName = 'OriginalWorkCardRenderer'

/** OperationsManager卡片渲染组件 - 渲染静态OperationsManager卡片 */
const OperationsManagerCardRenderer = memo<{
  stateStore: any
  className?: string
}>(({ stateStore, className }) => {
  /** 创建静态的OperationsManager卡片数据，不添加到消息流中 */

  const currentLeftIcon = 'creativeDirector' // 使用 Research Analyst 图标

  const operationsManagerCard = useMemo(() => {
    return {
      id: 'operations-manager-static-card',
      type: 'card' as const,
      timestamp: Date.now(),
      sender: 'assistant' as const,
      content: '',
      meta: {
        cardId: 'operations-manager-card',
        isOperationsManagerCard: true,
        step: 'step1' as const,
      },
      card: {
        title: 'Operations Manager',
        description: '正在生成运营管理详情，包含策略执行、资源协调和进度监控...',
        variant: 'success' as const,
        onClick: () => {
          console.warn('[OperationsManagerCard] 点击运营管理卡片 - 显示详细信息')

          /** 显示相关页面 */
          stateStore.showTrendSelection = true
          stateStore.isReportOpen = true

          console.warn('[OperationsManagerCard] 已设置相关显示状态')
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon3', // 使用自定义 SVG 图标
            size: 'md' as const,
          },
          content: {
            title: 'The Trend Report',
            description: 'Review the trend report based on your choice',
          },
          socialPost: {
            image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
            author: {
              name: 'Milla',
              avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31aje2ouc6q0049042ub7u510c0c06r0?imageView2/2/w/120/format/webp|imageMogr2/strip',
            },
          },
          layout: 'social' as const,
          button: {
            text: 'View Operations',
            variant: 'gradient-border' as const,
            position: 'bottom-left' as const,
            disabled: false,
            onClick: () => {
              console.warn('[OperationsManager Button] 查看运营详情按钮被点击')

              /** 显示相关页面 */
              stateStore.showTrendSelection = true
              stateStore.isReportOpen = true

              console.warn('[OperationsManager Button] 已设置显示状态')
            },
          },
        },
      },
    }
  }, [stateStore])

  return (
    <motion.div
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.5 } }
      className={ cn('mt-4', className) }
    >
      <MessageItem
        message={ operationsManagerCard }
        onDelete={ () => {} }
        className="w-full"
        stateStore={ stateStore }
      />
    </motion.div>
  )
})

OperationsManagerCardRenderer.displayName = 'OperationsManagerCardRenderer'

/** Planning Scheme专用的ThinkingStream组件 */
const PlanningThinkingStream = memo<{
  content: string
  isActive: boolean
}>(({ content, isActive }) => {
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const [displayedText, setDisplayedText] = useState('')
  const [isTyping, setIsTyping] = useState(false)

  useEffect(() => {
    if (!content || !isActive)
      return

    setIsTyping(true)
    setDisplayedText('')

    let currentIndex = 0
    const typeInterval = setInterval(() => {
      if (currentIndex < content.length) {
        setDisplayedText(content.slice(0, currentIndex + 1))
        currentIndex++
      }
      else {
        setIsTyping(false)
        clearInterval(typeInterval)
      }
    }, 20)

    return () => clearInterval(typeInterval)
  }, [content, isActive])

  if (!isActive || !content)
    return null

  return (
    <motion.div
      initial={ { opacity: 0, y: 10 } }
      animate={ { opacity: 1, y: 0 } }
      className="border border-gray-200 rounded-lg bg-white p-4 shadow-sm"
    >
      <div
        className="flex cursor-pointer items-center justify-between"
        onClick={ () => setThinkingExpanded(!thinkingExpanded) }
      >
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-blue-500" />
          <span className="text-sm text-gray-700 font-medium">Planning Analysis</span>
          {isTyping && <LoadingIcon className="h-3 w-3" />}
        </div>
        <ChevronDown
          className={ `h-4 w-4 text-gray-400 transition-transform ${
            thinkingExpanded
              ? 'rotate-180'
              : ''
          }` }
        />
      </div>

      <AnimateShow show={ thinkingExpanded }>
        <div className="mt-3 whitespace-pre-wrap text-sm text-gray-600">
          {displayedText}
          {isTyping && <span className="animate-pulse">|</span>}
        </div>
      </AnimateShow>
    </motion.div>
  )
})

PlanningThinkingStream.displayName = 'PlanningThinkingStream'

export const ChatPage = memo<ChatPageProps>((
  {
    style,
    className,
    taskStore,
    stateStore,
    messageStore,
    mdToCodePreview,
    resetDistributionStore: _resetDistributionStore,
    reportStore,
    stepState,

    // TopBar 相关 props
    showTopBar = false,
    topBarAgents = [],
    onTopBarAgentClick,
    topBarDropdownExpanded = false,
    onTopBarDropdownToggle,

    // ChatV2 流程相关 props
    onStartAIAnalysis,
  },
) => {
  // @ts-ignore - 添加 collectedFormData 和 thinkingContent 到监听列表
  const { formData, isReportOpen, chatV2FlowMode, userDescription, uploadedImage, collectedFormData, thinkingContent } = stateStore.use()
  taskStore.use() // 保持响应式连接

  const chatHistoryRef = useRef<{ scrollToBottom: () => void }>(null)
  const stateStoreRef = useRef(stateStore)
  const taskStoreRef = useRef(taskStore)

  /** 更新 refs */
  stateStoreRef.current = stateStore
  taskStoreRef.current = taskStore

  // ChatV2 流程状态管理
  const [isThinking, setIsThinking] = useState(false)
  const [showForm, setShowForm] = useState(true)
  const [showWorkflow, setShowWorkflow] = useState(false)
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const [isAnalysisStarted, setIsAnalysisStarted] = useState(false) // 控制是否已开始分析
  const [isButtonLoading, setIsButtonLoading] = useState(false) // 控制Continue to Strategy按钮的loading状态
  const [isButtonDisabled, setIsButtonDisabled] = useState(false) // 控制Continue to Strategy按钮的disabled状态
  const [cardsCreated, setCardsCreated] = useState(false) // 防重复创建标志
  const [competitiveReportItemId, setCompetitiveReportItemId] = useState<string>('') // 存储竞争对手分析报告项ID
  const [thinkingData, setThinkingData] = useState<string>('') // 存储从insight_report获取的thinking数据
  const [isThinkingDataReady, setIsThinkingDataReady] = useState(false) // 控制按钮是否可用（基于thinking数据是否准备好）
  const [showStrategyCard, setShowStrategyCard] = useState(false) // 控制Strategy卡片的显示

  /** 工作流完成状态追踪 */
  const [dataReportCompleted, setDataReportCompleted] = useState(false) // data_report 工作流完成状态
  const [insightReportCompleted, setInsightReportCompleted] = useState(false) // insight_report 工作流完成状态
  const [competitorReportCompleted, setCompetitorReportCompleted] = useState(false) // competitor_report 工作流完成状态
  const [planningSchemeCompleted, setPlanningSchemeCompleted] = useState(false) // planning_scheme 工作流完成状态
  const [hasApprovedStrategy, setHasApprovedStrategy] = useState(false) // 是否已经点击过 Approve Strategy
  const [strategyReportItemId, setStrategyReportItemId] = useState<string>('') // 存储Strategy报告项ID
  const [isStrategyApproved, setIsStrategyApproved] = useState(false) // 控制Strategy是否已批准
  const [displayText, setDisplayText] = useState<string>('') // 存储要在文本显示组件中展示的内容
  const [secondaryDisplayText, setSecondaryDisplayText] = useState<string>('') // 存储第二个文本显示组件的内容
  const [planningThinkingData, setPlanningThinkingData] = useState<string>('') // 存储Planning Scheme的thinking数据
  const [showPlanningThinking, setShowPlanningThinking] = useState(false) // 控制Planning Scheme ThinkingStream的显示
  const [isContentCreationDisabled, setIsContentCreationDisabled] = useState(false) // 控制 Start content creation 按钮的禁用状态
  const [isContentCreationLoading, setIsContentCreationLoading] = useState(false) // 控制 Start content creation 按钮的加载状态
  const [showImplementationCard, setShowImplementationCard] = useState(false) // 控制Implementation卡片的显示
  const [showAnalysisResults, setShowAnalysisResults] = useState(false) // 新增：控制是否显示分析结果
  const [thinkingMessages, setThinkingMessages] = useState<Array<{ id: string, text: string }>>([]) // 存储thinking消息
  const [hasThinkingCompleted, setHasThinkingCompleted] = useState(false) // 跟踪thinking是否已完成
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([]) // 存储工作流步骤
  const [isLoadingSteps, setIsLoadingSteps] = useState(false) // 工作流步骤加载状态
  const [hasCalledReportSync, setHasCalledReportSync] = useState(false) // 防止重复调用 updateBothCardsReportSync

  /** OperationsManager 相关状态变量 */
  const [operationsManagerDisplayText, setOperationsManagerDisplayText] = useState<string>('') // OperationsManager文本显示内容
  const [showOperationsManagerComponents, setShowOperationsManagerComponents] = useState(false) // 控制OperationsManager组件显示
  const [showOperationsManagerCard, setShowOperationsManagerCard] = useState(false) // 控制OperationsManager卡片显示
  const [operationsManagerThinkingData, setOperationsManagerThinkingData] = useState<string>('') // OperationsManager专用thinking数据
  const [isOperationsManagerActive, setIsOperationsManagerActive] = useState(false) // OperationsManager激活状态

  /** 防止 original_work 重复触发的 ref */
  const hasTriggeredOriginalWork = useRef<boolean>(false)

  /** 防止各个工作流重复触发的 refs */
  const hasTriggeredInsightReport = useRef<boolean>(false)
  const hasTriggeredCompetitorReport = useRef<boolean>(false)
  const hasTriggeredPlanningScheme = useRef<boolean>(false)
  const hasTriggeredHotpotsRef = useRef<boolean>(false)
  const hasTriggeredDistillRef = useRef<boolean>(false)

  /** hotpots_analysis 完成状态 */
  const [isHotpotsAnalysisComplete, setIsHotpotsAnalysisComplete] = useState(false)

  /** 监听 hotpots_analysis 数据变化来判断完成状态 */
  useEffect(() => {
    if (stateStore.hotpotsTopics && Object.keys(stateStore.hotpotsTopics).length > 0) {
      // 检查是否有至少一个 topic 数据
      const hasTopicData = ['topic1', 'topic2', 'topic3', 'topic4', 'topic5'].some(
        key => stateStore.hotpotsTopics[key]
      )
      if (hasTopicData) {
        console.log('[ChatPage] 检测到 hotpots_analysis 数据，设置完成状态')
        setIsHotpotsAnalysisComplete(true)
      }
    }
  }, [stateStore.hotpotsTopics])

  /** 表单状态管理 */
  const [formDataLocal, setFormDataLocal] = useState<Partial<MarketStep1Params>>({
    brand: '',
    product_name: '',
    industry: '',
    competitor: '',
    product: '', // 独立的表单字段，不使用 userDescription
    pic: '', // 独立的表单字段，不使用 uploadedImage
    industry_id: 83, // 默认值
    ip: '', // 默认值
    role: '', // 默认值
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isUploadingImage, setIsUploadingImage] = useState(false) // 图片上传状态

  /** 监听ButtonStateManager的状态变化，同步按钮状态 */
  useEffect(() => {
    const handleButtonStateChange = (event: CustomEvent) => {
      const { loading, disabled } = event.detail
      setIsButtonLoading(loading)
      setIsButtonDisabled(disabled)
    }

    window.addEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)

    return () => {
      window.removeEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)
    }
  }, [])

  /** 监听表单数据变化，确保数据更新 */
  useEffect(() => {
    console.log('🔄 formDataLocal 变化:', formDataLocal)
    if (formDataLocal.brand || formDataLocal.product_name || formDataLocal.industry) {
      console.log('✅ 表单数据已更新：', {
        brand: formDataLocal.brand,
        product_name: formDataLocal.product_name,
        industry: formDataLocal.industry,
        competitor: formDataLocal.competitor,
        product: formDataLocal.product,
      })
    }
  }, [formDataLocal])

  /** 监听 planning_scheme 完成状态，动态更新 Approve Strategy 按钮 */
  useEffect(() => {
    if (planningSchemeCompleted) {
      console.warn('[ChatPage] planning_scheme 已完成，更新 Approve Strategy 按钮状态')

      /** 查找 planning-scheme-report 卡片 */
      const planningCard = trendAg.messageStore.messages.find(msg => msg.meta?.cardId === 'planning-scheme-report')

      if (planningCard && planningCard.card && planningCard.card.cardConfig?.button) {
        /** 更新按钮文本和状态 */
        planningCard.card.cardConfig.button.text = 'Approve Strategy'
        planningCard.card.cardConfig.button.variant = 'gradient-border' as const
        planningCard.card.cardConfig.button.disabled = hasApprovedStrategy // 只有在已经点击过时才禁用

        console.warn('[ChatPage] 已更新 Approve Strategy 按钮为可点击')

        /** 强制触发重新渲染 */
        trendAg.messageStore.messages = [...trendAg.messageStore.messages]
      }
      else {
        console.warn('[ChatPage] 未找到 planning-scheme-report 卡片')
      }
    }
  }, [planningSchemeCompleted, hasApprovedStrategy])

  /** 监听 hasApprovedStrategy 状态，更新按钮状态 */
  useEffect(() => {
    if (hasApprovedStrategy) {
      console.warn('[ChatPage] 已点击 Approve Strategy，更新按钮状态')

      /** 查找 planning-scheme-report 卡片 */
      const planningCard = trendAg.messageStore.messages.find(msg => msg.meta?.cardId === 'planning-scheme-report')

      if (planningCard && planningCard.card) {
        /** 卡片点击仍然显示 planning_scheme 报告（不改变） */

        /** 更新按钮状态为已批准 */
        if (planningCard.card.cardConfig?.button) {
          planningCard.card.cardConfig.button.text = 'Approved'
          planningCard.card.cardConfig.button.variant = 'success' as const
          planningCard.card.cardConfig.button.disabled = true
        }

        console.warn('[ChatPage] 已更新按钮为 Approved 状态')

        /** 强制触发重新渲染 */
        trendAg.messageStore.messages = [...trendAg.messageStore.messages]
      }
    }
  }, [hasApprovedStrategy, stateStore])

  /** 处理 thinking 渲染完成 */
  const handleThinkingComplete = useCallback(() => {
    /** 避免重复触发 */
    if (hasThinkingCompleted) {
      console.log('⚠️ Thinking 已经完成，避免重复触发')
      return
    }

    console.log('✅ Thinking 内容渲染完成')
    setHasThinkingCompleted(true)

    /** 关闭 loading 状态 */
    if (isThinking) {
      setIsThinking(false)
      console.log('🔄 已关闭 loading 状态')
    }

    /** 收起 thinking 内容 */
    setThinkingExpanded(false)
    console.log('📦 已收起 thinking 内容')

    /** 短暂延迟后显示表单 */
    setTimeout(() => {
      /** 确保数据已经回填 */
      // @ts-ignore
      if (stateStore.collectedFormData) {
        console.log('📝 确认表单数据已准备好:', stateStore.collectedFormData)
      }

      setShowForm(true)
      stateStore.chatV2FlowMode = 'form'
      console.log('📋 表单已显示')

      /** 清除收集的数据，避免重复回填 */
      setTimeout(() => {
        // @ts-ignore
        if (stateStore.collectedFormData) {
          console.log('🧹 清除已使用的表单数据')
          // @ts-ignore
          stateStore.collectedFormData = null
        }
      }, 1000) // 表单显示1秒后清除数据
    }, 1000) // 1秒延迟，让用户看到渲染完成
  }, [isThinking, stateStore, hasThinkingCompleted])

  /** 监听 store 中的 thinking 内容并更新 */
  useEffect(() => {
    // @ts-ignore
    const thinkingData = stateStore?.thinkingContent || thinkingContent

    // console.log('🔍 ChatPage thinking数据检查:', {
    //   hasThinkingContent: !!thinkingContent,
    //   hasStoreThinkingContent: !!stateStore?.thinkingContent,
    //   thinkingDataLength: thinkingData
    //     ? thinkingData.length
    //     : 0,
    //   isThinking,
    // })

    if (thinkingData) {
      // console.log('📝 ChatPage 接收到 thinking 内容:', thinkingData.substring(0, 100))
      /** 将 thinking 内容设置为消息 */
      setThinkingMessages([{
        id: 'thinking-content',
        text: thinkingData,
      }])

      /** 不再立即解除 loading，等待渲染完成回调 */
      if (chatV2FlowMode === 'thinking') {
        console.log('⏳ 等待 thinking 内容渲染完成...')
      }
    }
    // @ts-ignore
    else if (stateStore?.sseStreamMessages) {
      /** 如果没有 thinking 内容，使用 SSE 消息作为备份 */
      const sseMessages = stateStore.sseStreamMessages
      console.log('📨 使用SSE消息作为备份:', sseMessages?.length, '条')
      if (sseMessages && Array.isArray(sseMessages) && sseMessages.length > 0) {
        const messages = sseMessages.map((msg, index) => ({
          id: `msg-${index}`,
          text: msg.content || msg.text || 'Processing...',
        }))
        setThinkingMessages(messages)
      }
    }
    else if (chatV2FlowMode === 'thinking') {
      console.log('⏳ Thinking模式但没有数据，保持loading...')
      /** 保持loading状态 */
    }
  }, [stateStore, thinkingContent, isThinking, chatV2FlowMode])

  /** 监听 store 中的收集数据并更新表单 */
  useEffect(() => {
    if (collectedFormData && typeof collectedFormData === 'object') {
      console.log('📋 ChatPage 接收到收集的数据，准备回填:', collectedFormData)

      /** 直接更新表单数据 */
      setFormDataLocal((prev) => {
        const updated = {
          ...prev,
          brand: collectedFormData.brand_name || prev.brand,
          product_name: collectedFormData.product_name || prev.product_name,
          industry: collectedFormData.industry_name || prev.industry,
          competitor: collectedFormData.competitor_name || prev.competitor,
          /** 如果需要，可以添加其他字段的映射 */
        }
        console.log('📝 更新后的表单数据:', updated)
        return updated
      })

      console.log('✅ Action plan 表单数据已回填')

      /** 不要立即清除数据，等待表单显示后再清除 */
      /** 移除了 setTimeout 清除数据的逻辑 */
    }
  }, [collectedFormData, stateStore])

  /** 监听 userDescription 和 uploadedImage 并更新到表单的 product 和 pic 字段 */
  useEffect(() => {
    if (userDescription || uploadedImage) {
      console.log('📝 接收到用户输入，准备回填到表单:', { userDescription, uploadedImage })

      setFormDataLocal((prev) => {
        const updated = {
          ...prev,
          product: userDescription || prev.product, // 回填到 Product description 字段
          pic: uploadedImage || prev.pic, // 回填到 Upload 字段
        }
        console.log('✅ Product description 和 Upload 字段已回填:', updated)

        /** 同时更新 userSubmitPic */
        if (updated.pic) {
          stateStore.userSubmitPic = updated.pic
          console.log('💾 更新 userSubmitPic:', updated.pic)
        }

        return updated
      })
    }
  }, [userDescription, uploadedImage])

  /** 监听 startOriginalWork 事件 */
  useEffect(() => {
    const handleStartOriginalWork = async () => {
      console.log('[ChatPage] Received startOriginalWork event')

      /** 防止重复触发 original_work */
      if (hasTriggeredOriginalWork.current) {
        console.warn('[original_work] 工作流已经触发过，跳过重复请求')
        /** 只触发显示预览事件（非用户主动点击） */
        ChatEventBus.emit('showImagePreview', { isUserAction: false })
        return
      }

      /** 标记已触发，防止重复调用 */
      hasTriggeredOriginalWork.current = true
      console.log('[original_work] 标记 hasTriggeredOriginalWork = true')

      /** 触发预览事件并设置 loading 状态（非用户主动点击） */
      ChatEventBus.emit('showImagePreview', { isUserAction: false })

      /** 设置 loading 开始 */
      stateStore.originalWorkLoading = true
      stateStore.originalWorkProgress = {} // 重置进度
      console.log('[original_work] 设置 loading 状态为 true')

      /** 调用 original_work 工作流 */
      try {
        /** 导入 callOriginalWorkAPI 函数 */
        const { callOriginalWorkAPI } = await import('../stores/cozeStreamApi')

        /** 获取动态数据 */
        const planningReport = stateStore.planningReportContent || ''

        /** 合并 hotpots_analysis 的 topics 数据 */
        const hotpotsTopics = stateStore.hotpotsTopics
        console.log('🔍 hotpotsTopics 原始数据:', hotpotsTopics)

        const anyTopic = [
          hotpotsTopics?.topic1,
          hotpotsTopics?.topic2,
          hotpotsTopics?.topic3,
          hotpotsTopics?.topic4,
          hotpotsTopics?.topic5,
          hotpotsTopics?.topic1_detail,
          hotpotsTopics?.topic2_detail,
          hotpotsTopics?.topic3_detail,
          hotpotsTopics?.topic4_detail,
          hotpotsTopics?.topic5_detail,
        ].filter(t => t).join('\n\n') || ''

        console.log('🔍 合并后的 anyTopic 长度:', anyTopic.length)
        const parameters = {
          planning_report: planningReport,
          any_topic: anyTopic,
          user_submit_pic: stateStore.userSubmitPic || '',
        }

        console.log('🚀 开始调用 original_work 工作流...')

        /** 存储响应数据 */
        const originalWorkData: Record<string, any> = {}
        const tagsList: string[] = [] // 存储所有的 tags

        /** 调用 original_work 工作流，传递正确的参数 */
        await callOriginalWorkAPI(
          (data) => {
            /** 处理流式数据 */
            if (data.nodeTitle && data.content) {
              console.log(`[original_work] 收到节点数据 ${data.nodeTitle}:`, data.content.substring(0, 100))

              /** 标记该节点已接收到数据 */
              stateStore.originalWorkProgress[data.nodeTitle] = true

              /** 特殊处理 tag 节点，累积而不是覆盖 */
              if (data.nodeTitle === 'tag') {
                tagsList.push(data.content)
                originalWorkData[data.nodeTitle] = tagsList.join(', ')
              }
              else {
                originalWorkData[data.nodeTitle] = data.content
              }

              /** 存储到 state store */
              if (!stateStore.originalWorkData) {
                stateStore.originalWorkData = {}
              }

              if (data.nodeTitle === 'tag') {
                /** 存储累积的 tags */
                stateStore.originalWorkData[data.nodeTitle] = tagsList.join(', ')
              }
              else {
                stateStore.originalWorkData[data.nodeTitle] = data.content
              }
            }
          },
          (error) => {
            console.error('[original_work] 工作流执行失败:', error)
            stateStore.originalWorkLoading = false
          },
          () => {
            console.log('[original_work] 工作流执行完成')
            console.log('[original_work] 收集到的数据:', originalWorkData)

            /** 设置 loading 结束 */
            stateStore.originalWorkLoading = false
            console.log('[original_work] 设置 loading 状态为 false')

            /** 存储完整数据到 stateStore */
            stateStore.originalWorkData = originalWorkData
          },
          parameters,
        )
      }
      catch (error) {
        console.error('[original_work] 工作流执行失败:', error)
        stateStore.originalWorkLoading = false
      }
    }

    /** 添加事件监听 */
    ChatEventBus.on('startOriginalWork', handleStartOriginalWork)

    /** 清理函数 */
    return () => {
      ChatEventBus.off('startOriginalWork', handleStartOriginalWork)
    }
  }, [stateStore])

  /** 处理 ChatV2 流程状态变化 */
  useEffect(() => {
    if (chatV2FlowMode === 'thinking') {
      /** 启动 Thinking 流程 */
      setIsThinking(true)
      setShowForm(true) // 确保表单隐藏
      setHasThinkingCompleted(false) // 重置完成状态

      /** 检查是否已经有thinking数据 */
      // @ts-ignore
      if (thinkingContent || stateStore?.thinkingContent) {
        console.log('✅ 已有thinking数据，等待渲染完成')
        /** 不要在这里设置任何状态，让 onThinkingComplete 处理 */
      }
      else {
        console.log('⏳ 等待thinking数据...')
        /**
         * 如果没有thinking数据，保持loading状态
         * 等待下一次effect触发（当thinking数据到达时）
         */
      }
    }
    else if (chatV2FlowMode === 'form') {
      /** 不要在这里直接显示表单，等待 thinking 渲染完成 */
      console.log('📋 Form 模式激活，但表单显示由 thinking 完成控制')
    }
    else if (chatV2FlowMode === 'workflow') {
      setShowForm(true)
      setShowWorkflow(true)
    }
  }, [chatV2FlowMode, stateStore, thinkingContent])

  /** 单独处理 text_link_disassemble 工作流 */
  useEffect(() => {
    if (chatV2FlowMode === 'text_link_disassemble') {
      /** 处理 text_link_disassemble 工作流模式 */
      console.log('🔄 ChatV2 flow mode is text_link_disassemble, starting workflow...')
      console.log('📋 Store data:', {
        taskInstanceId: stateStore.taskInstanceId,
        userDescription: stateStore.userDescription,
        detectedIntent: stateStore.detectedIntent,
      })
      setIsThinking(true)
      setThinkingExpanded(true) // 展开 thinking 内容
      setShowForm(true)
      setIsLoadingSteps(false)

      /** 直接执行 text_link_disassemble 工作流 */
      handleTextLinkDisassembleWorkflow()
    }
  }, [chatV2FlowMode]) // 只依赖于 chatV2FlowMode

  /** 当分析开始时，自动滚动到底部 */
  useEffect(() => {
    if (isAnalysisStarted) {
      /** 使用 requestAnimationFrame 确保 DOM 已更新 */
      requestAnimationFrame(() => {
        /** 第一次滚动：确保容器可见 */
        const container = document.querySelector('.ChatPageContainer .overflow-auto')
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }

        /** 延迟后再次滚动：确保内容加载完成 */
        setTimeout(() => {
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth',
            })
          }
          /** ChatHistory 内部滚动 */
          chatHistoryRef.current?.scrollToBottom?.()
        }, 300)
      })
    }
  }, [isAnalysisStarted])

  /** 表单处理函数 */
  const handleFormChange = (field: keyof MarketStep1Params, value: string) => {
    setFormDataLocal(prev => ({ ...prev, [field]: value }))
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  /** 处理 text_link_disassemble 工作流 */
  const handleTextLinkDisassembleWorkflow = async () => {
    console.log('🚀 handleTextLinkDisassembleWorkflow called!')
    console.log('📊 Current store state:', {
      taskInstanceId: stateStore.taskInstanceId,
      userDescription: stateStore.userDescription,
      detectedIntent: stateStore.detectedIntent,
    })
    console.log('📊 LocalStorage state:', {
      taskInstanceId: localStorage.getItem('taskInstanceId'),
      userMessage: localStorage.getItem('userMessage'),
      detectedIntent: localStorage.getItem('detectedIntent'),
    })

    try {
      const taskInstanceId = stateStore.taskInstanceId || localStorage.getItem('taskInstanceId')
      const userMessage = stateStore.userDescription || localStorage.getItem('userMessage')

      if (!taskInstanceId || !userMessage) {
        console.error('❌ Missing required data for text_link_disassemble workflow:', {
          taskInstanceId,
          userMessage,
        })
        console.error('Missing required data. Please try again.')
        setIsThinking(false)
        return
      }

      console.log('✅ Workflow parameters ready:', { taskInstanceId, userMessage })

      /** 保存 taskInstanceId 到 localStorage，供后续 confirm-form 使用 */
      localStorage.setItem('text_link_taskInstanceId', taskInstanceId)
      console.log('💾 Saved text_link_taskInstanceId to localStorage:', taskInstanceId)

      /** 准备 SSE 请求参数 */
      const sseParams = {
        taskInstanceId,
        platform: 'rednote',
        workflowName: 'text_link_disassemble',
        parameters: {
          user_submit: userMessage,
        },
      }

      console.log('📤 Sending SSE request with params:', sseParams)

      /** 调用 SSE 流式接口 */
      const { userStore } = await import('@/store/userStore')
      const token = userStore.token
      console.log('🔑 Using token:', token
        ? 'Token exists'
        : 'No token!')

      const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(sseParams),
      })

      console.log('📥 SSE Response status:', sseResponse.status, sseResponse.statusText)

      if (!sseResponse.ok) {
        throw new Error(`SSE request failed: ${sseResponse.status} ${sseResponse.statusText}`)
      }

      /** 处理 SSE 流式数据 */
      const reader = sseResponse.body?.getReader()
      const decoder = new TextDecoder()

      console.log('📖 Starting to read SSE stream...')

      if (reader) {
        let buffer = ''
        let thinkingContent = ''
        let formData: any = {}
        let chunkCount = 0

        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            console.log('✅ Stream reading completed, total chunks:', chunkCount)
            break
          }

          const chunk = decoder.decode(value, { stream: true })
          chunkCount++
          console.log(`📦 Chunk #${chunkCount} received, length: ${chunk.length}`)

          buffer += chunk
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.trim() === '')
              continue

            if (line.startsWith('data:')) {
              const data = line.slice(5).trim()

              if (data === '[DONE]') {
                console.log('✅ text_link_disassemble workflow completed')
                break
              }

              try {
                const jsonData = JSON.parse(data)

                /** 收集 thinking 内容并实时更新 */
                if (jsonData.node_title === 'thinking' && jsonData.content) {
                  thinkingContent += jsonData.content
                  console.log('💭 Thinking content received:', jsonData.content.substring(0, 100))

                  /** 实时更新 thinking 消息，触发流式渲染 */
                  setThinkingMessages([{
                    id: 'thinking-content',
                    text: thinkingContent,
                  }])

                  /** 确保 isThinking 状态为 true */
                  setIsThinking(true)
                  setShowForm(true)
                }

                /** 收集表单数据 - 根据实际的 node_title 处理 */
                if (jsonData.node_title && jsonData.content !== undefined && jsonData.content !== '') {
                  console.log('🔍 接收到节点:', jsonData.node_title, '内容:', jsonData.content)

                  /** 直接根据 node_title 映射到表单字段 */
                  switch (jsonData.node_title) {
                    case 'brand_name':
                      formData.brand = jsonData.content
                      console.log('📋 设置 brand:', jsonData.content)
                      break
                    case 'product_name':
                      formData.product_name = jsonData.content
                      console.log('📋 设置 product_name:', jsonData.content)
                      break
                    case 'industry_name':
                      formData.industry = jsonData.content
                      console.log('📋 设置 industry:', jsonData.content)
                      break
                    case 'competitor_name':
                      formData.competitor = jsonData.content
                      console.log('📋 设置 competitor:', jsonData.content)
                      break
                    case 'industry_id':
                      formData.industry_id = jsonData.content
                      console.log('📋 设置 industry_id:', jsonData.content)
                      break
                    case 'platform':
                      formData.platform = jsonData.content
                      console.log('📋 设置 platform:', jsonData.content)
                      break
                    case 'inputText':
                      formData.product = jsonData.content
                      console.log('📋 设置 product (从 inputText):', jsonData.content)
                      break
                    case 'product':
                      formData.product = jsonData.content
                      console.log('📋 设置 product:', jsonData.content)
                      break
                    default:
                      /** 其他节点不处理 */
                      break
                  }
                }
              }
              catch (e) {
                console.error('Failed to parse SSE data:', e)
              }
            }
          }
        }

        /** 工作流完成后，等待 thinking 渲染完成 */
        console.log('✅ text_link_disassemble 流完成，thinking 内容长度:', thinkingContent.length)
        console.log('📋 最终收集到的表单数据:', formData)

        /** 更新表单数据 */
        if (Object.keys(formData).length > 0) {
          console.log('🔄 准备更新表单数据到状态:', formData)

          /** 如果没有 product_name，使用默认值或从用户输入中提取 */
          const userMessage = stateStore.userDescription || localStorage.getItem('userMessage') || ''
          const extractedProductName = formData.product_name || '无痕锁妆粉底液' // 默认值

          /** 直接使用收集到的数据，已经在上面映射过了 */
          const normalizedFormData = {
            brand: formData.brand || '',
            product_name: extractedProductName,
            industry: formData.industry || '',
            competitor: formData.competitor || '',
            product: formData.product || userMessage || '好用', // 使用用户描述或默认值
            pic: formData.pic || '',
          }

          console.log('📋 准备设置的表单数据:', normalizedFormData)

          /** 延迟更新，确保组件已经渲染 */
          setTimeout(() => {
            setFormDataLocal((prev) => {
              const newData = {
                ...prev,
                ...normalizedFormData,
              }
              console.log('📋 更新表单状态为:', newData)
              return newData
            })

            /** 确保表单显示 */
            if (!showForm) {
              console.log('🔄 显示表单')
              setShowForm(true)
            }
          }, 500) // 稍微延迟，等待 thinking 渲染完成
        }
        else {
          console.warn('⚠️ 没有收集到任何表单数据')
        }

        /** 不要立即关闭 thinking，让 TypewriterTextWithScroll 组件完成渲染 */
        // onThinkingComplete 回调会在渲染完成后自动调用
      }
    }
    catch (error) {
      console.error('text_link_disassemble workflow failed:', error)
      console.error('Workflow failed. Please try again.')
      setIsThinking(false)
    }
  }

  const handleFormSubmit = async () => {
    if (isSubmitting)
      return

    /** 如果图片正在上传，阻止提交 */
    if (isUploadingImage) {
      console.log('图片正在上传，请稍后再提交')
      return
    }

    setFormErrors({})
    const errors: Record<string, string> = {}

    /** 验证所有必填字段 */
    const requiredFields = [
      { key: 'brand', label: 'Please enter the brand name to continue.' },
      { key: 'product_name', label: 'Please enter the product name to continue.' },
      { key: 'industry', label: 'Please enter the industry to continue.' },
      { key: 'competitor', label: 'Please enter the competitor information to continue.' },
      { key: 'product', label: 'Please enter the product description to continue.' },
      { key: 'pic', label: 'Please upload an image to continue.' },
    ]

    requiredFields.forEach(({ key, label }) => {
      const value = formDataLocal[key as keyof typeof formDataLocal]
      if (!value || (typeof value === 'string' && !value.trim())) {
        errors[key] = label
      }

      /** 特殊检查：确保图片是 OSS URL 而不是 blob URL */
      if (key === 'pic' && value && typeof value === 'string' && value.startsWith('blob:')) {
        errors[key] = '图片正在上传，请稍后再提交'
      }
    })

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      console.error('Please fill in all required fields')
      return
    }

    setIsSubmitting(true)

    /** 提交表单时关闭 thinking loading */
    setIsThinking(false)

    try {
      /** 获取 taskInstanceId - 优先从 text_link_disassemble 响应中获取，否则使用初始的 */
      const textLinkTaskId = localStorage.getItem('text_link_taskInstanceId')
      const initialTaskId = localStorage.getItem('taskInstanceId')
      const taskInstanceId = textLinkTaskId || initialTaskId || ''

      if (!taskInstanceId) {
        console.error('Task instance ID not found. Please restart the process.')
        setIsSubmitting(false)
        return
      }

      console.log('📌 Using taskInstanceId:', taskInstanceId, {
        fromTextLink: !!textLinkTaskId,
        fromInitial: !!initialTaskId,
      })

      /** 准备接口请求参数 */
      const requestPayload = {
        taskInstanceId,
        formData: {
          brand_name: formDataLocal.brand || '',
          product_name: formDataLocal.product_name || '',
          industry_name: formDataLocal.industry || '',
          competitor_name: formDataLocal.competitor || '',
          inputText: formDataLocal.product || '',
          pic: formDataLocal.pic || '',
        },
      }

      console.log('📤 Submitting form to /api/app/market/confirm-form:', requestPayload)

      /** 保存用户提交的图片到 store */
      if (requestPayload.formData.pic) {
        stateStore.userSubmitPic = requestPayload.formData.pic
        console.log('💾 保存用户提交的图片链接:', requestPayload.formData.pic)
      }

      /** 调用确认表单接口 - request.post 已经返回解析后的数据 */
      const response = await request.post('/app/market/confirm-form', requestPayload)
      console.log('📨 Confirm form response:', response)

      /** 检查响应状态 - 注意：response 已经是 data 对象 */
      /** 如果 request 工具已经处理了错误，这里只需要检查业务逻辑 */
      if (!response) {
        throw new Error('No response from server')
      }

      console.log('✅ Form submitted successfully:', response)

      /** 如果返回了新的 taskInstanceId，使用新的 */
      let finalTaskInstanceId = taskInstanceId
      /** 检查两种可能的响应格式 */
      const newTaskId = response.taskInstanceId || response.data?.taskInstanceId
      if (newTaskId) {
        console.log('📌 Got new taskInstanceId from confirm-form:', newTaskId)
        /** 更新 taskInstanceId 为新的值 */
        finalTaskInstanceId = newTaskId
        localStorage.setItem('confirmed_taskInstanceId', finalTaskInstanceId)
      }

      /** 准备完整的表单数据用于后续流程 */
      const completeFormData = {
        industry: formDataLocal.industry || '',
        industry_id: 83,
        product_name: formDataLocal.product_name || '',
        ip: '专研彩妆',
        brand: formDataLocal.brand || '',
        role: '中国时尚彩妆领导品牌',
        product: formDataLocal.product || '',
        pic: formDataLocal.pic || '',
        competitor: formDataLocal.competitor || '',
        company: formDataLocal.brand || '',
        marketing_strategy: '好用',
        product_market: '',
        competitor_info: '女性彩妆',
      }

      /** 保存到 stateStore 并切换到工作流模式 */
      stateStore.cacheFormData = completeFormData
      stateStore.chatV2FlowMode = 'workflow'
      setIsLoadingSteps(true) // 开始加载工作流步骤

      console.log('Form submitted successfully!')

      /** 保存表单数据供 data_report 使用 */
      const dataReportParams = {
        brand_name: formDataLocal.brand || '',
        competitor_name: formDataLocal.competitor || '',
        industry_id: '83', // 转为字符串格式
        industry_name: formDataLocal.industry || '',
        product_name: formDataLocal.product_name || '',
        platform: 'rednote',
      }
      localStorage.setItem('dataReportParams', JSON.stringify(dataReportParams))
      console.log('💾 Saved data_report params:', dataReportParams)

      /** 只有在确认成功时才执行 command_pot */
      /** 根据你提供的响应格式，response 本身就是完整的响应对象 */
      if (response.confirmStatus === 'SUCCESS' || (response.data && response.data.confirmStatus === 'SUCCESS')) {
        console.log('🚀 Starting command_pot workflow after successful form confirmation...')

        /** 执行 command_pot 工作流 */
        const executeCommandPot = async () => {
          try {
            const { userStore } = await import('@/store/userStore')
            const token = userStore.token
            /** 从 localStorage 和 stateStore 获取最新的 detectedIntent */
            const latestIntent = stateStore.detectedIntent || localStorage.getItem('detectedIntent') || 'create_post'
            console.log('📌 Using intent_result for command_pot:', latestIntent, {
              fromStore: stateStore.detectedIntent,
              fromLocalStorage: localStorage.getItem('detectedIntent'),
            })

            const executeParams = {
              taskInstanceId: finalTaskInstanceId, // 使用新的或原有的 taskInstanceId
              platform: 'rednote',
              workflowName: 'command_pot',
              parameters: {
                user_submit: stateStore.userDescription || '',
                intent_result: latestIntent, // 使用最新的 intent 值（可能来自 dialogue 工作流的 code 节点）
              },
            }

            console.log('📤 Executing command_pot with params:', executeParams)

            const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
              body: JSON.stringify(executeParams),
            })

            if (!sseResponse.ok) {
              throw new Error(`Failed to execute command_pot: ${sseResponse.statusText}`)
            }

            /** 处理 SSE 流响应 */
            const reader = sseResponse.body?.getReader()
            const decoder = new TextDecoder()

            if (reader) {
              console.log('📨 Receiving command_pot SSE stream...')
              let buffer = ''

              while (true) {
                const { done, value } = await reader.read()
                if (done) {
                  console.log('✅ command_pot workflow completed')
                  break
                }

                const chunk = decoder.decode(value, { stream: true })
                buffer += chunk
                const lines = buffer.split('\n')
                buffer = lines.pop() || ''

                for (const line of lines) {
                  if (line.trim() === '')
                    continue

                  if (line.startsWith('data:')) {
                    const data = line.slice(5).trim()
                    if (data === '[DONE]') {
                      console.log('📍 command_pot stream ended')
                      continue
                    }

                    try {
                      const jsonData = JSON.parse(data)
                      console.log('📝 command_pot message:', jsonData)

                      /** 解析步骤数据 */
                      if (jsonData.content && jsonData.node_title === 'four_steps') {
                        try {
                          const stepsArray = JSON.parse(jsonData.content)
                          if (Array.isArray(stepsArray) && stepsArray.length > 0) {
                            const steps = stepsArray[0] // 获取第一个对象
                            const parsedSteps: WorkflowStep[] = []

                            /** 解析每个步骤 */
                            Object.entries(steps).forEach(([key, value]) => {
                              const stepMatch = key.match(/step(\d+)/)
                              if (stepMatch && typeof value === 'string') {
                                const stepNumber = Number.parseInt(stepMatch[1])

                                /** 尝试两种格式的解析 */
                                let agent = ''
                                let description = ''

                                /** 格式1："第X步. 部门名称：描述" */
                                const chineseMatch = value.match(/第.*步\.\s*([^：:]+)[：:](.+)/)
                                /** 格式2："Department Name: Description"（英文格式） */
                                const englishMatch = value.match(/^([^:]+):\s*(.+)/)

                                if (chineseMatch) {
                                  agent = `${chineseMatch[1]} Agent`
                                  description = chineseMatch[2].trim()
                                }
                                else if (englishMatch) {
                                  agent = `${englishMatch[1].trim()} Agent`
                                  description = englishMatch[2].trim()
                                }
                                else {
                                  /** 如果都不匹配，使用整个value作为描述 */
                                  agent = `Step ${stepNumber} Agent`
                                  description = value
                                }

                                parsedSteps.push({
                                  step: stepNumber,
                                  agent,
                                  description,
                                  borderColor: stepNumber === 1
                                    ? '#DD9DFF'
                                    : '#36D3FF',
                                })
                              }
                            })

                            /** 按步骤号排序 */
                            parsedSteps.sort((a, b) => a.step - b.step)

                            console.log('✅ 解析出的工作流步骤:', parsedSteps)
                            setWorkflowSteps(parsedSteps)
                            setIsLoadingSteps(false)
                          }
                        }
                        catch (parseError) {
                          console.error('Failed to parse steps content:', parseError)
                        }
                      }
                    }
                    catch (e) {
                      console.error('Failed to parse SSE data:', e)
                    }
                  }
                }
              }
            }
          }
          catch (error) {
            console.error('Failed to execute command_pot:', error)
            console.error('Failed to start AI workflow. Please try again.')
          }
        }

        /** 异步执行 command_pot，不阻塞UI */
        executeCommandPot()
      }
      else {
        console.log('⚠️ Form confirmation not successful or code not 200, skipping command_pot workflow')
        console.warning('Form confirmed but workflow not started. Please check the response.')
      }
    }
    catch (error) {
      console.error('Form submission failed:', error)
      console.error('Failed to submit form. Please try again.')
      // const a = {
      //   brand_report: dataReportData.brand_report,
      //   industry_report: dataReportData.industry_report,
      //   insight_report: insightReportData.insight_report,
      //   competitor_report: competitorReportData.competitor_report,
      //   product_name: dataReportParams.product_name,
      //   brand_name: dataReportParams.brand_name,
      // }
    }
    finally {
      setIsSubmitting(false)
    }
  }

  /** 转换 MarketStep1Params 到 TrendStep1Params */
  const convertToTrendParams = (marketParams: MarketStep1Params): TrendStep1Params => {
    return {
      brand: marketParams.brand,
      product_name: marketParams.product_name,
      industry: marketParams.industry,
      industry_id: marketParams.industry_id,
      competitor: marketParams.competitor || '',
      product: marketParams.product,
      role: marketParams.role,
      company: marketParams.company || marketParams.brand,
      pic: marketParams.pic,
      ip: marketParams.ip,
      marketing_strategy: marketParams.marketing_strategy || '',
      product_market: marketParams.product_market || '',
      competitor_info: marketParams.competitor_info || '',
    }
  }

  /** 为两个卡片同步调用流式API更新报告内容 - 修复重复调用问题 */
  const updateBothCardsReportSync = useCallback(async (insightReportItemId: string, competitorReportItemId: string) => {
    console.warn('[updateBothCardsReportSync] 开始同步调用流式API:', { insightReportItemId, competitorReportItemId })

    try {
      /** 导入流式API函数 */
      const { callCozeStreamAPI, callCompetitorCozeStreamAPI } = await import('../stores/cozeStreamApi')

      /** 第一步：执行 insight_report 流式接口 - 添加防重复检查 */
      if (!hasTriggeredInsightReport.current) {
        hasTriggeredInsightReport.current = true
        console.warn('[updateBothCardsReportSync] 步骤1: 执行 insight_report')
        let isFirstContent = true

        /** 开始接收数据时立即打开报告面板，以便实时看到渲染 */
        stateStoreRef.current.isReportOpen = true
        eventBus.emit(DistributionEvent.SetActiveTab, insightReportItemId)

        await callCozeStreamAPI(
          (data: ParsedStreamData) => {
            const { reportStore, messageStore } = trendAg
            const targetItem = reportStore.items.find(item => item.id === insightReportItemId)

            if (data.type === 'report_title' && data.title && targetItem) {
              targetItem.title = data.title
              console.warn('[updateBothCardsReportSync] 更新insight报告标题:', data.title)

              /** 更新第一个卡片的标题 */
              const firstCard = messageStore.messages.find(msg => msg.meta?.cardId === 'interior-design-report')
              if (firstCard && firstCard.card) {
                firstCard.card.title = data.title
                if (firstCard.card.cardConfig?.content) {
                  firstCard.card.cardConfig.content.title = data.title
                }
                console.warn('[updateBothCardsReportSync] 更新第一个卡片标题:', data.title)
              }
            }
            else if (data.type === 'report_display' && data.content && targetItem) {
              if (isFirstContent) {
                targetItem.content = data.content
                isFirstContent = false
                /** 首次接收内容时，确保报告面板显示正确的 tab */
                eventBus.emit(DistributionEvent.SetActiveTab, insightReportItemId)
              }
              else {
                targetItem.content += data.content
              }
              console.warn('[updateBothCardsReportSync] 更新insight报告内容:', `${data.content.substring(0, 50)}...`)

              /** 触发 store 更新以确保界面重新渲染 */
              reportStore.items = [...reportStore.items]
            }
            else if (data.type === 'thinking_stream' && data.content) {
            /** 将insight_report的thinking数据传递给流式ThinkingStream组件 */
              console.warn('[updateBothCardsReportSync] 📝 接收到insight_report thinking数据:', `${data.content.substring(0, 100)}...`)
              setThinkingData(data.content)

              /** 推送数据到Insight阶段的流式thinking组件 */
              streamingDataManager.pushData(STREAM_IDS.INSIGHT_THINKING, data.content, true)
              console.warn('[updateBothCardsReportSync] ✅ Insight Thinking数据已推送到流式系统')

              /** 保持原有的stateStore更新以兼容其他组件 */
              stateStore.thinkingContent = data.content
              stateStore.isThinkingActive = true
            }
            else if (data.type === 'complete') {
              console.warn('[updateBothCardsReportSync] ✅ Insight Report 流式数据接收完成')
              setInsightReportCompleted(true) // 设置 insight_report 完成状态
            }
          },
          (error: Error) => {
            console.error('[updateBothCardsReportSync] Insight Report 流式API调用失败:', error)
            const { reportStore } = trendAg
            const targetItem = reportStore.items.find(item => item.id === insightReportItemId)
            if (targetItem) {
              targetItem.content = `行业洞察分析失败：${error.message}\n\n请检查网络连接后重试。`
            }
          },
          () => {
            console.warn('[updateBothCardsReportSync] Insight Report 流式响应完成')
            setInsightReportCompleted(true) // 确保在完成回调中也设置状态
          },
        )
      }
      else {
        console.warn('[updateBothCardsReportSync] insight_report 已经触发过，跳过')
        setInsightReportCompleted(true) // 如果已经触发过，直接设置为完成
      }

      /** 第二步：调用 competitor_report 流式接口 - 添加防重复检查 */
      if (!hasTriggeredCompetitorReport.current) {
        hasTriggeredCompetitorReport.current = true
        console.warn('[updateBothCardsReportSync] 步骤2: 执行 competitor_report')
        let isFirstContentCompetitor = true

        /** 切换到 competitor_report 的 tab，以便实时看到渲染 */
        setTimeout(() => {
          eventBus.emit(DistributionEvent.SetActiveTab, competitorReportItemId)
          console.warn('[updateBothCardsReportSync] 切换到 competitor_report tab')
        }, 100)

        await callCompetitorCozeStreamAPI(
          (data: ParsedStreamData) => {
            const { reportStore, messageStore } = trendAg
            const targetItem = reportStore.items.find(item => item.id === competitorReportItemId)

            if (data.type === 'report_title' && data.title && targetItem) {
              targetItem.title = data.title
              console.warn('[updateBothCardsReportSync] 更新competitor报告标题:', data.title)

              /** 更新第二个卡片的标题 */
              const secondCard = messageStore.messages.find(msg => msg.meta?.cardId === 'competitive-analysis')
              if (secondCard && secondCard.card) {
                secondCard.card.title = data.title
                if (secondCard.card.cardConfig?.content) {
                  secondCard.card.cardConfig.content.title = data.title
                }
                console.warn('[updateBothCardsReportSync] 更新第二个卡片标题:', data.title)
              }
            }
            else if (data.type === 'report_display' && data.content && targetItem) {
              if (isFirstContentCompetitor) {
                targetItem.content = data.content
                isFirstContentCompetitor = false
                /** 首次接收内容时，切换到 competitor tab */
                eventBus.emit(DistributionEvent.SetActiveTab, competitorReportItemId)
              }
              else {
                targetItem.content += data.content
              }
              console.warn('[updateBothCardsReportSync] 更新competitor报告内容:', `${data.content.substring(0, 50)}...`)

              /** 触发 store 更新以确保界面重新渲染 */
              reportStore.items = [...reportStore.items]
            }
            else if (data.type === 'complete') {
              console.warn('[updateBothCardsReportSync] ✅ Competitor Report 流式数据接收完成')
              setCompetitorReportCompleted(true) // 设置 competitor_report 完成状态
            }
          },
          (error: Error) => {
            console.error('[updateBothCardsReportSync] Competitor Report 流式API调用失败:', error)
            const { reportStore } = trendAg
            const targetItem = reportStore.items.find(item => item.id === competitorReportItemId)
            if (targetItem) {
              targetItem.content = `竞争对手分析失败：${error.message}\n\n请检查网络连接后重试。`
            }
          },
          () => {
            console.warn('[updateBothCardsReportSync] Competitor Report 流式响应完成')
            setCompetitorReportCompleted(true) // 确保在完成回调中也设置状态
          },
        )
      }
      else {
        console.warn('[updateBothCardsReportSync] competitor_report 已经触发过，跳过')
        setCompetitorReportCompleted(true) // 如果已经触发过，直接设置为完成
      }

      console.warn('[updateBothCardsReportSync] 同步流式API调用完成')
    }
    catch (error) {
      console.error('[updateBothCardsReportSync] 执行失败:', error)
    }
  }, [stateStore, setThinkingData, setInsightReportCompleted, setCompetitorReportCompleted, hasTriggeredInsightReport, hasTriggeredCompetitorReport])

  /** 独立的planning_scheme请求方法 - 为Continue to Strategy按钮专用 */
  /** 转换 distill_daren_list 数据格式 */
  const transformDistillDarenListData = (listData: any) => {
    const result: any = {
      KOL: [],
      KOC: [],
      Regulars: [],
      All: []
    }

    // 处理原始数据格式
    const data = Array.isArray(listData) ? listData[0] : listData

    // 处理 KOL 数据
    if (data.kol && Array.isArray(data.kol)) {
      result.KOL = data.kol.map((item: any, index: number) => {
        const info = item.info || item
        return {
          id: `KOL-${index}`,
          image: info.imageList || [],
          title: info.title || '',
          user: { name: info.nick || '' },
          stats: {
            likes: info.like
              ? (info.like >= 10000
                  ? `${(info.like / 10000).toFixed(1)}w`
                  : info.like >= 1000
                    ? `${(info.like / 1000).toFixed(1)}k`
                    : String(info.like))
              : '0',
            comm: String(info.comm || 0),
            read: String(info.read || 0),
          },
          noteLink: item.noteLink || '',
          desc: info.desc || '',
        }
      })
    }

    // 处理 KOC 数据
    if (data.koc && Array.isArray(data.koc)) {
      result.KOC = data.koc.map((item: any, index: number) => {
        const info = item.info || item
        return {
          id: `KOC-${index}`,
          image: info.imageList || [],
          title: info.title || '',
          user: { name: info.nick || '' },
          stats: {
            likes: info.like
              ? (info.like >= 10000
                  ? `${(info.like / 10000).toFixed(1)}w`
                  : info.like >= 1000
                    ? `${(info.like / 1000).toFixed(1)}k`
                    : String(info.like))
              : '0',
            comm: String(info.comm || 0),
            read: String(info.read || 0),
          },
          noteLink: item.noteLink || '',
          desc: info.desc || '',
        }
      })
    }

    // 处理素人数据
    if (data.suren && Array.isArray(data.suren)) {
      result.Regulars = data.suren.map((item: any, index: number) => {
        const info = item.info || item
        return {
          id: `Regulars-${index}`,
          image: info.imageList || [],
          title: info.title || '',
          user: { name: info.nick || '' },
          stats: {
            likes: info.like
              ? (info.like >= 10000
                  ? `${(info.like / 10000).toFixed(1)}w`
                  : info.like >= 1000
                    ? `${(info.like / 1000).toFixed(1)}k`
                    : String(info.like))
              : '0',
            comm: String(info.comm || 0),
            read: String(info.read || 0),
          },
          noteLink: item.noteLink || '',
          desc: info.desc || '',
        }
      })
    }

    // 合并所有数据到 All
    result.All = [...result.KOL, ...result.KOC, ...result.Regulars]

    return result
  }

  /** 自动触发 hotpots_analysis 和 distill_daren_list 工作流 */
  const triggerWorkflowsAfterPlanningScheme = useCallback(async () => {
    console.warn('[triggerWorkflowsAfterPlanningScheme] 🚀 开始自动触发工作流')
    console.warn('[triggerWorkflowsAfterPlanningScheme] 当前标记状态:', {
      hasTriggeredHotpotsRef: hasTriggeredHotpotsRef.current,
      hasTriggeredDistillRef: hasTriggeredDistillRef.current,
      storeHotpots: stateStore.hasTriggeredHotpotsAnalysis,
      storeDistill: stateStore.hasTriggeredDistillDarenList,
      planningReport: stateStore.planningReportContent ? '有内容' : '无内容'
    })

    const currentTaskInstanceId = stateStore?.taskInstanceId || localStorage.getItem('taskInstanceId')
    if (!currentTaskInstanceId) {
      console.error('[triggerWorkflowsAfterPlanningScheme] ❌ 缺少 taskInstanceId')
      return
    }

    try {
      /** 1. 触发 hotpots_analysis 工作流 */
      if (!hasTriggeredHotpotsRef.current && !stateStore.hasTriggeredHotpotsAnalysis) {
        console.warn('[triggerWorkflowsAfterPlanningScheme] ✅ 准备触发 hotpots_analysis')
        hasTriggeredHotpotsRef.current = true
        stateStore.hasTriggeredHotpotsAnalysis = true
        console.warn('[triggerWorkflowsAfterPlanningScheme] 已设置 hotpots_analysis 触发标记为 true')

        const { callHotpotsAnalysisAPI } = await import('../stores/cozeStreamApi')

        callHotpotsAnalysisAPI(
          (data) => {
            console.log('[Auto] hotpots_analysis 接收数据:', data)

            const topicTitleList = ['topic1', 'topic2', 'topic3', 'topic4', 'topic5']
            const topicDetailNodeTitle = ['topic1_detail', 'topic2_detail', 'topic3_detail', 'topic4_detail', 'topic5_detail']

            if (data.nodeTitle && data.content) {
              if (topicTitleList.includes(data.nodeTitle) || topicDetailNodeTitle.includes(data.nodeTitle)) {
                if (!stateStore.hotpotsTopics) {
                  stateStore.hotpotsTopics = {}
                }
                stateStore.hotpotsTopics[data.nodeTitle] = data.content
                console.log('[Auto] 存储 hotpots topic:', data.nodeTitle)
              }
            }
          },
          (error) => {
            console.error('[Auto] hotpots_analysis 错误:', error)
          },
          () => {
            console.log('[Auto] hotpots_analysis 工作流完成')
            stateStore.isHotpotsAnalysisComplete = true
          },
          {
            planning_report: stateStore.planningReportContent || ''
          },
          currentTaskInstanceId as string
        )
      } else {
        console.warn('[triggerWorkflowsAfterPlanningScheme] ⚠️ hotpots_analysis 已经触发过，跳过')
      }

      /** 2. 触发 distill_daren_list 工作流 */
      if (!hasTriggeredDistillRef.current && !stateStore.hasTriggeredDistillDarenList) {
        console.warn('[triggerWorkflowsAfterPlanningScheme] ✅ 准备触发 distill_daren_list')
        hasTriggeredDistillRef.current = true
        stateStore.hasTriggeredDistillDarenList = true
        console.warn('[triggerWorkflowsAfterPlanningScheme] 已设置 distill_daren_list 触发标记为 true')

        if (!stateStore.distillDarenListData) {
          stateStore.distillDarenListData = {
            loading: true,
            KOC: [],
            KOL: [],
            Regulars: [],
            All: []
          }
        }

        if (stateStore.trendSelectionState) {
          stateStore.trendSelectionState.showRednoteList = true
        }

        const { callDistillDarenListAPI } = await import('../stores/cozeStreamApi')

        callDistillDarenListAPI(
          (data) => {
            console.log('[Auto] distill_daren_list 接收数据:', data)

            if (data.nodeTitle && data.content && stateStore.distillDarenListData) {
              try {
                if (data.nodeTitle === 'biji_list' || data.node_title === 'biji_list') {
                  const parsedData = JSON.parse(data.content)
                  const listData = Array.isArray(parsedData) ? parsedData : (parsedData.list || parsedData.data || parsedData)

                  if (Array.isArray(listData)) {
                    const transformedData = transformDistillDarenListData(listData)
                    Object.assign(stateStore.distillDarenListData, transformedData)
                    console.log('[Auto] 更新小红书列表数据')
                  }
                }
              } catch (error) {
                console.error('[Auto] 解析 distill_daren_list 数据失败:', error)
              }
            }
          },
          (error) => {
            console.error('[Auto] distill_daren_list 错误:', error)
          },
          () => {
            console.log('[Auto] distill_daren_list 工作流完成')
            if (stateStore.distillDarenListData) {
              stateStore.distillDarenListData.loading = false
            }
          },
          currentTaskInstanceId as string
        )
      } else {
        console.warn('[triggerWorkflowsAfterPlanningScheme] ⚠️ distill_daren_list 已经触发过，跳过')
      }

      /** 3. 显示 TrendSelectionPage */
      setTimeout(() => {
        stateStore.showTrendSelection = true
        console.warn('[triggerWorkflowsAfterPlanningScheme] 设置 showTrendSelection = true')
      }, 500)

    } catch (error) {
      console.error('[triggerWorkflowsAfterPlanningScheme] 触发工作流失败:', error)
    }
  }, [stateStore])

  const executePlanningSchemeOnly = useCallback(async (planningReportItemId: string) => {
    console.warn('[executePlanningSchemeOnly] 🚀🚀🚀 开始独立执行planning_scheme工作流')
    console.warn('[executePlanningSchemeOnly] planningReportItemId:', planningReportItemId)
    console.warn('[executePlanningSchemeOnly] 当前 stateStore.showTrendSelection:', stateStore.showTrendSelection)

    /** 检查是否已经触发过 */
    if (hasTriggeredPlanningScheme.current) {
      console.warn('[executePlanningSchemeOnly] planning_scheme 已经触发过，跳过重复请求')
      setPlanningSchemeCompleted(true) // 如果已经触发过，直接设置为完成
      return
    }

    /** 标记已触发，防止重复 */
    hasTriggeredPlanningScheme.current = true
    console.warn('[executePlanningSchemeOnly] 标记 hasTriggeredPlanningScheme = true')

    /** 重置 planning_scheme 完成状态 */
    setPlanningSchemeCompleted(false)

    /** 获取 taskInstanceId */
    const currentTaskInstanceId = stateStore.taskInstanceId || localStorage.getItem('confirmed_taskInstanceId') || localStorage.getItem('taskInstanceId') || ''
    console.warn('[executePlanningSchemeOnly] 当前 taskInstanceId:', currentTaskInstanceId)

    if (!currentTaskInstanceId) {
      console.error('[executePlanningSchemeOnly] ⚠️ 没有找到 taskInstanceId！')
    }

    try {
      /** 导入planning_scheme专用的流式API函数 */
      const { callPlanningSchemeCozeStreamAPI } = await import('../stores/cozeStreamApi')

      /** 执行 planning_scheme 流式接口 */
      console.warn('[executePlanningSchemeOnly] 执行 planning_scheme')
      let isFirstContent = true
      let collectedPlanningReport = '' // 收集完整的 planning_report 内容

      await callPlanningSchemeCozeStreamAPI(
        (data: ParsedStreamData) => {
          /** 调试日志：打印所有接收到的数据 */
          console.warn('[executePlanningSchemeOnly] 接收到数据:', {
            type: data.type,
            nodeTitle: data.nodeTitle,
            contentLength: data.content?.length || 0,
            hasContent: !!data.content,
          })

          const { reportStore } = trendAg
          const targetItem = reportStore.items.find(item => item.id === planningReportItemId)

          if (data.type === 'report_title' && data.title && targetItem) {
            targetItem.title = data.title
            console.warn('[executePlanningSchemeOnly] 更新planning报告标题:', data.title)
          }
          else if (data.type === 'report_display' && data.content && targetItem) {
            if (isFirstContent) {
              targetItem.content = data.content
              isFirstContent = false
            }
            else {
              targetItem.content += data.content
            }
            console.warn('[executePlanningSchemeOnly] 更新planning报告内容:', `${data.content.substring(0, 50)}...`)

            /** 收集 planning_report 内容 - 注意：planning_report1 是用于显示的，planning_report 是实际的数据 */
            if (data.nodeTitle === 'planning_report1') {
              collectedPlanningReport += data.content
              console.warn('[executePlanningSchemeOnly] 收集 planning_report1 (display) 内容, 片段:', data.content.substring(0, 50))
              console.warn('[executePlanningSchemeOnly] 当前累计长度:', collectedPlanningReport.length)
            }
          }
          else if (data.type === 'workflow_param' && data.nodeTitle === 'planning_report') {
            /** 收集 workflow_param 类型的 planning_report - 这是实际的完整数据 */
            collectedPlanningReport += data.content
            console.warn('[executePlanningSchemeOnly] 收集 planning_report (workflow_param) 内容, 片段:', data.content.substring(0, 50))
            console.warn('[executePlanningSchemeOnly] 当前累计长度:', collectedPlanningReport.length)
          }
          else if (data.type === 'thinking_stream' && data.content) {
            /** 将thinking数据传递给流式ThinkingStream组件 */
            console.warn('[executePlanningSchemeOnly] 📝 接收到planning thinking数据:', `${data.content.substring(0, 100)}...`)
            setThinkingData(data.content)

            /** 推送数据到Planning阶段的流式thinking组件 */
            streamingDataManager.pushData(STREAM_IDS.PLANNING_THINKING, data.content, true)
            console.warn('[executePlanningSchemeOnly] ✅ Planning Thinking数据已推送到流式系统')

            /** 保持原有的stateStore更新以兼容其他组件 */
            stateStore.thinkingContent = data.content
            stateStore.isThinkingActive = true
          }
          else if (data.type === 'complete') {
            console.warn('[executePlanningSchemeOnly] ✅ Planning Scheme 流式数据接收完成')
            setPlanningSchemeCompleted(true) // 设置 planning_scheme 完成状态
            console.warn('[executePlanningSchemeOnly] ✅✅✅ setPlanningSchemeCompleted(true) 已调用！！！')
            console.warn('[executePlanningSchemeOnly] 收集到的 planning_report 内容:', collectedPlanningReport
              ? `长度: ${collectedPlanningReport.length}`
              : '空')

            /** 立即更新按钮状态 */
            const planningCard = trendAg.messageStore.messages.find(
              msg => msg.meta?.cardId === 'planning-scheme-report',
            )
            console.warn('[executePlanningSchemeOnly] 查找 planning-scheme-report 卡片:', planningCard
              ? '找到'
              : '未找到')

            if (planningCard && planningCard.card && planningCard.card.cardConfig?.button) {
              console.warn('[executePlanningSchemeOnly] 更新按钮状态 - 之前:', planningCard.card.cardConfig.button.text)
              planningCard.card.cardConfig.button.text = 'Approve Strategy'
              planningCard.card.cardConfig.button.variant = 'gradient-border'
              planningCard.card.cardConfig.button.disabled = false
              console.warn('[executePlanningSchemeOnly] 更新按钮状态 - 之后:', planningCard.card.cardConfig.button.text)

              /** 触发重新渲染 */
              trendAg.messageStore.messages = [...trendAg.messageStore.messages]
            }

            /** 当数据接收完成时，只更新 planning_report 内容，不自动显示 TrendSelection */
            if (collectedPlanningReport) {
              console.warn('[executePlanningSchemeOnly] 设置 planning_report 内容到 stateStore')

              /** 确保 taskInstanceId 也被设置 */
              if (!stateStore.taskInstanceId && currentTaskInstanceId) {
                stateStore.taskInstanceId = currentTaskInstanceId
                console.warn('[executePlanningSchemeOnly] 设置 taskInstanceId:', currentTaskInstanceId)
              }

              stateStore.planningReportContent = collectedPlanningReport
              /** 不在这里触发工作流，等待 onComplete 回调 */
              console.warn('[executePlanningSchemeOnly] planning_report 已保存，等待 onComplete 触发工作流')
              console.warn('[executePlanningSchemeOnly] planningReportContent 长度:', stateStore.planningReportContent.length)
              console.warn('[executePlanningSchemeOnly] taskInstanceId:', stateStore.taskInstanceId)
            }
            else {
              console.error('[executePlanningSchemeOnly] ⚠️ 没有收集到 planning_report 内容！')
            }
          }
        },
        (error: Error) => {
          console.error('[executePlanningSchemeOnly] Planning Scheme 流式API调用失败:', error)
          const { reportStore } = trendAg
          const targetItem = reportStore.items.find(item => item.id === planningReportItemId)
          if (targetItem) {
            targetItem.content = `营销策划方案生成失败：${error.message}\n\n请检查网络连接后重试。`
          }
        },
        async () => {
          console.warn('[executePlanningSchemeOnly] Planning Scheme 流式响应完成（onComplete回调）')
          console.warn('[executePlanningSchemeOnly] 最终收集到的 planning_report:', collectedPlanningReport
            ? `长度: ${collectedPlanningReport.length}`
            : '空')

          /** 确保在完成时也设置数据 */
          if (collectedPlanningReport && !stateStore.planningReportContent) {
            console.warn('[executePlanningSchemeOnly] 在完成回调中设置 planning_report 内容')

            /** 确保 taskInstanceId 也被设置 */
            if (!stateStore.taskInstanceId && currentTaskInstanceId) {
              stateStore.taskInstanceId = currentTaskInstanceId
              console.warn('[executePlanningSchemeOnly] onComplete - 设置 taskInstanceId:', currentTaskInstanceId)
            }

            stateStore.planningReportContent = collectedPlanningReport
            /** 自动触发 hotpots_analysis 和 distill_daren_list 工作流 */
            console.warn('[executePlanningSchemeOnly] ✅ planning_report 已保存，准备触发后续工作流')
            console.warn('[executePlanningSchemeOnly] 参数状态:', {
              taskInstanceId: stateStore.taskInstanceId,
              planningReportLength: collectedPlanningReport.length,
              hasTriggeredHotpots: hasTriggeredHotpotsRef.current,
              hasTriggeredDistill: hasTriggeredDistillRef.current
            })

            /** 自动触发工作流 */
            await triggerWorkflowsAfterPlanningScheme()
            console.warn('[executePlanningSchemeOnly] ✅ 工作流触发调用完成')
          }
          else if (collectedPlanningReport && stateStore.planningReportContent) {
            console.warn('[executePlanningSchemeOnly] planning_report 已在 complete 事件中设置，跳过')
          }
          else {
            console.error('[executePlanningSchemeOnly] onComplete - 没有 planning_report 数据可设置')
          }
        },
      )

      console.warn('[executePlanningSchemeOnly] planning_scheme独立执行完成')
    }
    catch (error) {
      console.error('[executePlanningSchemeOnly] 执行失败:', error)
    }
  }, [stateStore, setThinkingData, setPlanningSchemeCompleted])

  /** 创建自定义的两个策略卡片 */
  const createCustomStrategyCards = useCallback(() => {
    /** 防重复创建检查 */
    if (cardsCreated) {
      console.warn('[createCustomStrategyCards] 卡片已存在，跳过创建')
      return
    }

    console.warn('[createCustomStrategyCards] 开始创建卡片，时间戳:', Date.now())

    const curStep = 'step0' as any
    const currentLeftIcon = 'researchAnalyst' // 使用 Research Analyst 图标

    /** 创建第一个报告项 - 初始标题为Loading，等待数据响应后更新 */
    const industryReportItem = addReportItem({
      type: 'markdown',
      title: 'Loading...', // 初始标题为Loading
      content: 'Analyzing industry trends and market dynamics...',
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, true, trendAg)

    /** 创建第二个报告项 - 初始标题为Loading，等待数据响应后更新 */
    const competitiveReportItem = addReportItem({
      type: 'markdown',
      title: 'Loading...', // 初始标题为Loading
      content: 'Analyzing competitor strategies and positioning opportunities...',
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, false, trendAg)

    /** 保存竞争对手分析报告项ID */
    setCompetitiveReportItemId(competitiveReportItem.id)
    console.warn('[createCustomStrategyCards] 设置竞争对手分析报告项ID:', competitiveReportItem.id)

    /** 手动设置基础时间戳，确保卡片显示在正确位置 */
    const baseTimestamp = Date.now() - 10000 // 减去10秒，确保卡片显示在其他消息之前

    /** 第一个卡片：初始显示Loading，数据响应后更新标题 */
    const firstCard = createCardMessage(
      {
        title: 'Industry Analysis Report', // 初始通用标题
        description: 'Analyzing industry trends and market dynamics...',
        variant: 'success',
        onClick: () => {
          /** 直接显示对应的报告内容，不需要tab切换 */
          stateStoreRef.current.isReportOpen = true
          stateStoreRef.current.showTrendSelection = false // 隐藏 TrendSelection
          taskStoreRef.current.currentStep = curStep
          eventBus.emit(DistributionEvent.SetActiveTab, industryReportItem.id)
          /** 关闭图片预览卡片 */
          ChatEventBus.emit('hideImagePreview')
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon1',
            size: 'md' as const,
          },
          content: {
            title: 'Industry Analysis Report', // 初始通用标题
            description: 'Analyzing industry trends and market dynamics...',
          },
          layout: 'simple' as const,
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'interior-design-report',
        },
      },
      trendAg,
    )

    /** 第二个卡片：初始显示Loading，数据响应后更新标题 */
    const secondCard = createCardMessage(
      {
        title: 'Competitive Analysis', // 初始通用标题
        description: 'Analyzing competitor strategies and positioning opportunities...',
        variant: 'success',
        onClick: () => {
          /** 直接显示对应的报告内容，不需要tab切换 */
          stateStoreRef.current.isReportOpen = true
          stateStoreRef.current.showTrendSelection = false // 隐藏 TrendSelection
          taskStoreRef.current.currentStep = curStep
          eventBus.emit(DistributionEvent.SetActiveTab, competitiveReportItem.id)
          /** 关闭图片预览卡片 */
          ChatEventBus.emit('hideImagePreview')
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon2',
            size: 'md' as const,
          },
          content: {
            title: 'Competitive Analysis', // 初始通用标题
            description: 'Analyzing competitor strategies and positioning opportunities...',
          },
          layout: 'simple' as const,
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'competitive-analysis',
        },
      },
      trendAg,
    )

    /** 手动调整时间戳，确保卡片按正确顺序显示在其他消息之前 */
    firstCard.timestamp = baseTimestamp + 1 // 第一个卡片
    secondCard.timestamp = baseTimestamp + 2 // 第二个卡片

    console.warn('[createCustomStrategyCards] 卡片已创建，时间戳:', {
      firstCard: { id: firstCard.id, timestamp: firstCard.timestamp, title: firstCard.card?.title },
      secondCard: { id: secondCard.id, timestamp: secondCard.timestamp, title: secondCard.card?.title },
      totalMessages: trendAg.messageStore.messages.length,
      messageTypes: trendAg.messageStore.messages.map(msg => ({ id: msg.id, type: msg.type, timestamp: msg.timestamp })),
    })

    /** 使用修复后的同步流式API调用，确保ReportPreview正确显示 */
    if (!hasCalledReportSync) {
      setTimeout(() => {
        setHasCalledReportSync(true) // 标记已调用
        /** 重置工作流完成状态 */
        setDataReportCompleted(false)
        setInsightReportCompleted(false)
        setCompetitorReportCompleted(false)
        setPlanningSchemeCompleted(false)
        setHasApprovedStrategy(false)
        /** 不重置 hasTriggeredTrendWorkflows，防止重复触发 */
        // stateStore.hasTriggeredTrendWorkflows = false  // 注释掉，保持状态
        stateStore.showTrendSelection = false
        updateBothCardsReportSync(industryReportItem.id, competitiveReportItem.id)
      }, 200) // 短暂延迟确保卡片创建完成
    }
    else {
      console.warn('[createCustomStrategyCards] updateBothCardsReportSync 已调用过，跳过重复调用')
    }

    /** 立即打开报告面板并显示第一个卡片的tab，以便实时看到渲染 */
    stateStoreRef.current.isReportOpen = true
    setTimeout(() => {
      eventBus.emit(DistributionEvent.SetActiveTab, industryReportItem.id)
      console.warn('[createCustomStrategyCards] 自动打开报告面板，显示第一个卡片tab')
    }, 100) // 短暂延迟确保报告项已创建

    /** 设置创建完成标志，防止重复创建 */
    setCardsCreated(true)
  }, [cardsCreated, updateBothCardsReportSync, hasCalledReportSync]) // 添加依赖

  /**
   * 注意：原 updateStrategyReport 函数已移除
   * Strategy相关的API调用现在通过其他方式处理
   */

  /** Planning Scheme流式API更新函数 - 创建专门的报告项 */
  const updatePlanningSchemeReport = useCallback(async () => {
    try {
      const { addReportItem } = await import('../stores/create')

      const planningSchemeReportItem = addReportItem({
        type: 'markdown',
        title: 'Planning Scheme Report',
        content: '正在生成营销策划方案...',
        meta: {
          step: 'step0',
          canTransformCode: true,
        },
      }, true, false, trendAg)

      /** 导入Planning Scheme专用的流式API函数 */
      const { callPlanningSchemeCozeStreamAPI } = await import('../stores/cozeStreamApi')

      /** 调用Planning Scheme流式接口 */
      await callPlanningSchemeCozeStreamAPI(
        (data) => {
          if (data.type === 'thinking_stream' && data.content) {
            /** 处理Planning Scheme专用的thinking数据 */
            setPlanningThinkingData(data.content)
            setShowPlanningThinking(true)

            /** 同时更新原有的thinking数据以保持兼容性 */
            setThinkingData(data.content)
            setIsThinkingDataReady(true)
            stateStore.thinkingContent = data.content
            stateStore.isThinkingActive = true
          }
          else if (data.type === 'report_title' && data.title) {
            const { reportStore } = trendAg
            const planningItem = reportStore.items.find(item => item.id === planningSchemeReportItem.id)
            if (planningItem) {
              planningItem.title = data.title
            }
          }
          else if (data.type === 'report_display' && data.content) {
            const { reportStore } = trendAg
            const planningItem = reportStore.items.find(item => item.id === planningSchemeReportItem.id)
            if (planningItem) {
              if (data.content.startsWith('# ') || data.content.startsWith('## ')) {
                planningItem.content = data.content
              }
              else {
                planningItem.content += data.content
              }
            }
          }
        },
        (error: Error) => {
          console.error('[updatePlanningSchemeReport] Planning Scheme流式API调用失败:', error)
          const { reportStore } = trendAg
          const planningItem = reportStore.items.find(item => item.id === planningSchemeReportItem.id)

          if (planningItem) {
            planningItem.content = `Planning Scheme报告生成失败：${error.message}\n\n请检查网络连接后重试。`
          }
        },
        () => {
          /** 流式完成后，自动打开报告面板并更新卡片点击事件 */
          setTimeout(() => {
            stateStore.isReportOpen = true
            eventBus.emit(DistributionEvent.SetActiveTab, planningSchemeReportItem.id)

            /** 更新Planning Scheme卡片的点击事件 */
            const planningSchemeCard = trendAg.messageStore.messages.find(
              msg => msg.meta?.isPlanningSchemeCard === true,
            )
            if (planningSchemeCard && planningSchemeCard.card) {
              planningSchemeCard.card.onClick = () => {
                stateStore.isReportOpen = true
                stateStore.showTrendSelection = false // 隐藏 TrendSelection
                eventBus.emit(DistributionEvent.SetActiveTab, planningSchemeReportItem.id)
              }
            }
          }, 500)
        },
      )
    }
    catch (error) {
      console.error('Planning Scheme API failed:', error)
    }
  }, [stateStore])

  /** 创建Planning Scheme后续卡片 - 在Approve Strategy后显示 */
  const createPlanningFollowUpCard = useCallback(() => {
    const curStep = 'step1' as any
    const currentLeftIcon = 'brandStrategist'

    const followUpCard = createCardMessage(
      {
        title: 'The Trend Report and Reference Posts',
        description: 'Reference posts is curated to match your brand positioning.',
        variant: 'success',
        onClick: () => {
          console.warn('[ImplementationCard] 点击实施计划卡片 - 显示 TrendSelectionPage')

          /** 关闭图片预览 */
          ChatEventBus.emit('hideImagePreview')

          /** 显示 TrendSelectionPage（里面包含 hotpots_analysis 和 distill_daren_list 的结果） */
          stateStore.showTrendSelection = true
          stateStore.isReportOpen = true

          console.warn('[ImplementationCard] 已设置 showTrendSelection = true')
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon3', // 使用自定义 SVG 图标
            size: 'md' as const,
          },
          content: {
            title: 'The Trend Report',
            description: 'Review the trend report based on your choice',
          },
          socialPost: {
            image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
            author: {
              name: 'Milla',
              avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31aje2ouc6q0049042ub7u510c0c06r0?imageView2/2/w/120/format/webp|imageMogr2/strip',
            },
          },
          layout: 'social' as const,
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'implementation-plan-card',
          isImplementationCard: true,
        },
      },
      trendAg,
    )

    /** 设置时间戳确保显示在正确位置 */
    const futureTimestamp = Date.now() + 120000 // 比Planning Scheme卡片晚60秒
    followUpCard.timestamp = futureTimestamp

    return { followUpCard }
  }, [stateStore])

  /** 创建Planning Scheme独立卡片 - 创建独立的DOM节点和卡片 */
  const createPlanningSchemeCard = useCallback(() => {
    const curStep = 'step0' as any
    const currentLeftIcon = 'researchAnalyst' // 使用 Research Analyst 图标

    /** 先检查是否已存在报告项，避免重复创建 */
    let planningReportItem = trendAg.reportStore.items.find(
      item => item.title === 'Planning Scheme Report',
    )

    if (!planningReportItem) {
      /** 首次创建Planning Scheme报告项 */
      planningReportItem = addReportItem({
        type: 'markdown',
        title: 'Planning Scheme Report',
        content: '正在生成营销策划方案...',
        meta: {
          step: curStep,
          canTransformCode: true,
        },
      }, true, false, trendAg)
      console.warn('[createPlanningSchemeCard] 创建新的Planning报告项:', planningReportItem.id)
    }
    else {
      console.warn('[createPlanningSchemeCard] 使用已存在的Planning报告项:', planningReportItem.id)
    }

    /** 创建Planning Scheme专用的卡片消息，标记为独立的Strategy卡片 */
    const planningSchemeCard = createCardMessage(
      {
        title: 'Planning Scheme Report',
        description: '正在生成营销策划方案，包含完整的实施路线图和关键指标...',
        variant: 'success',
        onClick: () => {
          console.warn('[Planning Scheme Card] 卡片被点击 - 显示 planning_scheme 报告')

          /** 点击卡片（非按钮）始终显示 planning_scheme 报告 */
          /** 使用保存的报告项 ID 直接打开 */
          if (planningReportItem && planningReportItem.id) {
            /** 关闭图片预览卡片 */
            ChatEventBus.emit('hideImagePreview')

            stateStore.isReportOpen = true
            stateStore.showTrendSelection = false // 隐藏 TrendSelection
            eventBus.emit(DistributionEvent.SetActiveTab, planningReportItem.id)
            console.warn('[Planning Scheme Card] 已打开 planning_scheme 报告:', planningReportItem.id)
          }
          else {
            /** 如果没有报告项ID，尝试通过标题查找 */
            const planningReport = trendAg.reportStore.items.find(item => item.title === 'Planning Scheme Report')
            if (planningReport) {
              ChatEventBus.emit('hideImagePreview')
              stateStore.isReportOpen = true
              stateStore.showTrendSelection = false // 隐藏 TrendSelection
              eventBus.emit(DistributionEvent.SetActiveTab, planningReport.id)
              console.warn('[Planning Scheme Card] 通过标题找到并打开报告:', planningReport.id)
            }
            else {
              console.warn('[Planning Scheme Card] 未找到 planning_scheme 报告，报告可能还未创建')
            }
          }
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon1',
            size: 'md' as const,
          },
          content: {
            title: 'Planning Scheme Report',
            description: '营销策划方案生成中，包含实施路线图、关键指标和预算分配等详细内容。',
          },
          layout: 'simple' as const,
          button: {
            text: isStrategyApproved
              ? 'Approved'
              : 'Approve Strategy', // 直接显示 Approve Strategy
            variant: isStrategyApproved
              ? 'success' as const
              : 'gradient-border' as const, // 直接使用 gradient-border 样式
            position: 'bottom-left' as const,
            disabled: isStrategyApproved || hasApprovedStrategy, // 移除 planningSchemeCompleted 检查
            onClick: async () => {
              if (isStrategyApproved || hasApprovedStrategy)
                return

              /** 如果 planning_scheme 还没完成，先检查并设置必要的数据 */
              if (!planningSchemeCompleted) {
                console.warn('[Approve Strategy] planning_scheme 未完成，设置默认数据')
                if (!stateStore.taskInstanceId) {
                  /** 尝试从 localStorage 获取 */
                  const taskId = localStorage.getItem('confirmed_taskInstanceId') || localStorage.getItem('taskInstanceId')
                  if (taskId) {
                    stateStore.taskInstanceId = taskId
                  }
                }
                if (!stateStore.planningReportContent) {
                  stateStore.planningReportContent = '' // 设置空字符串，允许继续
                }
              }

              /** 防止重复点击 */
              setHasApprovedStrategy(true)
              setIsStrategyApproved(true)
              /** 保持报告预览打开，这样用户可以切换 */
              stateStoreRef.current.isReportOpen = true
              taskStoreRef.current.currentStep = 'step0' as any
              setSecondaryDisplayText(`Approve Strategy`)

              /** Approve Strategy 按钮点击处理 - 不需要重复触发工作流 */
              console.warn('[Approve Strategy] 按钮被点击，工作流已经在 planning_scheme 完成后自动触发')

              try {
                /** 检查工作流是否已经触发 */
                if (!hasTriggeredHotpotsRef.current || !hasTriggeredDistillRef.current) {
                  console.warn('[Approve Strategy] 工作流尚未触发，等待 planning_scheme 完成后自动触发')
                } else {
                  console.warn('[Approve Strategy] 工作流已经触发，无需重复执行')
                }

                // 仅保留必要的状态更新逻辑
                if (true) { // 保持代码结构
                  /** 显示 TrendSelectionPage - 工作流已经在 planning_scheme 完成后自动触发 */
                  console.warn('[Approve Strategy] 显示 TrendSelectionPage，工作流数据应该已经准备好')
                  
                  /** 设置显示小红书列表 */
                  if (stateStore.trendSelectionState) {
                    stateStore.trendSelectionState.showRednoteList = true
                    console.warn('[Approve Strategy] 设置 showRednoteList = true，准备显示小红书列表')
                  }
                  
                  /** 设置 showTrendSelection 为 true，显示 TrendSelectionPage */
                  stateStore.showTrendSelection = true
                  console.warn('[Approve Strategy] 已设置 showTrendSelection 为 true')
                }
              }
              catch (error) {
                console.error('[Approve Strategy] 调用工作流失败:', error)
              }

              /** 创建Planning Scheme后续卡片并显示 */
              setTimeout(() => {
                createPlanningFollowUpCard()
                setShowImplementationCard(true)
              }, 1000)
            },
          },
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'planning-scheme-report',
          isStrategyCard: true, // 标记为Strategy卡片，让StrategyCardRenderer能找到它
          isPlanningSchemeCard: true, // 额外标记为Planning Scheme卡片
        },
      },
      trendAg,
    )

    /** 设置特殊的时间戳，确保卡片显示在ThinkingStream下方 */
    const futureTimestamp = Date.now() + 60000
    planningSchemeCard.timestamp = futureTimestamp

    return { planningSchemeCard, planningReportItemId: planningReportItem.id }
  }, [stateStore, isStrategyApproved, createPlanningFollowUpCard, planningSchemeCompleted, hasApprovedStrategy])

  /** 处理开始分析按钮点击 */
  const handleStartAnalysis = async () => {
    /** 临时移除thinking数据依赖，提升用户体验 */
    if (isButtonLoading || isButtonDisabled)
      return

    /** 重置工作流触发标记，允许重新执行 */
    console.warn('[handleStartAnalysis] 重置工作流触发标记')
    hasTriggeredPlanningScheme.current = false
    hasTriggeredHotpotsRef.current = false
    hasTriggeredDistillRef.current = false
    stateStore.hasTriggeredHotpotsAnalysis = false
    stateStore.hasTriggeredDistillDarenList = false

    /** 标记分析已开始，显示分析结果区域 */
    setIsAnalysisStarted(true)
    setIsButtonLoading(true)

    /** 激活ThinkingStream，优先使用从API接收到的thinking数据 */
    stateStore.isThinkingActive = true

    /** 设置ThinkingStream内容 */
    if (thinkingData && thinkingData.trim() && !thinkingData.includes('正在分析市场趋势')) {
      stateStore.thinkingContent = thinkingData
    }
    else if (stateStore.thinkingContent && stateStore.thinkingContent.trim() && !stateStore.thinkingContent.includes('正在思考') && !stateStore.thinkingContent.includes('正在分析市场趋势')) {
      /** 保持现有内容 */
    }
    else {
      stateStore.thinkingContent = '正在思考...'
    }

    /** 创建Planning Scheme卡片并获取报告项ID */
    let planningReportItemId = ''
    setTimeout(() => {
      const planningCard = createPlanningSchemeCard()
      if (planningCard.planningReportItemId) {
        planningReportItemId = planningCard.planningReportItemId
        console.warn('[handleStartAnalysis] 获取到Planning报告项ID:', planningReportItemId)
      }
    }, 300)

    setShowStrategyCard(true)
    setDisplayText(`Continue to Strategy`)

    /** 开始分析后持续监听消息变化并滚动 */
    const scrollInterval = setInterval(() => {
      const container = document.querySelector('.ChatPageContainer .overflow-auto')
      if (container) {
        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100
        /** 只有在接近底部时才自动滚动，避免干扰用户手动滚动 */
        if (isNearBottom) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }
      }
    }, 500)

    /** 使用独立的planning_scheme请求方法 */
    try {
      /** 等待卡片创建完成 */
      await new Promise(resolve => setTimeout(resolve, 500))

      /** 如果没有获取到报告项ID，尝试从reportStore中查找 */
      if (!planningReportItemId) {
        const { reportStore } = trendAg
        const planningItem = reportStore.items.find(item =>
          item.title?.includes('Planning Scheme') || item.title?.includes('营销策划方案'),
        )
        if (planningItem) {
          planningReportItemId = planningItem.id
          console.warn('[handleStartAnalysis] 从reportStore中找到Planning报告项ID:', planningReportItemId)
        }
      }

      if (planningReportItemId) {
        await executePlanningSchemeOnly(planningReportItemId)
      }
      else {
        console.error('[handleStartAnalysis] 未找到Planning报告项ID，无法执行planning_scheme')
      }

      await new Promise(resolve => setTimeout(resolve, 2000))

      setTimeout(() => clearInterval(scrollInterval), 2000)
      setIsButtonDisabled(true)
    }
    catch (error) {
      console.error('Planning Scheme analysis failed:', error)
      setIsAnalysisStarted(false)
      clearInterval(scrollInterval)
    }
    finally {
      setIsButtonLoading(false)
    }
  }

  useBindWinEvent('resize', () => {
    stateStore.isReportOpen = false
    stateStore.isAgentCollapsed = true
  })

  /** 当页面加载且有表单数据时，创建一个开始按钮让用户手动启动 */
  useEffect(() => {
    if (!formData) {
      return
    }

    /** 创建一个初始任务，让用户点击开始 */
    if (taskStore.agentTasks.length === 0) {
      taskStore.agentTasks.push({
        id: 'start',
        title: 'Marketing Team Ready',
        description: '点击下方按钮开始执行营销方案生成',
        status: 'waiting',
        actions: [{ label: 'Continue to Strategy', type: 'primary' }],
        step: 'step0' as any,
      })
    }
  }, [formData, taskStore.agentTasks])

  /** 监听 showWorkflow 变化，在表单提交后立即创建策略卡片 */
  useEffect(() => {
    if (showWorkflow && !cardsCreated && !isAnalysisStarted) {
      /** 只有在表单提交后（非Continue to Strategy按钮点击后）才创建卡片 */
      console.warn('[useEffect] 表单提交后创建策略卡片')
      setTimeout(() => {
        /** 调用自定义的策略卡片创建方法 */
        createCustomStrategyCards()
      }, 100) // 短暂延迟确保状态更新完成
    }
    else if (!showWorkflow && cardsCreated) {
      /** 当 showWorkflow 变为 false 时，重置创建标志，允许下次重新创建 */
      console.warn('[useEffect] 重置卡片创建标志')
      setCardsCreated(false)
    }
    else if (showWorkflow && !cardsCreated && isAnalysisStarted) {
      /** Continue to Strategy按钮点击后，跳过卡片创建，因为会使用planning_scheme */
      console.warn('[useEffect] Continue to Strategy按钮点击后，跳过双流式API调用')
    }
  }, [showWorkflow, cardsCreated, isAnalysisStarted, createCustomStrategyCards])

  useInsertionEffect(() => {
    const overflow = document.body.style.overflow
    document.body.style.overflow = 'hidden'
    return () => {
      document.body.style.overflow = overflow
    }
  }, [])

  return <div
    className={ cn(
      'ChatPageContainer flex flex-row h-full overflow-hidden p-4 gap-4',
      className,
    ) }
    style={ style }
  >
    {/* 主内容区 */}
    <div className="flex flex-1 flex-col overflow-hidden rounded-2xl bg-white">
      {/* TopBar - 只在左侧聊天区域上方显示 */}
      {showTopBar && (
        <div className="flex-shrink-0 border-b border-gray-100">
          <TopBar
            agents={ topBarAgents }
            onAgentClick={ onTopBarAgentClick }
            dropdownExpanded={ topBarDropdownExpanded }
            onDropdownToggle={ onTopBarDropdownToggle }
            containerClassName="relative"
          />
        </div>
      )}

      {/* 聊天内容区域 */}
      <div className="flex-1 overflow-hidden">
        {chatV2FlowMode
          ? (
            // ChatV2 流程界面 - 使用可滚动容器
              <div className="relative h-full flex flex-col">
                <div className="flex-1 overflow-auto pb-28">
                  {' '}
                  {/* 为底部输入框预留空间 */}
                  <div className="mx-auto max-w-4xl">
                    <ChatWorkflow
                      showContentDisplay
                      content={ userDescription }
                      uploadedImage={ uploadedImage }
                      showThinking
                      isThinking={ isThinking }
                      thinkingExpanded={ thinkingExpanded }
                      onThinkingToggle={ setThinkingExpanded }
                      onThinkingComplete={ handleThinkingComplete }
                      thinkingMessages={ thinkingMessages.length > 0
                        ? thinkingMessages
                        : undefined }
                      showWorkflow={ showWorkflow }
                      workflowSteps={ workflowSteps }
                      isLoadingWorkflowSteps={ isLoadingSteps }
                      showForm={ showForm && !showWorkflow }
                      formData={ formDataLocal }
                      formErrors={ formErrors }
                      formUploadedImage={ formDataLocal.pic }
                      isSubmitting={ isSubmitting || isUploadingImage } // 图片上传时也禁用按钮
                      isUploadingImage={ isUploadingImage } // 传递图片上传状态
                      onFormChange={ handleFormChange }
                      isHotpotsAnalysisComplete={isHotpotsAnalysisComplete}
                      isOriginalWorkLoading={stateStore.originalWorkLoading}
                      onPreviewImages={ () => {
                        /** 仅触发显示预览事件 */
                        ChatEventBus.emit('showImagePreview', { isUserAction: true })
                      } }
                      onStartContentCreation={ async () => {
                        // 移除自动触发 original_work 的代码
                        // original_work 应该只在用户点击 "Start content creation" 按钮时触发
                        console.log('[ChatPage] onStartContentCreation 被调用，但不自动触发工作流')
                      } }
                      onFormSubmit={ handleFormSubmit }
                      onImageUpload={ async (file) => {
                        try {
                          console.log('开始上传图片到 OSS...')
                          setIsUploadingImage(true) // 开始上传

                          /** 先创建本地预览 URL */
                          const localPreviewUrl = URL.createObjectURL(file)
                          handleFormChange('pic', localPreviewUrl)

                          /** 上传到 OSS 获取 URL */
                          const uploadResult = await FileAPI.upFileToUrl([file])
                          if (uploadResult?.downloadLoadFileDetails?.[0]?.url) {
                            const ossUrl = uploadResult.downloadLoadFileDetails[0].url
                            console.log('图片上传成功，OSS URL:', ossUrl)

                            /** 清理本地预览 URL */
                            URL.revokeObjectURL(localPreviewUrl)

                            /** 更新为 OSS URL */
                            handleFormChange('pic', ossUrl)
                            console.log('图片上传成功')
                            /** 清除图片相关错误 */
                            setFormErrors((prev) => {
                              const newErrors = { ...prev }
                              delete newErrors.pic
                              return newErrors
                            })
                          }
                          else {
                            console.error('图片上传失败')
                            handleFormChange('pic', '')
                            /** 添加错误提示 */
                            setFormErrors(prev => ({ ...prev, pic: '图片上传失败，请重试' }))
                          }
                        }
                        catch (error) {
                          console.error('图片上传失败:', error)
                          handleFormChange('pic', '')
                          /** 添加错误提示 */
                          setFormErrors(prev => ({ ...prev, pic: '图片上传失败，请重试' }))
                        }
                        finally {
                          setIsUploadingImage(false) // 结束上传
                        }
                      } }
                      onImageRemove={ () => handleFormChange('pic', '') }
                      workflowTitle="Your AI Agent Team‘s Action plan"
                      workflowDescription="Perfect! Your brand profile is ready. Now I'm bringing together my AI agent team to create amazing trend analysis for you. Here's how we'll work together:"
                      onStartAnalysis={ handleStartAnalysis }
                      showAskInput={ false } // 禁用内置的 Ask 输入框
                      askInputPlaceholder=""
                      askInputValue=""
                      askInputDisabled
                      onAskInputSubmit={ () => {} }
                    />

                    {/* 分析结果区域 - 表单提交后立即显示，包含策略卡片和Continue to Strategy按钮 */}
                    {showWorkflow && (
                      <motion.div
                        className="border-gray-200"
                        initial={ { opacity: 0, y: 20 } }
                        animate={ { opacity: 1, y: 0 } }
                        transition={ { duration: 0.5 } }
                      >
                        <StreamingThinkingStream
                          streamId={ STREAM_IDS.COMBINED_THINKING1 }
                          className="mt-4"
                        />
                        {/* 策略卡片显示区域 - 在motion组件最顶部 */}
                        <div className="mb-6">
                          <ChatHistory
                            taskStore={ taskStore }
                            messageStore={ messageStore }
                            ref={ chatHistoryRef }
                            className="min-h-0 w-full"
                            onDeleteMessage={ removeMessage }
                            stateStore={ stateStore }
                          />
                        </div>

                        {/* Continue to Strategy 按钮 - 在卡片下方 */}
                        <div className="mb-6 flex justify-start">
                          <button
                            className={ cn(
                              'px-8 py-3 rounded-full text-sm font-medium transition-colors',
                              (!insightReportCompleted || !competitorReportCompleted)
                                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                : 'bg-black text-white hover:bg-gray-800',
                              'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black',
                              isButtonLoading && 'cursor-wait',
                            ) }
                            disabled={ isButtonLoading || isButtonDisabled || !insightReportCompleted || !competitorReportCompleted }
                            onClick={ handleStartAnalysis }
                          >
                            <div className="flex items-center gap-2">
                              {isButtonLoading && (
                                <div className="h-4 w-4 animate-spin border-2 border-white border-t-transparent rounded-full" />
                              )}
                              {(!insightReportCompleted || !competitorReportCompleted)
                                ? 'Waiting for reports...'
                                : 'Continue to Strategy'}
                            </div>
                          </button>
                        </div>

                        {/* 文本显示组件 - 在按钮下方，ThinkingStream上方 */}
                        {displayText && (
                          <TextDisplayWithPagination
                            content={ displayText }
                            charsPerPage={ 600 }
                            onCopySuccess={ () => {
                              console.warn('[TextDisplay] 📋 文本复制成功')
                              /** 这里可以添加toast提示 */
                            } }
                            onCopyError={ (error) => {
                              console.error('[TextDisplay] 复制失败:', error)
                              /** 这里可以添加错误提示 */
                            } }
                          />
                        )}
                        {/* Planning阶段 - 流式Thinking显示组件 */}
                        {displayText && (
                          <StreamingThinkingStream
                            streamId={ STREAM_IDS.PLANNING_THINKING1 }
                            className="mt-4"
                          />
                        )}

                        {/* Strategy Implementation Plan 卡片容器 - 在ThinkingStream下方 */}
                        {showStrategyCard && (
                          <StrategyCardRenderer
                            messageStore={ trendAg.messageStore }
                            stateStore={ stateStore }
                          />
                        )}

                        {/* 第二个文本显示组件 - 在Strategy卡片下方，显示实施路线图 */}
                        {secondaryDisplayText && isStrategyApproved && (
                          <TextDisplayWithPagination
                            content={ secondaryDisplayText }
                            charsPerPage={ 500 }
                            className="mt-4"
                            onCopySuccess={ () => {
                              console.warn('[SecondaryTextDisplay] 📋 实施路线图复制成功')
                              /** 这里可以添加toast提示 */
                            } }
                            onCopyError={ (error) => {
                              console.error('[SecondaryTextDisplay] 复制失败:', error)
                              /** 这里可以添加错误提示 */
                            } }
                          />
                        )}
                        {/* distill_daren_list - 流式Thinking显示组件 */}
                        {secondaryDisplayText && isStrategyApproved && (
                          <StreamingThinkingStream
                            streamId={ STREAM_IDS.OPERATIONS_THINKING1 }
                            className="mt-4"
                          />
                        )}
                        {/* Implementation Plan卡片容器 - 在Planning Scheme ThinkingStream下方 */}
                        {showImplementationCard && (
                          <>
                            <ImplementationCardRenderer
                              messageStore={ trendAg.messageStore }
                              stateStore={ stateStore }
                              className="mt-4 w-106.5"
                            />

                            {/* Start content creation 按钮 - 在 The Trend Report 卡片下方 */}
                            <div className="mb-6 mt-5 flex justify-start">
                              <button
                                className={ cn(
                                  'px-8 py-3 rounded-full text-sm font-medium transition-colors',
                                  (isContentCreationDisabled || isContentCreationLoading)
                                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                    : 'bg-black text-white hover:bg-gray-800',
                                  'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black',
                                  isContentCreationLoading && 'cursor-wait',
                                ) }
                                disabled={ isContentCreationDisabled || isContentCreationLoading }
                                onClick={async () => {
                                  console.log('[Start content creation] 按钮被点击')
                                  
                                  // 设置按钮为禁用和加载状态
                                  setIsContentCreationDisabled(true)
                                  setIsContentCreationLoading(true)

                                  // 防重复调用检查
                                  if (stateStore?.isOriginalWorkRunning) {
                                    console.warn('[Start content creation] original_work 工作流正在运行中，跳过重复调用')
                                    // 直接显示已有的 ImagePreviewCard
                                    ChatEventBus.emit('showImagePreview', { isUserAction: true })
                                    setIsContentCreationLoading(false) // 恢复加载状态但保持禁用
                                    return
                                  }

                                  // 获取必要的参数
                                  const currentTaskInstanceId = stateStore?.taskInstanceId || localStorage.getItem('taskInstanceId')
                                  const selectedTopicId = stateStore?.trendSelectionState?.selectedTopicId
                                  const selectedRednoteId = stateStore?.selectedRednoteId

                                  if (!currentTaskInstanceId) {
                                    alert('缺少 taskInstanceId')
                                    setIsContentCreationDisabled(false)
                                    setIsContentCreationLoading(false)
                                    return
                                  }

                                  // 不再强制要求选择话题或小红书笔记，工作流可以无参数运行
                                  if (!selectedTopicId && !selectedRednoteId) {
                                    console.warn('[Start content creation] 未选择话题或小红书笔记，将使用默认参数调用工作流')
                                  }

                                  // 不需要创建卡片，OriginalWorkCardRenderer 会负责渲染
                                  // 检查是否已经在运行，避免重复调用
                                  let originalWorkCard = trendAg.messageStore.messages.find(
                                    (msg: any) => msg.meta?.cardId === 'original-work-report'
                                  )
                                  
                                  if (originalWorkCard && originalWorkCard.originalWorkData) {
                                    // 如果已有数据，直接显示
                                    stateStore.originalWorkData = { ...originalWorkCard.originalWorkData }
                                    console.log('[Start content creation] 恢复已有数据:', stateStore.originalWorkData)
                                    ChatEventBus.emit('showImagePreview', { isUserAction: true })
                                    return
                                  }

                                  // 触发显示 ImagePreviewCard
                                  ChatEventBus.emit('showImagePreview', { isUserAction: true })
                                  console.log('[Start content creation] 已触发显示 ImagePreviewCard')

                                  // 调用 original_work 工作流
                                  try {
                                    const { callOriginalWorkAPI } = await import('../stores/cozeStreamApi')

                                    // 设置运行状态标志，防止重复调用
                                    stateStore.isOriginalWorkRunning = true
                                    stateStore.originalWorkLoading = true
                                    stateStore.originalWorkProgress = {}

                                    // 获取选中的话题和小红书笔记
                                    const selectedTopicId = stateStore.trendSelectionState?.selectedTopicId || ''
                                    const selectedRednoteId = stateStore.distillDarenListData?.selectNoteId || ''
                                    const planningReport = stateStore.planningReportContent || ''
                                    const userSubmitPic = stateStore.userSubmitPic || ''

                                    callOriginalWorkAPI(
                                      (data) => {
                                        console.log('[original_work] 接收数据:', data)
                                        // 更新预览数据到 stateStore
                                        if (data.nodeTitle && data.content) {
                                          stateStore.originalWorkProgress[data.nodeTitle] = true
                                          if (!stateStore.originalWorkData) {
                                            stateStore.originalWorkData = {}
                                          }
                                          stateStore.originalWorkData[data.nodeTitle] = data.content

                                          // 同时更新到卡片中，确保数据持久化
                                          if (originalWorkCard && !originalWorkCard.originalWorkData) {
                                            originalWorkCard.originalWorkData = {}
                                          }
                                          if (originalWorkCard) {
                                            originalWorkCard.originalWorkData[data.nodeTitle] = data.content
                                            // 触发消息列表更新
                                            trendAg.messageStore.messages = [...trendAg.messageStore.messages]
                                          }
                                        }
                                      },
                                      (error) => {
                                        console.error('[original_work] 错误:', error)
                                        stateStore.originalWorkLoading = false
                                        stateStore.isOriginalWorkRunning = false // 清除运行标志
                                      },
                                      () => {
                                        console.log('[original_work] 完成')
                                        stateStore.originalWorkLoading = false
                                        stateStore.isOriginalWorkRunning = false // 清除运行标志
                                        // 完成后确保数据同步到卡片
                                        if (originalWorkCard && stateStore.originalWorkData) {
                                          originalWorkCard.originalWorkData = { ...stateStore.originalWorkData }
                                          trendAg.messageStore.messages = [...trendAg.messageStore.messages]
                                        }
                                      },
                                      currentTaskInstanceId as string,
                                      {
                                        selectedTopicId,
                                        selectedRednoteId,
                                        planningReport,
                                        userSubmitPic
                                      }
                                    )
                                    
                                    // 成功后只关闭加载状态，保持按钮禁用
                                    setIsContentCreationLoading(false)
                                  } catch (error) {
                                    console.error('[Start content creation] 调用失败:', error)
                                    stateStore.originalWorkLoading = false
                                    stateStore.isOriginalWorkRunning = false // 清除运行标志
                                    // 失败时恢复按钮状态
                                    setIsContentCreationDisabled(false)
                                    setIsContentCreationLoading(false)
                                  }
                                }}
                              >
                                <div className="flex items-center gap-2">
                                  {isContentCreationLoading && (
                                    <div className="h-4 w-4 animate-spin border-2 border-white border-t-transparent rounded-full" />
                                  )}
                                  Start content creation
                                </div>
                              </button>
                            </div>

                            {/* Original Work 卡片容器 - 在按钮下方显示 */}
                            <OriginalWorkCardRenderer
                              messageStore={ trendAg.messageStore }
                              stateStore={ stateStore }
                              className="mt-4 w-106.5"
                            />
                          </>
                        )}

                        {/* OperationsManager 组件区域 - 在Implementation卡片下方 */}
                        {showOperationsManagerComponents && (
                          <>
                            {/* OperationsManager 文本显示组件 */}
                            {operationsManagerDisplayText && (
                              <TextDisplayWithPagination
                                content={ operationsManagerDisplayText }
                                charsPerPage={ 600 }
                                className="mt-4 w-106.5"
                                onCopySuccess={ () => {
                                  console.warn('[OperationsManager TextDisplay] 📋 文本复制成功')
                                } }
                                onCopyError={ (error) => {
                                  console.error('[OperationsManager TextDisplay] 复制失败:', error)
                                } }
                              />
                            )}

                            {/* Operations阶段 - 流式Thinking显示组件 */}
                            {operationsManagerDisplayText && (
                              <StreamingThinkingStream
                                streamId={ STREAM_IDS.OPERATIONS_THINKING }
                                className="mt-4"
                              />
                            )}

                            {/* OperationsManager 专用卡片容器 */}
                            {showOperationsManagerCard && (
                              <OperationsManagerCardRenderer
                                stateStore={ stateStore }
                                className="mt-4 w-106.5"
                              />
                            )}
                          </>
                        )}

                      </motion.div>
                    )}
                  </div>
                </div>

                {/* 悬浮的输入框 - 始终显示在底部 */}
                {showWorkflow && (
                  <div className="absolute bottom-0 left-0 right-0 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          value={ isAnalysisStarted
                            ? 'AI analysis in progress...'
                            : 'Analysis will start from workflow above' }
                          readOnly
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                          placeholder={ isAnalysisStarted
                            ? 'Analysis running...'
                            : 'Click the button in workflow to start analysis...' }
                        />
                        {isAnalysisStarted && (
                          <div className="h-8 w-8 flex items-center justify-center">
                            <div className="h-4 w-4 animate-spin border-b-2 border-blue-500 rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )
          : (
            /** 正常的聊天界面 */
              <div className="relative h-full flex flex-col">
                <motion.div
                  layout
                  className={ cn(
                    'flex-1 flex flex-col gap-4 max-w-4xl mx-auto overflow-auto',
                    isReportOpen && 'max-w-3xl',
                  ) }>

                  <ChatHistory
                    taskStore={ taskStore }
                    messageStore={ messageStore }
                    ref={ chatHistoryRef }
                    className="min-h-0 w-full flex-1 p-4"
                    onDeleteMessage={ removeMessage }
                    stateStore={ stateStore }
                  />
                </motion.div>

                {/* 预留悬浮输入框位置 - 目前不显示 */}
                {false && (
                  <div className="absolute bottom-0 left-0 right-0 border-t border-gray-200 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          placeholder="Type your console..."
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                        />
                        <button className="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500 text-white transition-colors hover:bg-blue-600">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
      </div>
    </div>

    {/* ReportPreview 已移至父组件的右侧区域 */}
  </div>
})

ChatPage.displayName = 'ChatPage'

export type ChatPageProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode

  taskStore: TaskStoreType
  stateStore: StateStoreType
  messageStore: MessageStoreType
  mdToCodePreview: MdToCodePreviewType
  resetDistributionStore: () => void
  reportStore: ReportStoreType
  stepState: StepStateType

  // TopBar 相关 props
  showTopBar?: boolean
  topBarAgents?: any[]
  onTopBarAgentClick?: (agent: any) => void
  topBarDropdownExpanded?: boolean
  onTopBarDropdownToggle?: (expanded: boolean) => void

  // ChatV2 流程相关 props
  onStartAIAnalysis?: (formData: any) => Promise<void>
}
& React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLDivElement>, HTMLDivElement>

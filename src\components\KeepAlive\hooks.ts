import type { KeepAliveContextType } from './type'
import { onMounted } from '@/hooks'
import { KeepAliveContext } from './context'

export const useActiveEffect: KeepAliveContextType['registerActiveEffect'] = (key, callback) => {
  const { registerActiveEffect, delActiveEffect } = useContext(KeepAliveContext)

  onMounted(() => {
    registerActiveEffect?.(key, callback)

    return () => {
      delActiveEffect?.(key)
    }
  })
}

export const useDeactiveEffect: KeepAliveContextType['registerDeactiveEffect'] = (key, callback) => {
  const { registerDeactiveEffect, delDeactiveEffect } = useContext(KeepAliveContext)

  onMounted(() => {
    registerDeactiveEffect?.(key, callback)

    return () => {
      delDeactiveEffect?.(key)
    }
  })
}

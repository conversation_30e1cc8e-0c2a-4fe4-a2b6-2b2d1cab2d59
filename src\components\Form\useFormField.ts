import type { ChangeEvent } from 'react'
import { useCallback, useContext, useState } from 'react'
import { FormContext } from './Form'

export function useFormField<
  V = any,
  E = ChangeEvent<HTMLElement>,
  PV = V,
>({
  name,
  value,
  error,
  errorMessage,
  onChange,
  defaultValue = '' as unknown as V,
}: UseFormFieldProps<V, E, PV>) {
  const formContext = useContext(FormContext)
  const isInForm = !!formContext && !!name
  const formState = isInForm
    ? formContext.state
    : null

  const fieldValue = isInForm && name
    ? formState?.values[name] as V
    : undefined
  const fieldError = isInForm && name
    ? formState?.touched[name] && !!formState?.errors[name]
    : false
  const fieldErrorMessage = isInForm && name && formState?.touched[name]
    ? formState?.errors[name]
    : undefined

  const actualValue = isInForm && name
    ? fieldValue
    : value
  const actualError = isInForm && name
    ? fieldError
    : error
  const actualErrorMessage = isInForm && name
    ? fieldErrorMessage
    : errorMessage

  const [internalVal, setInternalVal] = useState<V>(defaultValue)
  const isControlMode = actualValue !== undefined
  const realValue = isControlMode
    ? actualValue
    : internalVal

  const handleChangeVal = useCallback(
    (val: V, e: E) => {
      if (isInForm && name) {
        formContext.setFieldValue(name, val)
      }
      else if (isControlMode) {
        onChange?.(val as unknown as PV, e)
      }
      else {
        setInternalVal(val)
      }

      if (isInForm && onChange) {
        onChange(val as unknown as PV, e)
      }
    },
    [isControlMode, onChange, isInForm, name, formContext],
  )

  const handleBlur = useCallback(() => {
    if (isInForm && name) {
      formContext.setFieldTouched(name, true)
      formContext.validateField(name)
    }
  }, [isInForm, name, formContext])

  return {
    isInForm,
    formContext,
    actualValue: realValue,
    actualError,
    actualErrorMessage,
    isControlMode,
    handleChangeVal,
    handleBlur,
  }
}

export interface UseFormFieldProps<V = any, E = ChangeEvent<HTMLElement>, PV = V> {
  name?: string
  value?: V
  defaultValue?: V
  error?: boolean
  errorMessage?: string
  onChange?: (value: PV, e: E) => void
}

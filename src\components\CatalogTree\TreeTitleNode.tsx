import {
  FC,
  SyntheticEvent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react"

import { CatalogDO, CatalogVO } from "@/dts"
import { useAppDispatch, useT } from "@/hooks"

import styles from "./index.module.less"
import { Button, Dropdown, Input, InputRef, MenuProps, Typography } from "antd"

import classNames from 'clsx'
import { cancelBubble } from "@/utils"
import {
  deleteCatalogs,
  openAddRenameModal,
  renameDirectory,
} from "@/store/features/catalogSlice"
import { god } from "@/god"
import { Codepen, Ellipsis, Plus, Trash2 } from 'lucide-react'

interface Props {
  data: CatalogVO
  mode: "select" | "action"
}

const TreeTitleNode: FC<Props> = (props) => {
  const { data, mode } = props

  const appDispatch = useAppDispatch()

  const t = useT()

  const inputRef = useRef<InputRef>(null)
  const [editingNode, setEditingNode] = useState<CatalogDO>()

  const isActionMode = useMemo(() => "action" === mode, [mode])

  const onMenuItemClick: MenuProps["onClick"] = useCallback(
    (info: { key: string; domEvent: SyntheticEvent }) => {
      const { key, domEvent } = info
      cancelBubble(domEvent)

      const catalog = data

      switch (key) {
        // 新建文件夹
        case "new-catalog":
          {
            appDispatch(
              openAddRenameModal({
                name: "",
                dirId: catalog?.parentDirectoryId || undefined,
                isRename: false,
              }),
            )
          }
          break

        // 新建子文件夹
        case "new-child-catalog":
          appDispatch(
            openAddRenameModal({
              name: "",
              dirId: catalog.id,
              isRename: false,
            }),
          )
          break

        // 重命名
        case "rename":
          setEditingNode(catalog)
          break

        // 删除文件夹
        case "delete":
          {
            god.modal.confirm({
              title: t("layout.delete-confirm"),
              centered: true,
              okButtonProps: { danger: true },
              onOk: async () => {
                await appDispatch(
                  deleteCatalogs({ dirId: catalog.id, nested: true }),
                )

                god.messageSuccess()
              },
            })
          }
          break
      }
    },
    [appDispatch, data, t],
  )

  const genMenu = useCallback(() => {
    const items: MenuProps["items"] = [
      {
        key: "new-catalog",
        label: t("models.new-folder"),
        icon: <Plus />,
      },
      {
        key: `new-child-catalog`,
        label: t("models.new-child-folder"),
        icon: <Plus />,
      },
      {
        key: `rename`,
        label: t("rename"),
        icon: <Codepen />,
      },
      {
        key: `delete`,
        label: t("delete"),
        icon: <Trash2 />,
        danger: true,
      },
    ]

    return { items, onClick: onMenuItemClick }
  }, [onMenuItemClick, t])

  // 重命名
  const onEditingChange = useCallback(() => {
    if (!editingNode) {
      return
    }
    appDispatch(
      renameDirectory({
        dirId: editingNode.id,
        name: editingNode.name,
      }),
    ).then(() => {
      setEditingNode(undefined)
    })
  }, [editingNode, appDispatch])

  useEffect(() => {
    if (editingNode) {
      inputRef.current?.focus()
    }
  }, [editingNode])

  const { name, numOfChildren = 0, numOfSubDir = 0, id } = data

  const num = numOfSubDir + numOfChildren
  const canOperate = isActionMode

  return !editingNode ? (
    <div
      className={classNames(styles["title-renderer"], {
        [styles["action"]]: canOperate,
      })}
    >
      <Typography.Text>{name}</Typography.Text>

      {canOperate && (
        <Dropdown menu={genMenu()} trigger={["click"]} placement="bottomLeft">
          <Button
            type="text"
            size="small"
            className={styles["more-btn"]}
            icon={<Ellipsis className={styles["node-icon"]} />}
            onClick={cancelBubble}
          />
        </Dropdown>
      )}
      {0 < num && (
        <Typography.Text className={styles.text}>{num}</Typography.Text>
      )}
    </div>
  ) : (
    <Input
      key={id}
      ref={inputRef}
      className={styles["editing-input"]}
      size="small"
      value={editingNode.name}
      onChange={(e) =>
        setEditingNode((d) => ({ ...d!, name: e.target.value || "" }))
      }
      onBlur={onEditingChange}
      onPressEnter={onEditingChange}
    />
  )
}

export default TreeTitleNode

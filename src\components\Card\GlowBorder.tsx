import { cn } from '@/utils'
import { memo, useEffect, useRef, useState } from 'react'

export const GlowBorder = memo(({
  className,
  children,
  borderSize = 2,
  gradientColors = ['#D89FFF', '#A7AFFF', '#5AC8FF', '#3AD2FF'],
  animationDuration = '4s',
  spin = true,
  ...rest
}: GlowBorderProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [glowSize, setGlowSize] = useState(0)

  useEffect(() => {
    if (!containerRef.current)
      return

    const updateSize = () => {
      const el = containerRef.current
      if (!el)
        return

      const { width, height } = el.getBoundingClientRect()
      const maxSize = Math.max(width, height)
      setGlowSize(maxSize * 1.4)
    }

    updateSize()

    const resizeObserver = new ResizeObserver(updateSize)
    resizeObserver.observe(containerRef.current)

    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current)
      }
      resizeObserver.disconnect()
    }
  }, [])

  return (
    <div
      ref={ containerRef }
      className={ cn('relative overflow-hidden', className) }
      style={ { padding: borderSize } }
      { ...rest }
    >
      { glowSize > 0 && (
        <div
          style={ {
            width: glowSize,
            height: glowSize,
            background: `conic-gradient(from 0deg, ${gradientColors.join(', ')}, ${gradientColors[0]})`,
            translate: '-50% -50%',
            animationDuration,
          } }
          aria-hidden="true"
          className={ cn('absolute left-1/2 top-1/2 z-1', { 'animate-spin': spin }) }
        />
      ) }

      <div
        className="relative z-2 h-full w-full overflow-hidden"
        style={ {
          borderRadius: 'inherit',
        } }
      >
        { children }
      </div>
    </div>
  )
})

GlowBorder.displayName = 'GlowBorder'

export type GlowBorderProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & {
  children: React.ReactNode
  borderSize?: number
  gradientColors?: string[]
  animationDuration?: string
  spin?: boolean
}

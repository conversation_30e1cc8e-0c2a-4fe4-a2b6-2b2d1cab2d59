/**
 * Trend 模块状态管理
 *
 * 功能：
 * - 管理应用状态（加载、处理、错误等）
 * - 管理聊天消息和报告内容
 * - 管理代理任务和步骤状态
 * - 提供状态重置和订阅功能
 */

import type { TrendStep1Params } from '@/api/TrendApi'
import type { AgentTask, ChatMessage, ReportCodeMap, ReportContentItem, StepNum } from '../types'
import { createProxy } from '@/hooks'
import { ref } from 'valtio'
import { subscribeKey } from 'valtio/utils'
import { handleTrendAgentError } from './error'

/**
 * 创建初始步骤状态
 */
function createInitialStepState(): Record<StepNum, { isProcessing: boolean }> {
  const steps: StepNum[] = ['step0', 'step1', 'step2', 'step3', 'step4', 'step5', 'step6', 'step7', 'step8']
  return steps.reduce((acc, step) => {
    acc[step] = { isProcessing: false }
    return acc
  }, {} as Record<StepNum, { isProcessing: boolean }>)
}

/**
 * 创建初始报告代码映射
 */
function createInitialReportCodeMap(): Record<string, ReportCodeMap> {
  const reportTypes = ['brand_report', 'industry_report', 'brand_market_report']
  return reportTypes.reduce((acc, type) => {
    acc[type] = {
      code: '',
      done: false,
      isToCoding: false,
      mdIsProcessing: false,
    }
    return acc
  }, {} as Record<string, ReportCodeMap>)
}

/**
 * 创建 Trend 状态存储
 */
function cTrendStores() {
  /** 主要状态存储 */
  const stateStore = createProxy({
    /** 当前模式：表单模式或聊天模式 */
    mode: 'chat' as 'form' | 'chat', // 默认直接进入聊天模式
    /** 是否正在加载 */
    isLoading: false,
    /** 是否正在处理 */
    isProcessing: false,
    /** 是否已提交 */
    isSubmitted: false,
    /** 报告面板是否打开 */
    isReportOpen: false,
    /** 代理面板是否折叠 */
    isAgentCollapsed: false,

    /** 是否有错误 */
    hasError: false,
    /** 错误消息 */
    errorMsg: '',

    /** ChatV2 流程状态 */
    chatV2FlowMode: null as 'thinking' | 'form' | 'workflow' | 'text_link_disassemble' | null,
    /** 用户描述 */
    userDescription: '',
    /** 上传的图片 */
    uploadedImage: null as string | null,
    /** 任务实例ID */
    taskInstanceId: null as string | null,
    /** 检测到的意图 */
    detectedIntent: null as string | null,
    /** 收集的表单数据 */
    collectedFormData: null as any,
    /** SSE 流消息 */
    sseStreamMessages: [] as any[],

    /** Thinking 流程状态 */
    isThinkingActive: false,
    /** Thinking 内容 */
    thinkingContent: '',
    
    /** TrendSelection 相关状态 */
    showTrendSelection: false,
    planningReportContent: '',
    hasTriggeredTrendWorkflows: false, // 标记是否已触发过 hotpots_analysis 和 distill_daren_list
    hasTriggeredHotpotsAnalysis: false, // 单独标记 hotpots_analysis 是否已触发
    hasTriggeredDistillDarenList: false, // 单独标记 distill_daren_list 是否已触发
    trendSelectionState: {
      selectedTopicId: '', // 选中的话题ID
      previewTopicId: '', // 预览的话题ID
      showRednoteList: false, // 是否显示小红书列表
    },
    
    /** hotpots_analysis 收集的 topics 数据 */
    hotpotsTopics: {
      topic1: '',
      topic2: '',
      topic3: '',
      topic4: '',
      topic5: '',
      topic1_detail: '',
      topic2_detail: '',
      topic3_detail: '',
      topic4_detail: '',
      topic5_detail: '',
    },
    
    /** original_work 工作流相关 */
    originalWorkData: null as any,
    originalWorkLoading: false, // original_work 加载状态
    originalWorkProgress: {} as Record<string, boolean>, // 各节点的加载进度
    userSubmitPic: 'https://www.carslan.com.cn/uploads/upload/images/20241107/bcadeb2d185a620a711e8daa42520b76.png', // 用户提交的图片链接 - 使用默认值
    
    /** distill_daren_list 渲染数据 */
    distillDarenListData: {
      KOC: [] as any[],
      KOL: [] as any[],
      Regulars: [] as any[],
      All: [] as any[],
      loading: false,
      previewId: '',
      selectNoteId: '',
    },

    /** 设置默认的模拟数据，直接进入聊天模式 */
    formData: {
      brand: '卡姿兰',
      product_name: '无痕锁妆粉底液',
      industry: '彩妆',
      industry_id: 83,
      competitor: '欧莱雅',
      product: '好用',
      role: '"\n卡姿兰作为中国时尚彩妆领导品牌，创立于 2001 年，秉承 \"活出经典\" 的品牌理念，以 \"年轻、轻经典\" 的创意灵感引领彩妆潮流趋势。品牌专注于国内市场，经过 24 年的发展，已成为技术领先的彩妆专家，长期位居中国彩妆行业市占率前五位置。\n核心定位要素：\n品牌主张：为时尚年轻女性而生，传递 \"活出经典\" 的美妆理念\n技术定位：专研彩妆 24 年的技术专家，拥有自主研发能力\n市场定位：国内市场领先的大众时尚彩妆品牌\n价值主张：让中国消费者用上全球最好的彩妆产品\n目标客户画像：\n卡姿兰的核心目标客户群体为 18-40 岁女性，这一群体具有以下特征：\n人口统计特征：\n年龄：18-40 岁，涵盖 Z 世代和千禧一代\n性别：女性为主\n收入水平：中等收入群体，追求性价比\n心理特征：\n对时尚敏感，但不盲从潮流\n注重产品品质和实用性\n具有民族自豪感，认可国货品牌\n追求个性化表达和自我价值实现\n消费行为特征：\n线上购买习惯成熟，活跃于抖音、小红书等社交平台\n注重产品口碑和使用体验\n对爆品和经典产品有持续复购需求\n价格敏感度适中，重视性价比\n"',
      company: '卡姿兰',
      pic: 'https://www.carslan.com.cn/uploads/upload/images/20241107/bcadeb2d185a620a711e8daa42520b76.png',
      ip: '专研彩妆',
      marketing_strategy: '好用',
      product_market: '',
      competitor_info: '女性彩妆',
    } as TrendStep1Params | null,
    cacheFormData: {} as Omit<TrendStep1Params, 'pic'>,

    cacheAiData: ref({
      selected_biji_url: '',
    }) as any,
  })

  subscribeKey(stateStore, 'isReportOpen', (isReportOpen) => {
    isReportOpen && (stateStore.isAgentCollapsed = true)
  })

  subscribeKey(stateStore, 'hasError', (hasError) => {
    handleTrendAgentError(hasError)
  })

  /** 步骤状态存储 */
  const stepState = createProxy<Record<StepNum, {
    isProcessing: boolean
  }>>(createInitialStepState())

  /** 报告代码预览存储 */
  const mdToCodePreview = createProxy(createInitialReportCodeMap())

  const messageStore = createProxy({
    messages: [] as ChatMessage[],
  })

  const reportStore = createProxy({
    items: [] as ReportContentItem[],
  })

  const taskStore = createProxy({
    agentTasks: [] as AgentTask[],
    currentStep: 'step0' as StepNum,
  })

  function getLastStep() {
    const num = taskStore.agentTasks.length
    return `step${num}` as StepNum
  }

  const mapperStore = createProxy({
    nodeTitleToMessageId: {} as Record<string, string>,
    nodeTitleToReportId: {} as Record<string, string>,
  })

  function resetTrendStore() {
    stateStore.reset()
    stepState.reset()
    mdToCodePreview.reset()

    messageStore.reset()
    reportStore.reset()
    taskStore.reset()
    mapperStore.reset()
  }

  return {
    stateStore,
    stepState,
    mdToCodePreview,
    messageStore,
    reportStore,
    taskStore,
    mapperStore,

    getLastStep,
    resetTrendStore,
  }
}

export const trendAg = cTrendStores()
export const trendHi = cTrendStores()

/** 延迟导入以避免循环依赖 */
let ButtonStateManager: any
import('./create').then((module) => {
  ButtonStateManager = module.ButtonStateManager
  /** 为每个 store 实例创建全局按钮状态管理器 */
  ;(trendAg as any).globalButtonManager = new ButtonStateManager(trendAg)
  ;(trendHi as any).globalButtonManager = new ButtonStateManager(trendHi)
})

// window.hi = hi

export type StateStoreType = typeof trendAg.stateStore
export type StepStateType = typeof trendAg.stepState
export type MdToCodePreviewType = typeof trendAg.mdToCodePreview
export type MessageStoreType = typeof trendAg.messageStore
export type ReportStoreType = typeof trendAg.reportStore
export type TaskStoreType = typeof trendAg.taskStore
export type MapperStoreType = typeof trendAg.mapperStore

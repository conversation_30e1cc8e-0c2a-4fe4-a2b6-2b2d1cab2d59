/* eslint-disable @typescript-eslint/no-explicit-any */

import { request } from "@/utils"
import { FileAPI } from './FileAPI'
import { wait } from '@jl-org/tool'

const prefix = "/pic-extend"

export enum TASK_STATUS {
  PENDING = "PENDING",
  FINISHED = "FINISHED",
  FAILED = "FAILED",
}

export interface TaskResult {
  taskStatus: TASK_STATUS
  resultList: string[]
}

export const API_PHOTOE = {
  /**
   * @deprecated 换成 pollingUpload + getUploadTaskStatus
   * 兔展的老上传接口
   */
  async upload(b64: string): Promise<{ fileUrl: string }> {
    return request.post(`${prefix}/upload-image`, { base64Image: b64 })
  },

  async runAgent(
    agentParams: Record<string, any>,
    jobType: number
  ): Promise<string> {
    return request.post(`${prefix}/run-agent`, {
      jobType,
      elementId: 0,
      drawTaskId: 0,
      agentId: 0,
      agentParams: JSON.stringify(agentParams),
    })
  },

  /**
   * @deprecated 换成 API_RESOURCE.getListFour
   */
  async getTaskResult(historyId: string): Promise<TaskResult> {
    return request.get(`${prefix}/task-result?historyId=${historyId}`)
  },
  // 兔展的老上传接口 ============================================


  /**
   * 轮询上传抠图，返回任务 ID
   */
  async pollingUpload(imageUrl: string): Promise<{
    taskId: string
    status: Status
  }> {
    return request.post('pic-extend/exact-upload-image', { imageUrl })
  },
  /**
   * 查询抠图任务结果状态
   */
  async getUploadTaskStatus(taskId: string): Promise<{
    resultList: string[]
    status: Status
  }> {
    return request.get('pic-extend/take-exact-image-result', {
      params: {
        taskId,
      },
    })
  },

  /**
   * 获取图片详情
   */
  async getImgDetail(id: string): Promise<PhotoEImgDetail> {
    return request.get(`/pic-extend/related-detail/${id}`)
  }
}

export async function uploadCutImg(
  imageUrl: string,
  onDone?: VoidFunction,
  onFail?: VoidFunction
) {
  const { taskId } = await API_PHOTOE.pollingUpload(imageUrl)

  return new Promise<string>((resolve, reject) => {
    let timer: number
    const run = async () => {
      const { resultList, status } = await API_PHOTOE.getUploadTaskStatus(taskId)
      if (status === Status.FINISHED) {
        onDone?.()
        resolve(resultList[0])
        window.clearInterval(timer)
      }
      else if (status === Status.FAILED) {
        window.clearInterval(timer)
        onFail?.()
        reject()
      }
    }

    timer = window.setInterval(run, 3000)
  })
}


export enum Status {
  PENDING = 0,
  FINISHED = 1,
  FAILED = 2,
}

export type PhotoEImgDetail = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  previewUrl: string
  watermarkUrl: string
  url: string
  status: number
  source: string
  width: number
  height: number
  batchId: number
  itemUrl: string
  bgResourceUrl: string
  email: string
}
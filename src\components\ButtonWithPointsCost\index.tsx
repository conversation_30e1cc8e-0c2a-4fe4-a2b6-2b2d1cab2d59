import type { ButtonProps } from 'antd'
import type { FC } from 'react'
import SvgIcon from '@/components/SvgIcon'
import { god } from '@/god'
import { useT } from '@/hooks'

import { updateUserInfo, userStore } from '@/store/userStore'
import { Button, Flex } from 'antd'
import { useCallback, useMemo } from 'react'
import { useSnapshot } from 'valtio'
import styles from './index.module.less'

interface Props extends Omit<ButtonProps, 'onClick'> {
  cost: number
  onClick?: (
    e: Parameters<Required<ButtonProps>['onClick']>[0],
    credits: number,
  ) => Promise<boolean>
}

const ButtonWithPointsCost: FC<Props> = ({
  children,
  cost,
  onClick,
  htmlType,
  ...restButtonProps
}) => {
  const t = useT()

  const totalCredits = useSnapshot(userStore).userinfo.totalCredits

  const insufficientCredits = useMemo(() => {
    return !totalCredits || totalCredits < cost
  }, [totalCredits, cost])

  const handleClick = useCallback(
    async (e: Parameters<Required<ButtonProps>['onClick']>[0]) => {
      if (insufficientCredits) {
        god.messageError(t('layout.insufficient-points'))
        return
      }
      if (onClick) {
        onClick(e, totalCredits).then((success: boolean) => {
          if (success) {
            updateUserInfo()
          }
        })
      }
    },
    [onClick, insufficientCredits, t, totalCredits],
  )

  return (
    <Button
      type="primary"
      block
      size="large"
      { ...restButtonProps }
      onClick={ handleClick }
      htmlType={ !insufficientCredits
        ? htmlType
        : undefined }
    >
      {children}
      {cost > 0 && (
        <Flex className={ styles.cost } align="center" gap={ 4 }>
          <SvgIcon icon="pricing" noFill style={ { width: 12, marginTop: -1 } } />
          {t('models.cost-points', { cost })}
        </Flex>
      )}
    </Button>
  )
}
export default memo(ButtonWithPointsCost)

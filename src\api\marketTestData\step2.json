{"allJson": [{"executionId": "1942188802877558785", "processInstanceId": "1942185758874013696", "__internal__event": "meta"}, {"content": "```", "node_title": "brand_visualization", "node_seq_id": "0", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "html\n<div style", "node_title": "brand_visualization", "node_seq_id": "1", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"max-width:750px; margin:0 auto", "node_title": "brand_visualization", "node_seq_id": "2", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding:0 30", "node_title": "brand_visualization", "node_seq_id": "3", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; font-family:'PingF", "node_title": "brand_visualization", "node_seq_id": "4", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "ang <PERSON>','Microsoft YaHei',s", "node_title": "brand_visualization", "node_seq_id": "5", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "ans-serif; color:#333;\">\n  <!--", "node_title": "brand_visualization", "node_seq_id": "6", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 标题部分 -->\n  <div style", "node_title": "brand_visualization", "node_seq_id": "7", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"text-align:center; margin-bottom", "node_title": "brand_visualization", "node_seq_id": "8", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":40px;\">\n    <", "node_title": "brand_visualization", "node_seq_id": "9", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "h1 style=\"font-size:36", "node_title": "brand_visualization", "node_seq_id": "10", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; font-weight:600;", "node_title": "brand_visualization", "node_seq_id": "11", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#d4237a; margin-bottom:", "node_title": "brand_visualization", "node_seq_id": "12", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px;\">《卡姿", "node_title": "brand_visualization", "node_seq_id": "13", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "兰品牌现状及竞争分析报告》</h", "node_title": "brand_visualization", "node_seq_id": "14", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1>\n    <p style=\"", "node_title": "brand_visualization", "node_seq_id": "15", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:16px; color", "node_title": "brand_visualization", "node_seq_id": "16", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#666;\">中国彩妆市场", "node_title": "brand_visualization", "node_seq_id": "17", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "深度分析 | 2024年最新", "node_title": "brand_visualization", "node_seq_id": "18", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "数据</p>\n  </", "node_title": "brand_visualization", "node_seq_id": "19", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n\n  <!-- 市场定位与目标客户", "node_title": "brand_visualization", "node_seq_id": "20", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " -->\n  <div style=\"", "node_title": "brand_visualization", "node_seq_id": "21", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "background:#f9f3f", "node_title": "brand_visualization", "node_seq_id": "22", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "7; padding:25px; border-radius:", "node_title": "brand_visualization", "node_seq_id": "23", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; margin-bottom:30px", "node_title": "brand_visualization", "node_seq_id": "24", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n    <h2 style=\"", "node_title": "brand_visualization", "node_seq_id": "25", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:28px; color:#d", "node_title": "brand_visualization", "node_seq_id": "26", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "4237a; margin-bottom:20px", "node_title": "brand_visualization", "node_seq_id": "27", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-bottom:2px solid #", "node_title": "brand_visualization", "node_seq_id": "28", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "d4237a", "node_title": "brand_visualization", "node_seq_id": "29", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding-bottom:10px;\">一", "node_title": "brand_visualization", "node_seq_id": "30", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "、市场定位与目标客户</", "node_title": "brand_visualization", "node_seq_id": "31", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "h2>\n    \n    <div style=\"", "node_title": "brand_visualization", "node_seq_id": "32", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "display:flex; margin-bottom", "node_title": "brand_visualization", "node_seq_id": "33", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px;\">\n      <div style=\"flex", "node_title": "brand_visualization", "node_seq_id": "34", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":1; padding-right", "node_title": "brand_visualization", "node_seq_id": "35", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":15px;\">\n        <h3 style=\"font", "node_title": "brand_visualization", "node_seq_id": "36", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:22px;", "node_title": "brand_visualization", "node_seq_id": "37", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#333; margin-bottom:15px;\">1.1 品牌定位</h3", "node_title": "brand_visualization", "node_seq_id": "38", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n        <p style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "39", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":16px; line-height:", "node_title": "brand_visualization", "node_seq_id": "40", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1.8; margin-bottom:15px;\">\"中国时尚", "node_title": "brand_visualization", "node_seq_id": "41", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "彩妆领导品牌\"——卡姿", "node_title": "brand_visualization", "node_seq_id": "42", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "兰自2001年创立以来，", "node_title": "brand_visualization", "node_seq_id": "43", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "始终聚焦大众价位段（单品", "node_title": "brand_visualization", "node_seq_id": "44", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "主力价位 ¥90 – ¥", "node_title": "brand_visualization", "node_seq_id": "45", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "200），以\"活出经典 Living Classic\"为", "node_title": "brand_visualization", "node_seq_id": "46", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "核心理念。</p", "node_title": "brand_visualization", "node_seq_id": "47", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n        <p style=\"font-size:16", "node_title": "brand_visualization", "node_seq_id": "48", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; line-height:1.8;", "node_title": "brand_visualization", "node_seq_id": "49", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " background:#fff; padding:15px", "node_title": "brand_visualization", "node_seq_id": "50", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-radius:8", "node_title": "brand_visualization", "node_seq_id": "51", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; border-left:4px solid #d423", "node_title": "brand_visualization", "node_seq_id": "52", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "7a;\">\n          <strong>", "node_title": "brand_visualization", "node_seq_id": "53", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "关键数据：", "node_title": "brand_visualization", "node_seq_id": "54", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</strong>最新一整年线上销售额21.", "node_title": "brand_visualization", "node_seq_id": "55", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "79亿元、市场份额2.", "node_title": "brand_visualization", "node_seq_id": "56", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2%，位列全网TOP3\n        </p", "node_title": "brand_visualization", "node_seq_id": "57", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n      </div>\n      <div", "node_title": "brand_visualization", "node_seq_id": "58", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"flex:1; padding", "node_title": "brand_visualization", "node_seq_id": "59", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-left:15px;\">\n        <", "node_title": "brand_visualization", "node_seq_id": "60", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "h3 style=\"font-size:22px", "node_title": "brand_visualization", "node_seq_id": "61", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#333; margin-bottom", "node_title": "brand_visualization", "node_seq_id": "62", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":15px;\">1.2 目标", "node_title": "brand_visualization", "node_seq_id": "63", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "客群</h3>\n        <div", "node_title": "brand_visualization", "node_seq_id": "64", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"background:#fff", "node_title": "brand_visualization", "node_seq_id": "65", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding:15px; border-radius", "node_title": "brand_visualization", "node_seq_id": "66", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":8px;\">\n          <div style=\"display", "node_title": "brand_visualization", "node_seq_id": "67", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex; margin-bottom:10px", "node_title": "brand_visualization", "node_seq_id": "68", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-bottom:1px dashed #eee", "node_title": "brand_visualization", "node_seq_id": "69", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding-bottom:10px", "node_title": "brand_visualization", "node_seq_id": "70", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n            <div style=\"width:80px", "node_title": "brand_visualization", "node_seq_id": "71", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-weight", "node_title": "brand_visualization", "node_seq_id": "72", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":bold;\">年龄</div>\n            <", "node_title": "brand_visualization", "node_seq_id": "73", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>18-35", "node_title": "brand_visualization", "node_seq_id": "74", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "岁为核心，Z世代≈55%</div>\n", "node_title": "brand_visualization", "node_seq_id": "75", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          </div>\n          <div style", "node_title": "brand_visualization", "node_seq_id": "76", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"display:flex; margin-bottom:", "node_title": "brand_visualization", "node_seq_id": "77", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "10px; border-bottom:", "node_title": "brand_visualization", "node_seq_id": "78", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1px dashed #eee; padding-bottom:10", "node_title": "brand_visualization", "node_seq_id": "79", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px", "node_title": "brand_visualization", "node_seq_id": "80", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n            <div style=\"width:80px;", "node_title": "brand_visualization", "node_seq_id": "81", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-weight:bold;\">收入</div", "node_title": "brand_visualization", "node_seq_id": "82", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n            <div>月入4", "node_title": "brand_visualization", "node_seq_id": "83", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "k-10k元；二", "node_title": "brand_visualization", "node_seq_id": "84", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "线及以上城市为主</div>\n         ", "node_title": "brand_visualization", "node_seq_id": "85", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n          <div style=\"display", "node_title": "brand_visualization", "node_seq_id": "86", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex;\">\n            <", "node_title": "brand_visualization", "node_seq_id": "87", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"width:80px; font-weight:", "node_title": "brand_visualization", "node_seq_id": "88", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "bold;\">场景</div>\n            <", "node_title": "brand_visualization", "node_seq_id": "89", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>通勤妆", "node_title": "brand_visualization", "node_seq_id": "90", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "、社交场景妆、直播间\"种草\"</div", "node_title": "brand_visualization", "node_seq_id": "91", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n          </div>\n        </div", "node_title": "brand_visualization", "node_seq_id": "92", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n      </div>\n    </", "node_title": "brand_visualization", "node_seq_id": "93", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n  </div>\n\n", "node_title": "brand_visualization", "node_seq_id": "94", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "  <!-- 竞争对手分析 -->\n  <div", "node_title": "brand_visualization", "node_seq_id": "95", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"background:#f5f", "node_title": "brand_visualization", "node_seq_id": "96", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "7fa; padding:25px; border-radius:", "node_title": "brand_visualization", "node_seq_id": "97", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; margin-bottom:30px", "node_title": "brand_visualization", "node_seq_id": "98", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n    <h2", "node_title": "brand_visualization", "node_seq_id": "99", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:28px; color:#2", "node_title": "brand_visualization", "node_seq_id": "100", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "a5caa; margin-bottom", "node_title": "brand_visualization", "node_seq_id": "101", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; border-bottom:2", "node_title": "brand_visualization", "node_seq_id": "102", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px solid #2a5caa", "node_title": "brand_visualization", "node_seq_id": "103", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding-bottom:10px;\">", "node_title": "brand_visualization", "node_seq_id": "104", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "二、竞争对手分析</h2>\n    \n   ", "node_title": "brand_visualization", "node_seq_id": "105", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"margin-bottom", "node_title": "brand_visualization", "node_seq_id": "106", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":25px;\">\n      <h3 style=\"", "node_title": "brand_visualization", "node_seq_id": "107", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:22px;", "node_title": "brand_visualization", "node_seq_id": "108", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#333; margin-bottom:15px", "node_title": "brand_visualization", "node_seq_id": "109", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">2.1 主要", "node_title": "brand_visualization", "node_seq_id": "110", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "竞争者及座次</h3>\n      <", "node_title": "brand_visualization", "node_seq_id": "111", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"overflow-x:auto;\">\n       ", "node_title": "brand_visualization", "node_seq_id": "112", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <table style=\"width:100%;", "node_title": "brand_visualization", "node_seq_id": "113", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-collapse:collapse;", "node_title": "brand_visualization", "node_seq_id": "114", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " background:#fff; border-radius:", "node_title": "brand_visualization", "node_seq_id": "115", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "8px;\">\n          <thead>\n            <tr style", "node_title": "brand_visualization", "node_seq_id": "116", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"background:#2a5caa", "node_title": "brand_visualization", "node_seq_id": "117", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#fff;\">\n", "node_title": "brand_visualization", "node_seq_id": "118", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "              <th style=\"padding:12", "node_title": "brand_visualization", "node_seq_id": "119", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; text-align:left;\">阵营", "node_title": "brand_visualization", "node_seq_id": "120", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</th>\n              <th style=\"padding:", "node_title": "brand_visualization", "node_seq_id": "121", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; text-align:left;\">代表", "node_title": "brand_visualization", "node_seq_id": "122", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "品牌</th>\n              <", "node_title": "brand_visualization", "node_seq_id": "123", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "th style=\"padding:12px;", "node_title": "brand_visualization", "node_seq_id": "124", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " text-align:right;\">销售额/份额</th>\n            </tr>\n          </", "node_title": "brand_visualization", "node_seq_id": "125", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "thead>\n          <tbody>\n            <tr", "node_title": "brand_visualization", "node_seq_id": "126", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"border-bottom:", "node_title": "brand_visualization", "node_seq_id": "127", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1px solid #eee;\">\n              <td", "node_title": "brand_visualization", "node_seq_id": "128", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"padding:12px;", "node_title": "brand_visualization", "node_seq_id": "129", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-weight:bold;\">国货领跑</", "node_title": "brand_visualization", "node_seq_id": "130", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td>\n              <td style=\"padding:", "node_title": "brand_visualization", "node_seq_id": "131", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px;\">花西", "node_title": "brand_visualization", "node_seq_id": "132", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "子</td>\n              <td style=\"padding", "node_title": "brand_visualization", "node_seq_id": "133", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":12px", "node_title": "brand_visualization", "node_seq_id": "134", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; text-align:right;\">¥23.40", "node_title": "brand_visualization", "node_seq_id": "135", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "亿/2.4", "node_title": "brand_visualization", "node_seq_id": "136", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "%</td>\n            </tr>\n           ", "node_title": "brand_visualization", "node_seq_id": "137", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <tr style=\"border-bottom:1px solid", "node_title": "brand_visualization", "node_seq_id": "138", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " #eee;\">\n              <", "node_title": "brand_visualization", "node_seq_id": "139", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td style=\"padding:12px;\"></td>\n", "node_title": "brand_visualization", "node_seq_id": "140", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "              <td style=\"padding:", "node_title": "brand_visualization", "node_seq_id": "141", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; font-weight:bold", "node_title": "brand_visualization", "node_seq_id": "142", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">卡姿兰</td>\n              <td", "node_title": "brand_visualization", "node_seq_id": "143", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"padding:12px;", "node_title": "brand_visualization", "node_seq_id": "144", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " text-align:right;\">¥21.79亿", "node_title": "brand_visualization", "node_seq_id": "145", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "/2.2%", "node_title": "brand_visualization", "node_seq_id": "146", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</td>\n            </tr>\n            <", "node_title": "brand_visualization", "node_seq_id": "147", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "tr style=\"border-bottom:1", "node_title": "brand_visualization", "node_seq_id": "148", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px solid #eee;\">\n              <td style", "node_title": "brand_visualization", "node_seq_id": "149", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"padding:12px;\">国际高端</", "node_title": "brand_visualization", "node_seq_id": "150", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td>\n              <td style=\"", "node_title": "brand_visualization", "node_seq_id": "151", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "padding:12px;\">YSL Beauty</", "node_title": "brand_visualization", "node_seq_id": "152", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td>\n              <td style=\"padding:", "node_title": "brand_visualization", "node_seq_id": "153", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; text-align:", "node_title": "brand_visualization", "node_seq_id": "154", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "right;\">¥27.80亿", "node_title": "brand_visualization", "node_seq_id": "155", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "/2.8%</td>\n            </tr", "node_title": "brand_visualization", "node_seq_id": "156", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n          </tbody>\n        </", "node_title": "brand_visualization", "node_seq_id": "157", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "table>\n      </div>\n   ", "node_title": "brand_visualization", "node_seq_id": "158", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n    \n    <div>\n      <h3 style", "node_title": "brand_visualization", "node_seq_id": "159", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:22px;", "node_title": "brand_visualization", "node_seq_id": "160", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#333; margin-bottom:15px;\">", "node_title": "brand_visualization", "node_seq_id": "161", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2.2 SWOT 摘要</", "node_title": "brand_visualization", "node_seq_id": "162", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "h3>\n      <div style=\"display", "node_title": "brand_visualization", "node_seq_id": "163", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex; flex-wrap", "node_title": "brand_visualization", "node_seq_id": "164", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":wrap; gap:15px;\">\n       ", "node_title": "brand_visualization", "node_seq_id": "165", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"flex:1; min", "node_title": "brand_visualization", "node_seq_id": "166", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-width:250px; background:#fff;", "node_title": "brand_visualization", "node_seq_id": "167", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " padding:15px; border-radius", "node_title": "brand_visualization", "node_seq_id": "168", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":8px; border-top", "node_title": "brand_visualization", "node_seq_id": "169", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":4px solid #4caf50;\">\n", "node_title": "brand_visualization", "node_seq_id": "170", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <h4 style=\"", "node_title": "brand_visualization", "node_seq_id": "171", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:18px; color:#4", "node_title": "brand_visualization", "node_seq_id": "172", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "caf50;", "node_title": "brand_visualization", "node_seq_id": "173", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " margin-bottom:10px;\">优势", "node_title": "brand_visualization", "node_seq_id": "174", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "(Strength)</h4>\n          <p", "node_title": "brand_visualization", "node_seq_id": "175", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:15px;", "node_title": "brand_visualization", "node_seq_id": "176", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " line-height:1.6;\">底妆产品", "node_title": "brand_visualization", "node_seq_id": "177", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "矩阵齐全；大众价位段拥有统治", "node_title": "brand_visualization", "node_seq_id": "178", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "级份额；供应链自有", "node_title": "brand_visualization", "node_seq_id": "179", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "工厂保障成本。</p>\n        </div>\n       ", "node_title": "brand_visualization", "node_seq_id": "180", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"flex:", "node_title": "brand_visualization", "node_seq_id": "181", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1; min-width:250px;", "node_title": "brand_visualization", "node_seq_id": "182", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " background:#fff; padding:15px; border", "node_title": "brand_visualization", "node_seq_id": "183", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:8px; border-top", "node_title": "brand_visualization", "node_seq_id": "184", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":4px solid #ff5722;\">\n", "node_title": "brand_visualization", "node_seq_id": "185", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <h4 style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "186", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":18px; color:#ff", "node_title": "brand_visualization", "node_seq_id": "187", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "5722; margin-bottom:10px;\">", "node_title": "brand_visualization", "node_seq_id": "188", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "劣势(Weakness)</h", "node_title": "brand_visualization", "node_seq_id": "189", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "4>\n          <p style=\"", "node_title": "brand_visualization", "node_seq_id": "190", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:15px; line-height:1", "node_title": "brand_visualization", "node_seq_id": "191", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ".6;\">国际高端心智仍弱；", "node_title": "brand_visualization", "node_seq_id": "192", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "彩妆科研话语", "node_title": "brand_visualization", "node_seq_id": "193", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "权不足；新品叙事需更深度", "node_title": "brand_visualization", "node_seq_id": "194", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "。</p>\n        </div", "node_title": "brand_visualization", "node_seq_id": "195", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n        <div style=\"flex:1;", "node_title": "brand_visualization", "node_seq_id": "196", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " min-width:250px; background", "node_title": "brand_visualization", "node_seq_id": "197", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#fff; padding:", "node_title": "brand_visualization", "node_seq_id": "198", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px; border-radius:8px", "node_title": "brand_visualization", "node_seq_id": "199", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-top:4px", "node_title": "brand_visualization", "node_seq_id": "200", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " solid #2196f3;\">\n         ", "node_title": "brand_visualization", "node_seq_id": "201", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <h4 style=\"font-size:18px", "node_title": "brand_visualization", "node_seq_id": "202", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#2196", "node_title": "brand_visualization", "node_seq_id": "203", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "f3; margin-bottom:10px", "node_title": "brand_visualization", "node_seq_id": "204", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">机会(Opportunity)</h4>\n", "node_title": "brand_visualization", "node_seq_id": "205", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <p style=\"font-size:15px", "node_title": "brand_visualization", "node_seq_id": "206", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; line-height:1.6", "node_title": "brand_visualization", "node_seq_id": "207", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">社媒增量在抖音，国货", "node_title": "brand_visualization", "node_seq_id": "208", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "美妆政策红利；男士", "node_title": "brand_visualization", "node_seq_id": "209", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "彩妆蓝海。</p>\n", "node_title": "brand_visualization", "node_seq_id": "210", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        </div>\n        <div style=\"flex:", "node_title": "brand_visualization", "node_seq_id": "211", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1; min-width:250px;", "node_title": "brand_visualization", "node_seq_id": "212", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " background:#fff; padding:15", "node_title": "brand_visualization", "node_seq_id": "213", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; border-radius:8px; border", "node_title": "brand_visualization", "node_seq_id": "214", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-top:4px solid #9c27b0;\">\n          <h", "node_title": "brand_visualization", "node_seq_id": "215", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "4 style=\"font-size:18px", "node_title": "brand_visualization", "node_seq_id": "216", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#9c27b", "node_title": "brand_visualization", "node_seq_id": "217", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0; margin-bottom:10px;\">", "node_title": "brand_visualization", "node_seq_id": "218", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "威胁(Threat)</h4>\n          <", "node_title": "brand_visualization", "node_seq_id": "219", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p style=\"font-size:15px", "node_title": "brand_visualization", "node_seq_id": "220", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; line-height:1.", "node_title": "brand_visualization", "node_seq_id": "221", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6;\">国际大牌向下探价", "node_title": "brand_visualization", "node_seq_id": "222", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " + 新锐国货持续上新；", "node_title": "brand_visualization", "node_seq_id": "223", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "护肤科技融入彩妆后技术门槛抬", "node_title": "brand_visualization", "node_seq_id": "224", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "升。</p>\n", "node_title": "brand_visualization", "node_seq_id": "225", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        </div>\n      </div>\n    </", "node_title": "brand_visualization", "node_seq_id": "226", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n  </div>\n\n ", "node_title": "brand_visualization", "node_seq_id": "227", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <!-- 波特五力模型 -->\n ", "node_title": "brand_visualization", "node_seq_id": "228", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"background:#f1f8e", "node_title": "brand_visualization", "node_seq_id": "229", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "9; padding:25px;", "node_title": "brand_visualization", "node_seq_id": "230", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:12px;", "node_title": "brand_visualization", "node_seq_id": "231", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " margin-bottom:30px;\">\n    <h2 style", "node_title": "brand_visualization", "node_seq_id": "232", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:28px;", "node_title": "brand_visualization", "node_seq_id": "233", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#4caf50; margin-bottom", "node_title": "brand_visualization", "node_seq_id": "234", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; border-bottom:2", "node_title": "brand_visualization", "node_seq_id": "235", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px solid #4caf50;", "node_title": "brand_visualization", "node_seq_id": "236", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " padding-bottom:", "node_title": "brand_visualization", "node_seq_id": "237", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "10px;\">三、波特五力模型", "node_title": "brand_visualization", "node_seq_id": "238", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</h2>\n    \n   ", "node_title": "brand_visualization", "node_seq_id": "239", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"overflow-x:auto;\">\n     ", "node_title": "brand_visualization", "node_seq_id": "240", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <table style=\"width", "node_title": "brand_visualization", "node_seq_id": "241", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":100%; border-collapse:collapse; background:#", "node_title": "brand_visualization", "node_seq_id": "242", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "fff; border-radius:8px;\">\n", "node_title": "brand_visualization", "node_seq_id": "243", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        <thead>\n         ", "node_title": "brand_visualization", "node_seq_id": "244", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <tr style=\"background:#4caf50;", "node_title": "brand_visualization", "node_seq_id": "245", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#fff;\">\n            <th style", "node_title": "brand_visualization", "node_seq_id": "246", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"padding:12px", "node_title": "brand_visualization", "node_seq_id": "247", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; text-align:left;\">力量</", "node_title": "brand_visualization", "node_seq_id": "248", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "th>\n            <th style=\"padding:", "node_title": "brand_visualization", "node_seq_id": "249", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px;", "node_title": "brand_visualization", "node_seq_id": "250", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " text-align:center;\">强度</th", "node_title": "brand_visualization", "node_seq_id": "251", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n            <th style=\"padding:12px;", "node_title": "brand_visualization", "node_seq_id": "252", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " text-align:left;\">原因 &", "node_title": "brand_visualization", "node_seq_id": "253", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 说明</th>\n          </tr", "node_title": "brand_visualization", "node_seq_id": "254", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n        </thead>\n        <", "node_title": "brand_visualization", "node_seq_id": "255", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "tbody>\n          <tr style=\"border", "node_title": "brand_visualization", "node_seq_id": "256", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:1px solid #eee", "node_title": "brand_visualization", "node_seq_id": "257", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n            <td style=\"padding:12", "node_title": "brand_visualization", "node_seq_id": "258", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; font-weight:bold;\">供应商议价", "node_title": "brand_visualization", "node_seq_id": "259", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "力</td>\n            <", "node_title": "brand_visualization", "node_seq_id": "260", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td style=\"padding:12px", "node_title": "brand_visualization", "node_seq_id": "261", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; text-align:center;\">中-</td>\n", "node_title": "brand_visualization", "node_seq_id": "262", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "            <td style=\"padding", "node_title": "brand_visualization", "node_seq_id": "263", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":12px;\">彩妆包材与原料", "node_title": "brand_visualization", "node_seq_id": "264", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "日趋标准化；", "node_title": "brand_visualization", "node_seq_id": "265", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "但高端粉体/香精仍掌握", "node_title": "brand_visualization", "node_seq_id": "266", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "在日韩/欧洲供应商手中</", "node_title": "brand_visualization", "node_seq_id": "267", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td>\n          </tr>\n         ", "node_title": "brand_visualization", "node_seq_id": "268", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <tr style=\"border-bottom:", "node_title": "brand_visualization", "node_seq_id": "269", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1px solid #eee;\">\n           ", "node_title": "brand_visualization", "node_seq_id": "270", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <td style=\"padding:12px", "node_title": "brand_visualization", "node_seq_id": "271", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; font-weight:bold;\">买方议价力", "node_title": "brand_visualization", "node_seq_id": "272", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</td>\n            <td", "node_title": "brand_visualization", "node_seq_id": "273", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"padding:12px; text", "node_title": "brand_visualization", "node_seq_id": "274", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-align:center;\">高</td>\n            <td", "node_title": "brand_visualization", "node_seq_id": "275", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style", "node_title": "brand_visualization", "node_seq_id": "276", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"padding:12px;\">消费者信息透明", "node_title": "brand_visualization", "node_seq_id": "277", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "、比价心态强；直播间", "node_title": "brand_visualization", "node_seq_id": "278", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"低价心智\"强化</td>\n          </", "node_title": "brand_visualization", "node_seq_id": "279", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "tr>\n          <tr style=\"border-bottom", "node_title": "brand_visualization", "node_seq_id": "280", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":1px solid #", "node_title": "brand_visualization", "node_seq_id": "281", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "eee;\">\n            <td style=\"padding", "node_title": "brand_visualization", "node_seq_id": "282", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":12px; font-weight:bold", "node_title": "brand_visualization", "node_seq_id": "283", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">新进入者威胁</td>\n            <td", "node_title": "brand_visualization", "node_seq_id": "284", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"padding:12", "node_title": "brand_visualization", "node_seq_id": "285", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; text-align:center;\">中+</td", "node_title": "brand_visualization", "node_seq_id": "286", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n            <td style=\"padding:", "node_title": "brand_visualization", "node_seq_id": "287", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px;\">直播渠道降低", "node_title": "brand_visualization", "node_seq_id": "288", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "流量门槛；但体系化底妆研发 &", "node_title": "brand_visualization", "node_seq_id": "289", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 线下渠道铺设需重投入</", "node_title": "brand_visualization", "node_seq_id": "290", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td>\n          </tr>\n          <", "node_title": "brand_visualization", "node_seq_id": "291", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "tr style=\"border-bottom:1px", "node_title": "brand_visualization", "node_seq_id": "292", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " solid #eee;\">\n            <", "node_title": "brand_visualization", "node_seq_id": "293", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td style=\"padding:12px; font-weight", "node_title": "brand_visualization", "node_seq_id": "294", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":bold;\">替代品威胁</td", "node_title": "brand_visualization", "node_seq_id": "295", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n            <td style=\"padding:", "node_title": "brand_visualization", "node_seq_id": "296", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; text-align:center;\">", "node_title": "brand_visualization", "node_seq_id": "297", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "中</td>\n            <", "node_title": "brand_visualization", "node_seq_id": "298", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td style=\"padding:12px;\">半", "node_title": "brand_visualization", "node_seq_id": "299", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "永久美妆、医美", "node_title": "brand_visualization", "node_seq_id": "300", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"轻改颜\"对日常彩妆", "node_title": "brand_visualization", "node_seq_id": "301", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "替代度上升</td>\n          </", "node_title": "brand_visualization", "node_seq_id": "302", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "tr>\n          <tr>\n           ", "node_title": "brand_visualization", "node_seq_id": "303", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <td style=\"padding:12px; font-weight", "node_title": "brand_visualization", "node_seq_id": "304", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":bold;\">行业竞争强度</", "node_title": "brand_visualization", "node_seq_id": "305", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td>\n            <td style=\"padding:", "node_title": "brand_visualization", "node_seq_id": "306", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; text-align:center;\">", "node_title": "brand_visualization", "node_seq_id": "307", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "高</td>\n            <td style=\"padding:12px;\">3000", "node_title": "brand_visualization", "node_seq_id": "308", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "+ 活跃彩妆注册品牌", "node_title": "brand_visualization", "node_seq_id": "309", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "；新品更替周期 ≈", "node_title": "brand_visualization", "node_seq_id": "310", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 3-6 个月</td>\n          </", "node_title": "brand_visualization", "node_seq_id": "311", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "tr>\n        </tbody", "node_title": "brand_visualization", "node_seq_id": "312", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n      </table>\n    </", "node_title": "brand_visualization", "node_seq_id": "313", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n    \n    <div style", "node_title": "brand_visualization", "node_seq_id": "314", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"background:#fff; margin-top", "node_title": "brand_visualization", "node_seq_id": "315", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; padding:20", "node_title": "brand_visualization", "node_seq_id": "316", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; border-radius:8px; border-left:", "node_title": "brand_visualization", "node_seq_id": "317", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "4px solid #4caf50", "node_title": "brand_visualization", "node_seq_id": "318", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n      <p style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "319", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":16px; line-height:1", "node_title": "brand_visualization", "node_seq_id": "320", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ".7; margin:0", "node_title": "brand_visualization", "node_seq_id": "321", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n        <strong>结论：</strong>", "node_title": "brand_visualization", "node_seq_id": "322", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "竞争激烈、买方强势，品牌若无", "node_title": "brand_visualization", "node_seq_id": "323", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "技术型差异化/社群心智，容易", "node_title": "brand_visualization", "node_seq_id": "324", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "陷入价格战。卡姿", "node_title": "brand_visualization", "node_seq_id": "325", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "兰的底妆技术与价格带双护", "node_title": "brand_visualization", "node_seq_id": "326", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "城河目前仍然稳固。\n      </", "node_title": "brand_visualization", "node_seq_id": "327", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p>\n    </div>\n  </div", "node_title": "brand_visualization", "node_seq_id": "328", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n\n  <!-- 产品与服务 -->\n ", "node_title": "brand_visualization", "node_seq_id": "329", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"background:#", "node_title": "brand_visualization", "node_seq_id": "330", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "e8f4f8; padding", "node_title": "brand_visualization", "node_seq_id": "331", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":25px; border-radius:12px", "node_title": "brand_visualization", "node_seq_id": "332", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:30px", "node_title": "brand_visualization", "node_seq_id": "333", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n    <h2 style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "334", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":28px; color:#2196", "node_title": "brand_visualization", "node_seq_id": "335", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "f3; margin-bottom:20", "node_title": "brand_visualization", "node_seq_id": "336", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; border-bottom:2px solid", "node_title": "brand_visualization", "node_seq_id": "337", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " #2196f3; padding-bottom", "node_title": "brand_visualization", "node_seq_id": "338", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":10px;\">四、产品与服务</h2", "node_title": "brand_visualization", "node_seq_id": "339", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n    \n    <div", "node_title": "brand_visualization", "node_seq_id": "340", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"margin-bottom:25px;\">\n     ", "node_title": "brand_visualization", "node_seq_id": "341", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <h3 style=\"font-size:22px", "node_title": "brand_visualization", "node_seq_id": "342", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#333; margin", "node_title": "brand_visualization", "node_seq_id": "343", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:15px;\">4.1", "node_title": "brand_visualization", "node_seq_id": "344", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 产品族谱</h3>\n      <", "node_title": "brand_visualization", "node_seq_id": "345", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"display:flex; flex", "node_title": "brand_visualization", "node_seq_id": "346", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-wrap:wrap; gap:", "node_title": "brand_visualization", "node_seq_id": "347", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px;\">\n        <div style", "node_title": "brand_visualization", "node_seq_id": "348", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"flex:1; min-width:250", "node_title": "brand_visualization", "node_seq_id": "349", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; background:#fff; padding", "node_title": "brand_visualization", "node_seq_id": "350", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px;", "node_title": "brand_visualization", "node_seq_id": "351", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:8px; box-shadow:", "node_title": "brand_visualization", "node_seq_id": "352", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0 2px 8px rgba", "node_title": "brand_visualization", "node_seq_id": "353", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "(0,0,0,0.05", "node_title": "brand_visualization", "node_seq_id": "354", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ");\">\n          <div style", "node_title": "brand_visualization", "node_seq_id": "355", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:20px; color", "node_title": "brand_visualization", "node_seq_id": "356", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#2196f3; margin-bottom:10", "node_title": "brand_visualization", "node_seq_id": "357", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">", "node_title": "brand_visualization", "node_seq_id": "358", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "💄 底妆线</div>\n         ", "node_title": "brand_visualization", "node_seq_id": "359", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <p style=\"font-size:15", "node_title": "brand_visualization", "node_seq_id": "360", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; line-height:1.", "node_title": "brand_visualization", "node_seq_id": "361", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6; color:#666;\">", "node_title": "brand_visualization", "node_seq_id": "362", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"黑磁散粉\"\"小奶猫", "node_title": "brand_visualization", "node_seq_id": "363", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "气垫\"\"安瓶粉底液\"等", "node_title": "brand_visualization", "node_seq_id": "364", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "覆盖从学生党到", "node_title": "brand_visualization", "node_seq_id": "365", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "轻奢客群</p>\n        </", "node_title": "brand_visualization", "node_seq_id": "366", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n        <div style=\"flex:1", "node_title": "brand_visualization", "node_seq_id": "367", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; min-width:250", "node_title": "brand_visualization", "node_seq_id": "368", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; background:#fff; padding:20px;", "node_title": "brand_visualization", "node_seq_id": "369", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:8px", "node_title": "brand_visualization", "node_seq_id": "370", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; box-shadow:", "node_title": "brand_visualization", "node_seq_id": "371", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0 2px 8px rgba", "node_title": "brand_visualization", "node_seq_id": "372", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "(0,0,0,0.05);", "node_title": "brand_visualization", "node_seq_id": "373", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\">\n          <div style", "node_title": "brand_visualization", "node_seq_id": "374", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:20px; color:#", "node_title": "brand_visualization", "node_seq_id": "375", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2196f3; margin-bottom:10", "node_title": "brand_visualization", "node_seq_id": "376", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">💋 唇妆线</", "node_title": "brand_visualization", "node_seq_id": "377", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n          <p style=\"font", "node_title": "brand_visualization", "node_seq_id": "378", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:15px; line-height:", "node_title": "brand_visualization", "node_seq_id": "379", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1.6; color:#666;\">", "node_title": "brand_visualization", "node_seq_id": "380", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "主打\"低饱和显白", "node_title": "brand_visualization", "node_seq_id": "381", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"与\"护唇精华\"概念", "node_title": "brand_visualization", "node_seq_id": "382", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "，联名包装频繁</p>\n        </", "node_title": "brand_visualization", "node_seq_id": "383", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n        <div style=\"", "node_title": "brand_visualization", "node_seq_id": "384", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "flex:1; min-width:", "node_title": "brand_visualization", "node_seq_id": "385", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "250px; background:#fff; padding:20", "node_title": "brand_visualization", "node_seq_id": "386", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; border-radius:8", "node_title": "brand_visualization", "node_seq_id": "387", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; box-shadow:0", "node_title": "brand_visualization", "node_seq_id": "388", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 2px 8px", "node_title": "brand_visualization", "node_seq_id": "389", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " rgba(0,0,0,", "node_title": "brand_visualization", "node_seq_id": "390", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0.05);\">\n          <div style=\"font", "node_title": "brand_visualization", "node_seq_id": "391", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:20px; color:#219", "node_title": "brand_visualization", "node_seq_id": "392", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6f3; margin-bottom:10px", "node_title": "brand_visualization", "node_seq_id": "393", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">👁", "node_title": "brand_visualization", "node_seq_id": "394", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "️ 眼妆线</div>\n", "node_title": "brand_visualization", "node_seq_id": "395", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <p style=\"font-size:", "node_title": "brand_visualization", "node_seq_id": "396", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px; line-height:1.6", "node_title": "brand_visualization", "node_seq_id": "397", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#666;\">大眼睛睫毛膏、吸", "node_title": "brand_visualization", "node_seq_id": "398", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "睛眼影盘、", "node_title": "brand_visualization", "node_seq_id": "399", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "零触感眼线笔</p>\n       ", "node_title": "brand_visualization", "node_seq_id": "400", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n        <div style", "node_title": "brand_visualization", "node_seq_id": "401", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"flex:1; min-width:250px", "node_title": "brand_visualization", "node_seq_id": "402", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";", "node_title": "brand_visualization", "node_seq_id": "403", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " background:#fff; padding:20px; border-radius:8px;", "node_title": "brand_visualization", "node_seq_id": "404", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " box-shadow:0 2px ", "node_title": "brand_visualization", "node_seq_id": "405", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "8px rgba(0", "node_title": "brand_visualization", "node_seq_id": "406", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ",0,0,0.05", "node_title": "brand_visualization", "node_seq_id": "407", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ");\">\n          <div style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "408", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; color:#2196f3", "node_title": "brand_visualization", "node_seq_id": "409", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:10px;\">", "node_title": "brand_visualization", "node_seq_id": "410", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "🧴 护肤线</", "node_title": "brand_visualization", "node_seq_id": "411", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n          <p style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "412", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":15px; line-height:1.6; color:#666;\">202", "node_title": "brand_visualization", "node_seq_id": "413", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "5年推出动能肽面霜、", "node_title": "brand_visualization", "node_seq_id": "414", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "精华液，切入功效护肤赛道", "node_title": "brand_visualization", "node_seq_id": "415", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</p>\n        </div>\n      </", "node_title": "brand_visualization", "node_seq_id": "416", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n    </div>\n    \n    <div", "node_title": "brand_visualization", "node_seq_id": "417", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n      <h3 style=\"", "node_title": "brand_visualization", "node_seq_id": "418", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:22px; color:#333", "node_title": "brand_visualization", "node_seq_id": "419", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin", "node_title": "brand_visualization", "node_seq_id": "420", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:15px;\">4.2 产品", "node_title": "brand_visualization", "node_seq_id": "421", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "创新范式</h3>\n     ", "node_title": "brand_visualization", "node_seq_id": "422", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"background:#fff; padding", "node_title": "brand_visualization", "node_seq_id": "423", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; border-radius:", "node_title": "brand_visualization", "node_seq_id": "424", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "8px;\">\n        <div style=\"", "node_title": "brand_visualization", "node_seq_id": "425", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "display:flex", "node_title": "brand_visualization", "node_seq_id": "426", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; align-items:center; margin-bottom:15px;\">\n          <", "node_title": "brand_visualization", "node_seq_id": "427", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"width:40px", "node_title": "brand_visualization", "node_seq_id": "428", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; height:40px; background", "node_title": "brand_visualization", "node_seq_id": "429", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#e3f2fd; border-radius:", "node_title": "brand_visualization", "node_seq_id": "430", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "50%; display:flex; align-items", "node_title": "brand_visualization", "node_seq_id": "431", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":center; justify-content:center;", "node_title": "brand_visualization", "node_seq_id": "432", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " margin-right:15px; color", "node_title": "brand_visualization", "node_seq_id": "433", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#2196f3;", "node_title": "brand_visualization", "node_seq_id": "434", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-size:20px;\">1</", "node_title": "brand_visualization", "node_seq_id": "435", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n          <div style=\"flex:1;\">\n", "node_title": "brand_visualization", "node_seq_id": "436", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "           ", "node_title": "brand_visualization", "node_seq_id": "437", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"font-weight:bold", "node_title": "brand_visualization", "node_seq_id": "438", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:5px;\">\"彩妆+", "node_title": "brand_visualization", "node_seq_id": "439", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "护肤\"</div>\n            <p", "node_title": "brand_visualization", "node_seq_id": "440", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:15px", "node_title": "brand_visualization", "node_seq_id": "441", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; line-height:1.6;", "node_title": "brand_visualization", "node_seq_id": "442", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " color:#666; margin:0;\">在", "node_title": "brand_visualization", "node_seq_id": "443", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "粉底与气垫中加入胜肽", "node_title": "brand_visualization", "node_seq_id": "444", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "、烟酰胺等成分，响应\"", "node_title": "brand_visualization", "node_seq_id": "445", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "养肤底妆\"潮流</p", "node_title": "brand_visualization", "node_seq_id": "446", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n          </div>\n        </div", "node_title": "brand_visualization", "node_seq_id": "447", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n        <div style=\"display", "node_title": "brand_visualization", "node_seq_id": "448", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex; align-items:", "node_title": "brand_visualization", "node_seq_id": "449", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "center; margin-bottom:15px;\">\n          <", "node_title": "brand_visualization", "node_seq_id": "450", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"width:40px;", "node_title": "brand_visualization", "node_seq_id": "451", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " height:40px; background:#e3", "node_title": "brand_visualization", "node_seq_id": "452", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "f2fd; border-radius", "node_title": "brand_visualization", "node_seq_id": "453", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":50%; display:flex; align-items", "node_title": "brand_visualization", "node_seq_id": "454", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":center; justify-content:center; margin", "node_title": "brand_visualization", "node_seq_id": "455", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-right:15px; color:#", "node_title": "brand_visualization", "node_seq_id": "456", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2196f3; font-size", "node_title": "brand_visualization", "node_seq_id": "457", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px;\">2</div>\n", "node_title": "brand_visualization", "node_seq_id": "458", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <div style=\"flex:1;\">\n", "node_title": "brand_visualization", "node_seq_id": "459", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "            <div style=\"font-weight:", "node_title": "brand_visualization", "node_seq_id": "460", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "bold; margin-bottom:5px;\">小", "node_title": "brand_visualization", "node_seq_id": "461", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "包装&多色号</div>\n", "node_title": "brand_visualization", "node_seq_id": "462", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "            <p style=\"font", "node_title": "brand_visualization", "node_seq_id": "463", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:15px; line-height:1.", "node_title": "brand_visualization", "node_seq_id": "464", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6; color:#666;", "node_title": "brand_visualization", "node_seq_id": "465", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " margin:0;\">适配\"混合猫", "node_title": "brand_visualization", "node_seq_id": "466", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"气垫可拆卸芯，", "node_title": "brand_visualization", "node_seq_id": "467", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "减少库存压力</p>\n          </", "node_title": "brand_visualization", "node_seq_id": "468", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n        </div>\n        <div style=\"", "node_title": "brand_visualization", "node_seq_id": "469", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "display:flex; align-items", "node_title": "brand_visualization", "node_seq_id": "470", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":center;\">\n          <div style=\"width", "node_title": "brand_visualization", "node_seq_id": "471", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":40px; height:40px", "node_title": "brand_visualization", "node_seq_id": "472", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; background:#e3f2fd;", "node_title": "brand_visualization", "node_seq_id": "473", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:50%; display", "node_title": "brand_visualization", "node_seq_id": "474", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex; align-items:center; justify-content", "node_title": "brand_visualization", "node_seq_id": "475", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":center; margin-right:", "node_title": "brand_visualization", "node_seq_id": "476", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "15px; color:#2196f3;", "node_title": "brand_visualization", "node_seq_id": "477", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-size:20px;\">3", "node_title": "brand_visualization", "node_seq_id": "478", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</div>\n          <div style=\"flex:1;\">\n           ", "node_title": "brand_visualization", "node_seq_id": "479", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"font-weight:", "node_title": "brand_visualization", "node_seq_id": "480", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "bold; margin-bottom:5", "node_title": "brand_visualization", "node_seq_id": "481", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">环保包装</div>\n            <p", "node_title": "brand_visualization", "node_seq_id": "482", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:15px; line", "node_title": "brand_visualization", "node_seq_id": "483", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-height:1.6; color", "node_title": "brand_visualization", "node_seq_id": "484", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#666; margin:0;\">部分新品", "node_title": "brand_visualization", "node_seq_id": "485", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "已使用PCR材料；建议", "node_title": "brand_visualization", "node_seq_id": "486", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "继续强化ESG叙事</p", "node_title": "brand_visualization", "node_seq_id": "487", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n          </div>\n        </div>\n      </", "node_title": "brand_visualization", "node_seq_id": "488", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n    </div>\n  </", "node_title": "brand_visualization", "node_seq_id": "489", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n\n  <!-- 财务", "node_title": "brand_visualization", "node_seq_id": "490", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "表现与扩张策略 -->\n  <div style=\"", "node_title": "brand_visualization", "node_seq_id": "491", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "background:#f3e5", "node_title": "brand_visualization", "node_seq_id": "492", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "f5; padding:25px", "node_title": "brand_visualization", "node_seq_id": "493", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-radius:12px; margin", "node_title": "brand_visualization", "node_seq_id": "494", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:30px;\">\n    <h2 style", "node_title": "brand_visualization", "node_seq_id": "495", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:28px; color:#", "node_title": "brand_visualization", "node_seq_id": "496", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "9c27b0; margin", "node_title": "brand_visualization", "node_seq_id": "497", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:20px; border-bottom", "node_title": "brand_visualization", "node_seq_id": "498", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":2px solid #9c27b0", "node_title": "brand_visualization", "node_seq_id": "499", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding-bottom:", "node_title": "brand_visualization", "node_seq_id": "500", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "10px;\">五、财务表现与扩张策略", "node_title": "brand_visualization", "node_seq_id": "501", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</h2>\n    \n    <div style=\"", "node_title": "brand_visualization", "node_seq_id": "502", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "margin-bottom:25px", "node_title": "brand_visualization", "node_seq_id": "503", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n      <h", "node_title": "brand_visualization", "node_seq_id": "504", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "3 style=\"font-size:22px; color:#", "node_title": "brand_visualization", "node_seq_id": "505", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "333; margin-bottom:15", "node_title": "brand_visualization", "node_seq_id": "506", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">5", "node_title": "brand_visualization", "node_seq_id": "507", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ".1 核心财务指标（公开口径）</", "node_title": "brand_visualization", "node_seq_id": "508", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "h3>\n      <div style", "node_title": "brand_visualization", "node_seq_id": "509", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"overflow-x:auto;\">\n        <", "node_title": "brand_visualization", "node_seq_id": "510", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "table style=\"width:100%; border-coll", "node_title": "brand_visualization", "node_seq_id": "511", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "apse:collapse; background:#fff;", "node_title": "brand_visualization", "node_seq_id": "512", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border", "node_title": "brand_visualization", "node_seq_id": "513", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:8px;\">\n          <thead>\n            <", "node_title": "brand_visualization", "node_seq_id": "514", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "tr style=\"background:#", "node_title": "brand_visualization", "node_seq_id": "515", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "9c27b0; color:#", "node_title": "brand_visualization", "node_seq_id": "516", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "fff;\">\n              <th style=\"", "node_title": "brand_visualization", "node_seq_id": "517", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "padding:12px; text-align:left;\">指标</th>\n", "node_title": "brand_visualization", "node_seq_id": "518", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "              <th style=\"padding:12px;", "node_title": "brand_visualization", "node_seq_id": "519", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " text-align:right;\">", "node_title": "brand_visualization", "node_seq_id": "520", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2022年</th>\n              <th style=\"", "node_title": "brand_visualization", "node_seq_id": "521", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "padding:12px", "node_title": "brand_visualization", "node_seq_id": "522", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; text-align:right;\">", "node_title": "brand_visualization", "node_seq_id": "523", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2023年</th>\n              <", "node_title": "brand_visualization", "node_seq_id": "524", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "th style=\"padding:12px; text-align", "node_title": "brand_visualization", "node_seq_id": "525", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":center;\">同比</th>\n           ", "node_title": "brand_visualization", "node_seq_id": "526", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </tr>\n          </thead>\n", "node_title": "brand_visualization", "node_seq_id": "527", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <tbody>\n            <tr style=\"", "node_title": "brand_visualization", "node_seq_id": "528", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "border-bottom:1px solid #", "node_title": "brand_visualization", "node_seq_id": "529", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "eee;\">\n              <td style=\"", "node_title": "brand_visualization", "node_seq_id": "530", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "padding:12px; font-weight:", "node_title": "brand_visualization", "node_seq_id": "531", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "bold;\">线上销售额</td>\n             ", "node_title": "brand_visualization", "node_seq_id": "532", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <td style=\"padding:12px; text", "node_title": "brand_visualization", "node_seq_id": "533", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-align:right;\">¥19.8", "node_title": "brand_visualization", "node_seq_id": "534", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "亿</td>\n              <", "node_title": "brand_visualization", "node_seq_id": "535", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "td style=\"padding:12px; text-align", "node_title": "brand_visualization", "node_seq_id": "536", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":right; font-weight:bold;\">", "node_title": "brand_visualization", "node_seq_id": "537", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "¥21.79亿", "node_title": "brand_visualization", "node_seq_id": "538", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</td>\n              <td style=\"padding:12", "node_title": "brand_visualization", "node_seq_id": "539", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; text-align:center; color", "node_title": "brand_visualization", "node_seq_id": "540", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#4c", "node_title": "brand_visualization", "node_seq_id": "541", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "af50;\">↑ 10.1%", "node_title": "brand_visualization", "node_seq_id": "542", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</td>\n            </tr", "node_title": "brand_visualization", "node_seq_id": "543", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n            <tr>\n              <td", "node_title": "brand_visualization", "node_seq_id": "544", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"padding:12px; font-weight:", "node_title": "brand_visualization", "node_seq_id": "545", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "bold;\">线上市场份额</td>\n             ", "node_title": "brand_visualization", "node_seq_id": "546", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <td style=\"padding:", "node_title": "brand_visualization", "node_seq_id": "547", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "12px; text-align:right;\">1", "node_title": "brand_visualization", "node_seq_id": "548", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ".9%</td>\n              <td", "node_title": "brand_visualization", "node_seq_id": "549", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"padding:12px", "node_title": "brand_visualization", "node_seq_id": "550", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; text-align:right; font-weight", "node_title": "brand_visualization", "node_seq_id": "551", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":bold;\">2.2%</td", "node_title": "brand_visualization", "node_seq_id": "552", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n              <td style=\"", "node_title": "brand_visualization", "node_seq_id": "553", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "padding:12px; text-align:center; color", "node_title": "brand_visualization", "node_seq_id": "554", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#4caf50;\">+0", "node_title": "brand_visualization", "node_seq_id": "555", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ".3 ppts</td>\n           ", "node_title": "brand_visualization", "node_seq_id": "556", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </tr>\n          </tbody>\n", "node_title": "brand_visualization", "node_seq_id": "557", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        </table>\n      </div>\n     ", "node_title": "brand_visualization", "node_seq_id": "558", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <p style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "559", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":14px; color:#666; margin-top", "node_title": "brand_visualization", "node_seq_id": "560", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":10px", "node_title": "brand_visualization", "node_seq_id": "561", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">数据来源：魔镜洞察 MAT（", "node_title": "brand_visualization", "node_seq_id": "562", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2022-12~202", "node_title": "brand_visualization", "node_seq_id": "563", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "3-11）</p>\n    </div>\n    \n", "node_title": "brand_visualization", "node_seq_id": "564", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "    <div>\n      <h3 style=\"", "node_title": "brand_visualization", "node_seq_id": "565", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:22px", "node_title": "brand_visualization", "node_seq_id": "566", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#333; margin-bottom:15px;\">", "node_title": "brand_visualization", "node_seq_id": "567", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "5.2 资本 & 渠道", "node_title": "brand_visualization", "node_seq_id": "568", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "扩张</h3>\n      <div", "node_title": "brand_visualization", "node_seq_id": "569", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"background:#fff; padding:", "node_title": "brand_visualization", "node_seq_id": "570", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "20px; border-radius:8", "node_title": "brand_visualization", "node_seq_id": "571", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;", "node_title": "brand_visualization", "node_seq_id": "572", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " position:relative;\">\n       ", "node_title": "brand_visualization", "node_seq_id": "573", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"position:absolute; left:", "node_title": "brand_visualization", "node_seq_id": "574", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0; top:0; bottom:", "node_title": "brand_visualization", "node_seq_id": "575", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0; width:4px; background", "node_title": "brand_visualization", "node_seq_id": "576", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":linear-gradient(to bottom,", "node_title": "brand_visualization", "node_seq_id": "577", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " #9c27b0, #e91", "node_title": "brand_visualization", "node_seq_id": "578", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "e63); border-radius:", "node_title": "brand_visualization", "node_seq_id": "579", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "4px 0 0 4px;\"></", "node_title": "brand_visualization", "node_seq_id": "580", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n        <div style", "node_title": "brand_visualization", "node_seq_id": "581", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"padding-left:15px", "node_title": "brand_visualization", "node_seq_id": "582", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n          <div style=\"display:", "node_title": "brand_visualization", "node_seq_id": "583", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "flex; margin-bottom:20", "node_title": "brand_visualization", "node_seq_id": "584", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; padding-bottom:20px;", "node_title": "brand_visualization", "node_seq_id": "585", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-bottom:1px dashed #eee;\">\n           ", "node_title": "brand_visualization", "node_seq_id": "586", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"width", "node_title": "brand_visualization", "node_seq_id": "587", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":100px; font-weight:bold", "node_title": "brand_visualization", "node_seq_id": "588", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#9c27b0;\">2011", "node_title": "brand_visualization", "node_seq_id": "589", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-2014</", "node_title": "brand_visualization", "node_seq_id": "590", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n            <div style=\"flex", "node_title": "brand_visualization", "node_seq_id": "591", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":1;\">\n              <div", "node_title": "brand_visualization", "node_seq_id": "592", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-weight:bold; margin", "node_title": "brand_visualization", "node_seq_id": "593", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:5px;\">全国\"百万大", "node_title": "brand_visualization", "node_seq_id": "594", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "店\"计划</div>\n             ", "node_title": "brand_visualization", "node_seq_id": "595", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <p style=\"font-size:15px;", "node_title": "brand_visualization", "node_seq_id": "596", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " line-height:1.6; color", "node_title": "brand_visualization", "node_seq_id": "597", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#666; margin:0;\">县域", "node_title": "brand_visualization", "node_seq_id": "598", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "百货专柜+加盟", "node_title": "brand_visualization", "node_seq_id": "599", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "店，下沉触达低线市场</", "node_title": "brand_visualization", "node_seq_id": "600", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p>\n            </div>\n          </div>\n", "node_title": "brand_visualization", "node_seq_id": "601", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "          <div style=\"display", "node_title": "brand_visualization", "node_seq_id": "602", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":flex; margin-bottom:20px; padding", "node_title": "brand_visualization", "node_seq_id": "603", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:20px; border-bottom:", "node_title": "brand_visualization", "node_seq_id": "604", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1px dashed #eee;\">\n            <", "node_title": "brand_visualization", "node_seq_id": "605", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"width:100px;", "node_title": "brand_visualization", "node_seq_id": "606", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " font-weight:bold; color:#", "node_title": "brand_visualization", "node_seq_id": "607", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "9c27b0;\">2016-", "node_title": "brand_visualization", "node_seq_id": "608", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2020</div>\n            <", "node_title": "brand_visualization", "node_seq_id": "609", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"flex:1;\">\n", "node_title": "brand_visualization", "node_seq_id": "610", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "              <div style", "node_title": "brand_visualization", "node_seq_id": "611", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-weight:bold; margin-bottom:5px;\">冠名", "node_title": "brand_visualization", "node_seq_id": "612", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "卫视综艺+明星代言", "node_title": "brand_visualization", "node_seq_id": "613", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</div>\n              <p style=\"font", "node_title": "brand_visualization", "node_seq_id": "614", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:15px; line-height:1.", "node_title": "brand_visualization", "node_seq_id": "615", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6", "node_title": "brand_visualization", "node_seq_id": "616", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#666; margin:0;\">蔡", "node_title": "brand_visualization", "node_seq_id": "617", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "依林、郭碧婷等代言，", "node_title": "brand_visualization", "node_seq_id": "618", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "品牌认知破圈</p>\n           ", "node_title": "brand_visualization", "node_seq_id": "619", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n          </div>\n         ", "node_title": "brand_visualization", "node_seq_id": "620", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"display:", "node_title": "brand_visualization", "node_seq_id": "621", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "flex; margin-bottom:20px", "node_title": "brand_visualization", "node_seq_id": "622", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding-bottom:20px; border-bottom:1", "node_title": "brand_visualization", "node_seq_id": "623", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px dashed #eee;\">\n", "node_title": "brand_visualization", "node_seq_id": "624", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "            <div style=\"width:100px; font", "node_title": "brand_visualization", "node_seq_id": "625", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-weight:bold; color", "node_title": "brand_visualization", "node_seq_id": "626", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#9c27b0;\">202", "node_title": "brand_visualization", "node_seq_id": "627", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1-2024</div>\n           ", "node_title": "brand_visualization", "node_seq_id": "628", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"flex:1;\">\n             ", "node_title": "brand_visualization", "node_seq_id": "629", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " <div style=\"font-weight:", "node_title": "brand_visualization", "node_seq_id": "630", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "bold; margin-bottom:5px;\">抖音", "node_title": "brand_visualization", "node_seq_id": "631", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "、快手自播 + KOL ", "node_title": "brand_visualization", "node_seq_id": "632", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "联动</div>\n              <p style=\"font", "node_title": "brand_visualization", "node_seq_id": "633", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:15px; line-height:", "node_title": "brand_visualization", "node_seq_id": "634", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1.6; color:#", "node_title": "brand_visualization", "node_seq_id": "635", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "666; margin:0;\">流量", "node_title": "brand_visualization", "node_seq_id": "636", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "阵地转向短视频，天猫超级品牌日</p>\n", "node_title": "brand_visualization", "node_seq_id": "637", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "            </div>\n          </", "node_title": "brand_visualization", "node_seq_id": "638", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n          <div style=\"display:flex", "node_title": "brand_visualization", "node_seq_id": "639", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n            <div style=\"width:", "node_title": "brand_visualization", "node_seq_id": "640", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "100px; font-weight:", "node_title": "brand_visualization", "node_seq_id": "641", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "bold; color:#9c27b", "node_title": "brand_visualization", "node_seq_id": "642", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0;\">2024-2025</div>\n", "node_title": "brand_visualization", "node_seq_id": "643", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "            <div style=\"flex:", "node_title": "brand_visualization", "node_seq_id": "644", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1;\">\n              <div style=\"", "node_title": "brand_visualization", "node_seq_id": "645", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-weight:bold; margin-bottom:5", "node_title": "brand_visualization", "node_seq_id": "646", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">护肤业务 \"动能肽", "node_title": "brand_visualization", "node_seq_id": "647", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"上线</div>\n              <p style=\"", "node_title": "brand_visualization", "node_seq_id": "648", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:15px; line", "node_title": "brand_visualization", "node_seq_id": "649", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-height:1.6; color:#", "node_title": "brand_visualization", "node_seq_id": "650", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "666; margin:0;\">", "node_title": "brand_visualization", "node_seq_id": "651", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "研发中心升级，开辟第二增长曲线</p", "node_title": "brand_visualization", "node_seq_id": "652", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n            </div>\n          </", "node_title": "brand_visualization", "node_seq_id": "653", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n        </div>\n      </", "node_title": "brand_visualization", "node_seq_id": "654", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n    </", "node_title": "brand_visualization", "node_seq_id": "655", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n  </div>\n\n  <!-- 关键", "node_title": "brand_visualization", "node_seq_id": "656", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "结论与战略建议 -->\n  <div", "node_title": "brand_visualization", "node_seq_id": "657", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"background:#fff8e1", "node_title": "brand_visualization", "node_seq_id": "658", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding:25px; border-radius", "node_title": "brand_visualization", "node_seq_id": "659", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":12px; margin-bottom:30", "node_title": "brand_visualization", "node_seq_id": "660", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px;\">\n    <h2 style=\"", "node_title": "brand_visualization", "node_seq_id": "661", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:28px; color:#", "node_title": "brand_visualization", "node_seq_id": "662", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "ff9800; margin-bottom:20", "node_title": "brand_visualization", "node_seq_id": "663", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; border-bottom:2", "node_title": "brand_visualization", "node_seq_id": "664", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px solid #ff9800; padding-bottom:", "node_title": "brand_visualization", "node_seq_id": "665", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "10px;\">关键结论与战略", "node_title": "brand_visualization", "node_seq_id": "666", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "建议</h2>\n    \n    <div", "node_title": "brand_visualization", "node_seq_id": "667", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"display:flex; flex-wrap", "node_title": "brand_visualization", "node_seq_id": "668", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":wrap; gap:15px;", "node_title": "brand_visualization", "node_seq_id": "669", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " margin-bottom:25px;\">\n      <", "node_title": "brand_visualization", "node_seq_id": "670", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"flex:1; min", "node_title": "brand_visualization", "node_seq_id": "671", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-width:250px; background:#fff", "node_title": "brand_visualization", "node_seq_id": "672", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; padding:20px;", "node_title": "brand_visualization", "node_seq_id": "673", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:8px; box-shadow:0", "node_title": "brand_visualization", "node_seq_id": "674", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 2px 8", "node_title": "brand_visualization", "node_seq_id": "675", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px rgba(0,0,0,0.", "node_title": "brand_visualization", "node_seq_id": "676", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "05); border-top:4px solid", "node_title": "brand_visualization", "node_seq_id": "677", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " #ff9800;\">\n        <", "node_title": "brand_visualization", "node_seq_id": "678", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div style=\"font-size:", "node_title": "brand_visualization", "node_seq_id": "679", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "20px; color:#ff9800; margin", "node_title": "brand_visualization", "node_seq_id": "680", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-bottom:10px;\">1️⃣", "node_title": "brand_visualization", "node_seq_id": "681", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 巩固底妆护城河</div>\n", "node_title": "brand_visualization", "node_seq_id": "682", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "        <p style=\"font", "node_title": "brand_visualization", "node_seq_id": "683", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-size:15px; line-height:1.", "node_title": "brand_visualization", "node_seq_id": "684", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6; color:#666;\">继续加", "node_title": "brand_visualization", "node_seq_id": "685", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "码\"黑磁散粉", "node_title": "brand_visualization", "node_seq_id": "686", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"IP，推出升级替换芯；同时布局", "node_title": "brand_visualization", "node_seq_id": "687", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "男士油皮气垫，抢占新", "node_title": "brand_visualization", "node_seq_id": "688", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "细分</p>\n      </", "node_title": "brand_visualization", "node_seq_id": "689", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n      <div style=\"flex:1", "node_title": "brand_visualization", "node_seq_id": "690", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; min-width:250px; background", "node_title": "brand_visualization", "node_seq_id": "691", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#fff; padding:20", "node_title": "brand_visualization", "node_seq_id": "692", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; border-radius:8px", "node_title": "brand_visualization", "node_seq_id": "693", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; box-shadow:0 2px 8px", "node_title": "brand_visualization", "node_seq_id": "694", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " rgba(0,0,0,", "node_title": "brand_visualization", "node_seq_id": "695", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0.05); border-top", "node_title": "brand_visualization", "node_seq_id": "696", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":4px solid #ff9800", "node_title": "brand_visualization", "node_seq_id": "697", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n        <div style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "698", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; color:#ff9800", "node_title": "brand_visualization", "node_seq_id": "699", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; margin-bottom:10px;\">", "node_title": "brand_visualization", "node_seq_id": "700", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2️⃣ 用\"科研故事\"对冲", "node_title": "brand_visualization", "node_seq_id": "701", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "同质化</div>\n        <p style=\"font-size:15px;", "node_title": "brand_visualization", "node_seq_id": "702", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " line-height:1.", "node_title": "brand_visualization", "node_seq_id": "703", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6; color:#666;\">", "node_title": "brand_visualization", "node_seq_id": "704", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "与上游原料龙头/院校共建\"胜", "node_title": "brand_visualization", "node_seq_id": "705", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "肽联合实验室\"，形成专利壁垒", "node_title": "brand_visualization", "node_seq_id": "706", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "</p>\n      </div>\n", "node_title": "brand_visualization", "node_seq_id": "707", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "      <div style=\"flex:1;", "node_title": "brand_visualization", "node_seq_id": "708", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " min-width:250px; background", "node_title": "brand_visualization", "node_seq_id": "709", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#fff; padding:20px; border", "node_title": "brand_visualization", "node_seq_id": "710", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-radius:8", "node_title": "brand_visualization", "node_seq_id": "711", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; box-shadow:0 2", "node_title": "brand_visualization", "node_seq_id": "712", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px 8px rgba(0,0,", "node_title": "brand_visualization", "node_seq_id": "713", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0,0.05); border-top", "node_title": "brand_visualization", "node_seq_id": "714", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":4px solid #ff9800", "node_title": "brand_visualization", "node_seq_id": "715", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";\">\n        <div style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "716", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; color:#ff980", "node_title": "brand_visualization", "node_seq_id": "717", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0; margin-bottom:10px;\">", "node_title": "brand_visualization", "node_seq_id": "718", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "3️⃣ 护肤业务三年", "node_title": "brand_visualization", "node_seq_id": "719", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "倍增计划</div>\n        <p style=\"", "node_title": "brand_visualization", "node_seq_id": "720", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:15px; line-height", "node_title": "brand_visualization", "node_seq_id": "721", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":1.6; color", "node_title": "brand_visualization", "node_seq_id": "722", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#666;\">目标2026年", "node_title": "brand_visualization", "node_seq_id": "723", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "护肤收入占比≥15%；开发\"修护+", "node_title": "brand_visualization", "node_seq_id": "724", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "彩妆前打底\"全链路", "node_title": "brand_visualization", "node_seq_id": "725", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "产品</p>\n      </", "node_title": "brand_visualization", "node_seq_id": "726", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n      <div style=\"", "node_title": "brand_visualization", "node_seq_id": "727", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "flex:1; min-width:250", "node_title": "brand_visualization", "node_seq_id": "728", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; background:#fff; padding:20", "node_title": "brand_visualization", "node_seq_id": "729", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; border-radius:8px; box", "node_title": "brand_visualization", "node_seq_id": "730", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-shadow:0 2", "node_title": "brand_visualization", "node_seq_id": "731", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px 8px rgba(0", "node_title": "brand_visualization", "node_seq_id": "732", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ",0,0,0.", "node_title": "brand_visualization", "node_seq_id": "733", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "05); border-top:4px solid", "node_title": "brand_visualization", "node_seq_id": "734", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " #ff9800;\">\n        <div style=\"", "node_title": "brand_visualization", "node_seq_id": "735", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "font-size:20px; color", "node_title": "brand_visualization", "node_seq_id": "736", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#ff9800; margin-bottom", "node_title": "brand_visualization", "node_seq_id": "737", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":10px;\">4️⃣ 全渠道矩阵", "node_title": "brand_visualization", "node_seq_id": "738", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "重新平衡</div>\n        <", "node_title": "brand_visualization", "node_seq_id": "739", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p style=\"font-size:15", "node_title": "brand_visualization", "node_seq_id": "740", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; line-height:1.6", "node_title": "brand_visualization", "node_seq_id": "741", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; color:#666;\">通过O", "node_title": "brand_visualization", "node_seq_id": "742", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "2O\"线上下单、线下30min", "node_title": "brand_visualization", "node_seq_id": "743", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "自提\"模式，盘活县域", "node_title": "brand_visualization", "node_seq_id": "744", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "加盟店</p>\n     ", "node_title": "brand_visualization", "node_seq_id": "745", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n      <div style=\"flex:1", "node_title": "brand_visualization", "node_seq_id": "746", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ";", "node_title": "brand_visualization", "node_seq_id": "747", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " min-width:250px; background:#fff; padding", "node_title": "brand_visualization", "node_seq_id": "748", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":20px; border-radius:", "node_title": "brand_visualization", "node_seq_id": "749", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "8px; box-shadow:0 2", "node_title": "brand_visualization", "node_seq_id": "750", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px 8px rgba(", "node_title": "brand_visualization", "node_seq_id": "751", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "0,0,0,0.05", "node_title": "brand_visualization", "node_seq_id": "752", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "); border-top:4px", "node_title": "brand_visualization", "node_seq_id": "753", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " solid #ff9800;\">\n        <div", "node_title": "brand_visualization", "node_seq_id": "754", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " style=\"font-size:20px; color:#", "node_title": "brand_visualization", "node_seq_id": "755", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "ff9800; margin-bottom", "node_title": "brand_visualization", "node_seq_id": "756", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":10px;\">5️⃣ ", "node_title": "brand_visualization", "node_seq_id": "757", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "强化ESG & 可持续</div>\n        <", "node_title": "brand_visualization", "node_seq_id": "758", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "p style=\"font-size:15", "node_title": "brand_visualization", "node_seq_id": "759", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "px; line-height:1.", "node_title": "brand_visualization", "node_seq_id": "760", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "6; color:#666;\">202", "node_title": "brand_visualization", "node_seq_id": "761", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "5年实现明星SKU 50%", "node_title": "brand_visualization", "node_seq_id": "762", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "使用可回收包材；增设\"绿色彩", "node_title": "brand_visualization", "node_seq_id": "763", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "妆\"标签</p>\n      </", "node_title": "brand_visualization", "node_seq_id": "764", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "div>\n    </div", "node_title": "brand_visualization", "node_seq_id": "765", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ">\n    \n    <div style=\"background:#fff;", "node_title": "brand_visualization", "node_seq_id": "766", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " padding:20px;", "node_title": "brand_visualization", "node_seq_id": "767", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " border-radius:8px; text-align:center;\">\n", "node_title": "brand_visualization", "node_seq_id": "768", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "      <p style=\"font-size", "node_title": "brand_visualization", "node_seq_id": "769", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":18px; line-height:", "node_title": "brand_visualization", "node_seq_id": "770", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "1.7; margin:0; font", "node_title": "brand_visualization", "node_seq_id": "771", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "-weight:bold; color:#333;\">\n       ", "node_title": "brand_visualization", "node_seq_id": "772", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " \"卡姿兰", "node_title": "brand_visualization", "node_seq_id": "773", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "已经凭借底妆技术和", "node_title": "brand_visualization", "node_seq_id": "774", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "大众价位带在红海中占据战略高地", "node_title": "brand_visualization", "node_seq_id": "775", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "，下一站是", "node_title": "brand_visualization", "node_seq_id": "776", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "科研＋护肤双轮驱动，进一步夯实国", "node_title": "brand_visualization", "node_seq_id": "777", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "货彩妆第一梯队的位置", "node_title": "brand_visualization", "node_seq_id": "778", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "\"\n      </p>\n    </div>\n ", "node_title": "brand_visualization", "node_seq_id": "779", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " </div>\n\n  <!--", "node_title": "brand_visualization", "node_seq_id": "780", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " 数据来源 -->\n  <div style", "node_title": "brand_visualization", "node_seq_id": "781", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "=\"font-size:14px; color", "node_title": "brand_visualization", "node_seq_id": "782", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": ":#999; text-align:center; margin-top:40px; padding-top:20px", "node_title": "brand_visualization", "node_seq_id": "783", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "; border-top:1px", "node_title": "brand_visualization", "node_seq_id": "784", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": " solid #eee;\">\n    <p>数据来源", "node_title": "brand_visualization", "node_seq_id": "785", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "：魔镜洞察、欧睿国际", "node_title": "brand_visualization", "node_seq_id": "786", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "、公开财报等 | 报告日期", "node_title": "brand_visualization", "node_seq_id": "787", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "：2024年</p>\n", "node_title": "brand_visualization", "node_seq_id": "788", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "  </div>\n</div>\n```", "node_title": "brand_visualization", "node_seq_id": "789", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "", "node_title": "brand_visualization", "node_seq_id": "790", "node_is_finish": true, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "```html\n<div style=\"width: 100%; max-width: 750px; margin: 0 auto; padding: 0 30px; box-sizing: border-box; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; color: #333; background: #fff;\">\n\n  <!-- 标题区域 -->\n  <div style=\"text-align: center; margin-bottom: 30px;\">\n    <h1 style=\"font-size: 32px; font-weight: 700; color: #d81e06; margin-bottom: 10px;\">彩妆专题研究报告</h1>\n    <p style=\"font-size: 16px; color: #666;\">2025年中国彩妆市场深度分析</p>\n  </div>\n\n  <!-- 行业概述 -->\n  <div style=\"margin-bottom: 40px; background: #f9f9f9; padding: 20px; border-radius: 8px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 15px;\">一、行业概述</h2>\n    <div style=\"display: flex; flex-wrap: wrap; gap: 20px;\">\n      <div style=\"flex: 1; min-width: 300px;\">\n        <p style=\"margin-bottom: 15px; line-height: 1.6;\">彩妆(Color Cosmetics)通常指用于面部、唇部、眼部、美甲等部位的上妆及修饰产品。</p>\n        <div style=\"background: #fff; padding: 15px; border-radius: 6px; margin-bottom: 15px;\">\n          <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n            <span style=\"font-size: 24px; margin-right: 10px;\">📊</span>\n            <span style=\"font-weight: 600;\">2023年中国彩妆市场规模</span>\n          </div>\n          <p style=\"font-size: 28px; color: #d81e06; font-weight: 700; margin: 0;\">618亿</p>\n          <p style=\"color: #666; margin-top: 5px;\">人民币，同比增长11%</p>\n        </div>\n      </div>\n      <div style=\"flex: 1; min-width: 300px;\">\n        <div style=\"background: #fff; padding: 15px; border-radius: 6px; margin-bottom: 15px;\">\n          <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n            <span style=\"font-size: 24px; margin-right: 10px;\">🌍</span>\n            <span style=\"font-weight: 600;\">全球市场规模</span>\n          </div>\n          <p style=\"font-size: 28px; color: #d81e06; font-weight: 700; margin: 0;\">790.6亿</p>\n          <p style=\"color: #666; margin-top: 5px;\">美元(2025年预测)</p>\n        </div>\n        <p style=\"margin-bottom: 10px; line-height: 1.6;\">预计2025-2034年复合增长率(CAGR)为7.23%，2034年可达1481.9亿美元。</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- 市场趋势与预测 -->\n  <div style=\"margin-bottom: 40px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 20px;\">二、市场趋势与预测</h2>\n    \n    <!-- 图表1 -->\n    <div style=\"background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n      <h3 style=\"font-size: 18px; color: #d81e06; margin-bottom: 15px;\">中国市场增长预测</h3>\n      <div style=\"height: 250px; position: relative;\">\n        <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 600 250\" style=\"overflow: visible;\">\n          <!-- X轴 -->\n          <line x1=\"50\" y1=\"200\" x2=\"550\" y2=\"200\" stroke=\"#999\" stroke-width=\"1.5\"/>\n          <!-- Y轴 -->\n          <line x1=\"50\" y1=\"200\" x2=\"50\" y2=\"50\" stroke=\"#999\" stroke-width=\"1.5\"/>\n          \n          <!-- 刻度线 -->\n          <line x1=\"50\" y1=\"180\" x2=\"550\" y2=\"180\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"160\" x2=\"550\" y2=\"160\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"140\" x2=\"550\" y2=\"140\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"120\" x2=\"550\" y2=\"120\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"100\" x2=\"550\" y2=\"100\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"80\" x2=\"550\" y2=\"80\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"60\" x2=\"550\" y2=\"60\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          \n          <!-- 数据点 -->\n          <circle cx=\"100\" cy=\"180\" r=\"5\" fill=\"#d81e06\"/>\n          <circle cx=\"200\" cy=\"160\" r=\"5\" fill=\"#d81e06\"/>\n          <circle cx=\"300\" cy=\"140\" r=\"5\" fill=\"#d81e06\"/>\n          <circle cx=\"400\" cy=\"120\" r=\"5\" fill=\"#d81e06\"/>\n          <circle cx=\"500\" cy=\"100\" r=\"5\" fill=\"#d81e06\"/>\n          \n          <!-- 连接线 -->\n          <path d=\"M100 180 L200 160 L300 140 L400 120 L500 100\" stroke=\"#d81e06\" stroke-width=\"2\" fill=\"none\"/>\n          \n          <!-- 标签 -->\n          <text x=\"100\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2023</text>\n          <text x=\"200\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2025</text>\n          <text x=\"300\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2027</text>\n          <text x=\"400\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2029</text>\n          <text x=\"500\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2030</text>\n          \n          <!-- 数值标签 -->\n          <text x=\"100\" y=\"170\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">618亿</text>\n          <text x=\"200\" y=\"150\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">720亿</text>\n          <text x=\"300\" y=\"130\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">800亿</text>\n          <text x=\"400\" y=\"110\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">880亿</text>\n          <text x=\"500\" y=\"90\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">950亿</text>\n          \n          <!-- 标题 -->\n          <text x=\"300\" y=\"30\" text-anchor=\"middle\" font-size=\"14\" font-weight=\"bold\">中国彩妆市场规模预测(人民币)</text>\n        </svg>\n      </div>\n      <p style=\"margin-top: 10px; font-size: 14px; color: #666; text-align: center;\">预计2025-2030年CAGR保持在8-9%，2030年市场规模有望突破950亿元人民币</p>\n    </div>\n    \n    <!-- 消费人群 -->\n    <div style=\"display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px;\">\n      <div style=\"flex: 1; min-width: 200px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n          <span style=\"font-size: 24px; margin-right: 10px;\">👩‍🎤</span>\n          <span style=\"font-weight: 600;\">Z世代</span>\n        </div>\n        <p style=\"margin: 0; line-height: 1.5; font-size: 14px;\">追求个性化妆容与即时分享，偏好遮瑕、高光等精细品类</p>\n      </div>\n      <div style=\"flex: 1; min-width: 200px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n          <span style=\"font-size: 24px; margin-right: 10px;\">👨‍🎤</span>\n          <span style=\"font-weight: 600;\">男性彩妆</span>\n        </div>\n        <p style=\"margin: 0; line-height: 1.5; font-size: 14px;\">18-25岁人群占男士彩妆消费者59.5%，护肤-彩妆一体化产品增长最快</p>\n      </div>\n      <div style=\"flex: 1; min-width: 200px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n          <span style=\"font-size: 24px; margin-right: 10px;\">📱</span>\n          <span style=\"font-weight: 600;\">直播电商</span>\n        </div>\n        <p style=\"margin: 0; line-height: 1.5; font-size: 14px;\">2024年上半年抖音彩妆月均播放量超400亿，直播月均商品点击破10亿</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- 技术发展与创新 -->\n  <div style=\"margin-bottom: 40px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 20px;\">三、技术发展与创新</h2>\n    \n    <div style=\"display: flex; flex-wrap: wrap; gap: 20px;\">\n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 20px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #d81e06; margin-bottom: 15px;\">数字化与AI赋能</h3>\n        <div style=\"display: flex; align-items: center; margin-bottom: 15px;\">\n          <span style=\"font-size: 40px; margin-right: 15px;\">🤖</span>\n          <div>\n            <p style=\"font-weight: 600; margin: 0;\">AI虚拟试妆</p>\n            <p style=\"margin: 5px 0 0; font-size: 14px; color: #666;\">2024年被列为影响美妆与时尚的五大AI趋势之一</p>\n          </div>\n        </div>\n        <p style=\"margin-bottom: 15px; line-height: 1.6;\">Florasis(花西子)在杭州落成6480㎡\"智能工厂\"，通过AI缺陷检测、机器人包装与实时质量监控，实现年产5000万件、能耗下降15%</p>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 20px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #d81e06; margin-bottom: 15px;\">可持续创新</h3>\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"margin-right: 15px;\">\n            <div style=\"width: 60px; height: 60px; background: #e8f5e9; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;\">\n              <span style=\"font-size: 24px;\">♻️</span>\n            </div>\n            <p style=\"text-align: center; font-size: 12px; margin: 0;\">配方</p>\n          </div>\n          <div style=\"margin-right: 15px;\">\n            <div style=\"width: 60px; height: 60px; background: #e8f5e9; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;\">\n              <span style=\"font-size: 24px;\">🔄</span>\n            </div>\n            <p style=\"text-align: center; font-size: 12px; margin: 0;\">包装</p>\n          </div>\n          <div>\n            <div style=\"width: 60px; height: 60px; background: #e8f5e9; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;\">\n              <span style=\"font-size: 24px;\">🌱</span>\n            </div>\n            <p style=\"text-align: center; font-size: 12px; margin: 0;\">认证</p>\n          </div>\n        </div>\n        <p style=\"margin-bottom: 0; line-height: 1.6;\">水基、无酒精配方；PCR(再生塑料)、可替换芯包装；合成云母或经RMI认证的天然云母以取代童工风险高的开采链</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- 卡姿兰案例 -->\n  <div style=\"margin-bottom: 40px; background: #f9f9f9; padding: 20px; border-radius: 8px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 20px;\">卡姿兰案例研究</h2>\n    \n    <div style=\"display: flex; flex-wrap: wrap; gap: 20px; align-items: center;\">\n      <div style=\"flex: 1; min-width: 300px;\">\n        <h3 style=\"font-size: 20px; color: #d81e06; margin-bottom: 15px;\">从渠道覆盖到底妆专家</h3>\n        <ul style=\"padding-left: 20px; margin-bottom: 15px;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">黑磁/光磁/安瓶/小猫系列覆盖从粉底液到散粉</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">\"一妆到底24H全场景持妆\"对接年轻消费者全天候需求</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">天猫+抖音双平台，加密县域百货专柜</li>\n          <li style=\"line-height: 1.5;\">自建研发中心＋与大学联合实验室，共享皮肤数据</li>\n        </ul>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px;\">\n        <div style=\"background: #fff; padding: 15px; border-radius: 8px; text-align: center;\">\n          <p style=\"font-weight: 600; margin-bottom: 10px;\">2023年底妆TOP10品牌线上占比</p>\n          <svg width=\"100%\" height=\"200\" viewBox=\"0 0 300 200\">\n            <circle cx=\"150\" cy=\"100\" r=\"80\" fill=\"none\" stroke=\"#eee\" stroke-width=\"15\"/>\n            <path d=\"M150 20 A80 80 0 0 1 150 180\" fill=\"none\" stroke=\"#d81e06\" stroke-width=\"15\" stroke-linecap=\"round\"/>\n            <text x=\"150\" y=\"100\" text-anchor=\"middle\" font-size=\"32\" font-weight=\"bold\" fill=\"#d81e06\">43.3%</text>\n            <text x=\"150\" y=\"130\" text-anchor=\"middle\" font-size=\"14\" fill=\"#666\">卡姿兰市场份额</text>\n          </svg>\n          <p style=\"font-size: 14px; color: #666; margin-top: 5px;\">被称\"大众底妆之王\"</p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- SWOT分析 -->\n  <div style=\"margin-bottom: 40px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 20px;\">SWOT分析</h2>\n    \n    <div style=\"display: flex; flex-wrap: wrap; gap: 15px;\">\n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #4caf50; margin-bottom: 15px; display: flex; align-items: center;\">\n          <span style=\"margin-right: 8px;\">✅</span>优势 (S)\n        </h3>\n        <ul style=\"padding-left: 20px; margin: 0;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">消费基数大、年轻化</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">数字渠道成本较低、转化快</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">政策支持国货与创新</li>\n          <li style=\"line-height: 1.5;\">技术升级(AI、智能工厂)提升效率</li>\n        </ul>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #f44336; margin-bottom: 15px; display: flex; align-items: center;\">\n          <span style=\"margin-right: 8px;\">❌</span>劣势 (W)\n        </h3>\n        <ul style=\"padding-left: 20px; margin: 0;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">市场集中度低，价格战激烈</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">产品同质化高、研发投入不足</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">原料依赖进口、汇率风险</li>\n          <li style=\"line-height: 1.5;\">品牌生命周期短，新锐易被复制</li>\n        </ul>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #2196f3; margin-bottom: 15px; display: flex; align-items: center;\">\n          <span style=\"margin-right: 8px;\">🔍</span>机会 (O)\n        </h3>\n        <ul style=\"padding-left: 20px; margin: 0;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">男性化妆、银发经济等蓝海</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">可持续消费与ESG驱动差异化</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">海外并购与跨境电商</li>\n          <li style=\"line-height: 1.5;\">科技+医美联动产品创新</li>\n        </ul>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #ff9800; margin-bottom: 15px; display: flex; align-items: center;\">\n          <span style=\"margin-right: 8px;\">⚠️</span>威胁 (T)\n        </h3>\n        <ul style=\"padding-left: 20px; margin: 0;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">宏观经济放缓消费降级</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">监管趋严、CSAR罚款与召回风险</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">供应链ESG负面事件引发舆情</li>\n          <li style=\"line-height: 1.5;\">新媒体流量红利见顶、获客成本上升</li>\n        </ul>\n      </div>\n    </div>\n  </div>\n\n  <!-- 结论 -->\n  <div style=\"background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 30px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 15px;\">结论</h2>\n    <p style=\"line-height: 1.8; margin-bottom: 15px;\">彩妆行业在<b>\"技术革新 × 消费升级 × 监管进步\"</b>的三重驱动下，正迈向高质量、可持续的新周期。</p>\n    <div style=\"display: flex; align-items: center; background: #fff; padding: 15px; border-radius: 8px;\">\n      <span style=\"font-size: 40px; margin-right: 15px;\">🚀</span>\n      <div>\n        <p style=\"margin: 0; font-weight: 600; color: #d81e06;\">1000亿级增量红利</p>\n        <p style=\"margin: 5px 0 0; font-size: 14px; color: #666;\">企业若能在研发创新、供应链透明、数字生态三大核心能力上形成闭环，将有望在2025-2030年的竞争浪潮中脱颖而出</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- 页脚 -->\n  <div style=\"text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #999; font-size: 14px;\">\n    <p>© 2025 卡姿兰市场研究中心 | 数据来源: 公开资料整理</p>\n  </div>\n</div>\n```", "node_title": "industry_visualization", "node_seq_id": "0", "node_is_finish": false, "token": null, "ext": null, "__internal__event": "Message"}, {"content": "", "node_title": "industry_visualization", "node_seq_id": "1", "node_is_finish": true, "token": null, "ext": null, "__internal__event": "Message"}, {"debugUrl": {"debug_url": "https://www.coze.cn/work_flow?execute_id=7524305403984494601&space_id=7509711081779593253&workflow_id=7523810726952566824&execute_mode=2"}, "__internal__event": "Done"}, "[DONE]"], "brand_visualization": {"content": "```html\n<div style=\"max-width:750px; margin:0 auto; padding:0 30px; font-family:'PingFang SC','Microsoft YaHei',sans-serif; color:#333;\">\n  <!-- 标题部分 -->\n  <div style=\"text-align:center; margin-bottom:40px;\">\n    <h1 style=\"font-size:36px; font-weight:600; color:#d4237a; margin-bottom:15px;\">《卡姿兰品牌现状及竞争分析报告》</h1>\n    <p style=\"font-size:16px; color:#666;\">中国彩妆市场深度分析 | 2024年最新数据</p>\n  </div>\n\n  <!-- 市场定位与目标客户 -->\n  <div style=\"background:#f9f3f7; padding:25px; border-radius:12px; margin-bottom:30px;\">\n    <h2 style=\"font-size:28px; color:#d4237a; margin-bottom:20px; border-bottom:2px solid #d4237a; padding-bottom:10px;\">一、市场定位与目标客户</h2>\n    \n    <div style=\"display:flex; margin-bottom:20px;\">\n      <div style=\"flex:1; padding-right:15px;\">\n        <h3 style=\"font-size:22px; color:#333; margin-bottom:15px;\">1.1 品牌定位</h3>\n        <p style=\"font-size:16px; line-height:1.8; margin-bottom:15px;\">\"中国时尚彩妆领导品牌\"——卡姿兰自2001年创立以来，始终聚焦大众价位段（单品主力价位 ¥90 – ¥200），以\"活出经典 Living Classic\"为核心理念。</p>\n        <p style=\"font-size:16px; line-height:1.8; background:#fff; padding:15px; border-radius:8px; border-left:4px solid #d4237a;\">\n          <strong>关键数据：</strong>最新一整年线上销售额21.79亿元、市场份额2.2%，位列全网TOP3\n        </p>\n      </div>\n      <div style=\"flex:1; padding-left:15px;\">\n        <h3 style=\"font-size:22px; color:#333; margin-bottom:15px;\">1.2 目标客群</h3>\n        <div style=\"background:#fff; padding:15px; border-radius:8px;\">\n          <div style=\"display:flex; margin-bottom:10px; border-bottom:1px dashed #eee; padding-bottom:10px;\">\n            <div style=\"width:80px; font-weight:bold;\">年龄</div>\n            <div>18-35岁为核心，Z世代≈55%</div>\n          </div>\n          <div style=\"display:flex; margin-bottom:10px; border-bottom:1px dashed #eee; padding-bottom:10px;\">\n            <div style=\"width:80px; font-weight:bold;\">收入</div>\n            <div>月入4k-10k元；二线及以上城市为主</div>\n          </div>\n          <div style=\"display:flex;\">\n            <div style=\"width:80px; font-weight:bold;\">场景</div>\n            <div>通勤妆、社交场景妆、直播间\"种草\"</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 竞争对手分析 -->\n  <div style=\"background:#f5f7fa; padding:25px; border-radius:12px; margin-bottom:30px;\">\n    <h2 style=\"font-size:28px; color:#2a5caa; margin-bottom:20px; border-bottom:2px solid #2a5caa; padding-bottom:10px;\">二、竞争对手分析</h2>\n    \n    <div style=\"margin-bottom:25px;\">\n      <h3 style=\"font-size:22px; color:#333; margin-bottom:15px;\">2.1 主要竞争者及座次</h3>\n      <div style=\"overflow-x:auto;\">\n        <table style=\"width:100%; border-collapse:collapse; background:#fff; border-radius:8px;\">\n          <thead>\n            <tr style=\"background:#2a5caa; color:#fff;\">\n              <th style=\"padding:12px; text-align:left;\">阵营</th>\n              <th style=\"padding:12px; text-align:left;\">代表品牌</th>\n              <th style=\"padding:12px; text-align:right;\">销售额/份额</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr style=\"border-bottom:1px solid #eee;\">\n              <td style=\"padding:12px; font-weight:bold;\">国货领跑</td>\n              <td style=\"padding:12px;\">花西子</td>\n              <td style=\"padding:12px; text-align:right;\">¥23.40亿/2.4%</td>\n            </tr>\n            <tr style=\"border-bottom:1px solid #eee;\">\n              <td style=\"padding:12px;\"></td>\n              <td style=\"padding:12px; font-weight:bold;\">卡姿兰</td>\n              <td style=\"padding:12px; text-align:right;\">¥21.79亿/2.2%</td>\n            </tr>\n            <tr style=\"border-bottom:1px solid #eee;\">\n              <td style=\"padding:12px;\">国际高端</td>\n              <td style=\"padding:12px;\">YSL Beauty</td>\n              <td style=\"padding:12px; text-align:right;\">¥27.80亿/2.8%</td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n    \n    <div>\n      <h3 style=\"font-size:22px; color:#333; margin-bottom:15px;\">2.2 SWOT 摘要</h3>\n      <div style=\"display:flex; flex-wrap:wrap; gap:15px;\">\n        <div style=\"flex:1; min-width:250px; background:#fff; padding:15px; border-radius:8px; border-top:4px solid #4caf50;\">\n          <h4 style=\"font-size:18px; color:#4caf50; margin-bottom:10px;\">优势(Strength)</h4>\n          <p style=\"font-size:15px; line-height:1.6;\">底妆产品矩阵齐全；大众价位段拥有统治级份额；供应链自有工厂保障成本。</p>\n        </div>\n        <div style=\"flex:1; min-width:250px; background:#fff; padding:15px; border-radius:8px; border-top:4px solid #ff5722;\">\n          <h4 style=\"font-size:18px; color:#ff5722; margin-bottom:10px;\">劣势(Weakness)</h4>\n          <p style=\"font-size:15px; line-height:1.6;\">国际高端心智仍弱；彩妆科研话语权不足；新品叙事需更深度。</p>\n        </div>\n        <div style=\"flex:1; min-width:250px; background:#fff; padding:15px; border-radius:8px; border-top:4px solid #2196f3;\">\n          <h4 style=\"font-size:18px; color:#2196f3; margin-bottom:10px;\">机会(Opportunity)</h4>\n          <p style=\"font-size:15px; line-height:1.6;\">社媒增量在抖音，国货美妆政策红利；男士彩妆蓝海。</p>\n        </div>\n        <div style=\"flex:1; min-width:250px; background:#fff; padding:15px; border-radius:8px; border-top:4px solid #9c27b0;\">\n          <h4 style=\"font-size:18px; color:#9c27b0; margin-bottom:10px;\">威胁(Threat)</h4>\n          <p style=\"font-size:15px; line-height:1.6;\">国际大牌向下探价 + 新锐国货持续上新；护肤科技融入彩妆后技术门槛抬升。</p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 波特五力模型 -->\n  <div style=\"background:#f1f8e9; padding:25px; border-radius:12px; margin-bottom:30px;\">\n    <h2 style=\"font-size:28px; color:#4caf50; margin-bottom:20px; border-bottom:2px solid #4caf50; padding-bottom:10px;\">三、波特五力模型</h2>\n    \n    <div style=\"overflow-x:auto;\">\n      <table style=\"width:100%; border-collapse:collapse; background:#fff; border-radius:8px;\">\n        <thead>\n          <tr style=\"background:#4caf50; color:#fff;\">\n            <th style=\"padding:12px; text-align:left;\">力量</th>\n            <th style=\"padding:12px; text-align:center;\">强度</th>\n            <th style=\"padding:12px; text-align:left;\">原因 & 说明</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr style=\"border-bottom:1px solid #eee;\">\n            <td style=\"padding:12px; font-weight:bold;\">供应商议价力</td>\n            <td style=\"padding:12px; text-align:center;\">中-</td>\n            <td style=\"padding:12px;\">彩妆包材与原料日趋标准化；但高端粉体/香精仍掌握在日韩/欧洲供应商手中</td>\n          </tr>\n          <tr style=\"border-bottom:1px solid #eee;\">\n            <td style=\"padding:12px; font-weight:bold;\">买方议价力</td>\n            <td style=\"padding:12px; text-align:center;\">高</td>\n            <td style=\"padding:12px;\">消费者信息透明、比价心态强；直播间\"低价心智\"强化</td>\n          </tr>\n          <tr style=\"border-bottom:1px solid #eee;\">\n            <td style=\"padding:12px; font-weight:bold;\">新进入者威胁</td>\n            <td style=\"padding:12px; text-align:center;\">中+</td>\n            <td style=\"padding:12px;\">直播渠道降低流量门槛；但体系化底妆研发 & 线下渠道铺设需重投入</td>\n          </tr>\n          <tr style=\"border-bottom:1px solid #eee;\">\n            <td style=\"padding:12px; font-weight:bold;\">替代品威胁</td>\n            <td style=\"padding:12px; text-align:center;\">中</td>\n            <td style=\"padding:12px;\">半永久美妆、医美\"轻改颜\"对日常彩妆替代度上升</td>\n          </tr>\n          <tr>\n            <td style=\"padding:12px; font-weight:bold;\">行业竞争强度</td>\n            <td style=\"padding:12px; text-align:center;\">高</td>\n            <td style=\"padding:12px;\">3000+ 活跃彩妆注册品牌；新品更替周期 ≈ 3-6 个月</td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n    \n    <div style=\"background:#fff; margin-top:20px; padding:20px; border-radius:8px; border-left:4px solid #4caf50;\">\n      <p style=\"font-size:16px; line-height:1.7; margin:0;\">\n        <strong>结论：</strong>竞争激烈、买方强势，品牌若无技术型差异化/社群心智，容易陷入价格战。卡姿兰的底妆技术与价格带双护城河目前仍然稳固。\n      </p>\n    </div>\n  </div>\n\n  <!-- 产品与服务 -->\n  <div style=\"background:#e8f4f8; padding:25px; border-radius:12px; margin-bottom:30px;\">\n    <h2 style=\"font-size:28px; color:#2196f3; margin-bottom:20px; border-bottom:2px solid #2196f3; padding-bottom:10px;\">四、产品与服务</h2>\n    \n    <div style=\"margin-bottom:25px;\">\n      <h3 style=\"font-size:22px; color:#333; margin-bottom:15px;\">4.1 产品族谱</h3>\n      <div style=\"display:flex; flex-wrap:wrap; gap:15px;\">\n        <div style=\"flex:1; min-width:250px; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n          <div style=\"font-size:20px; color:#2196f3; margin-bottom:10px;\">💄 底妆线</div>\n          <p style=\"font-size:15px; line-height:1.6; color:#666;\">\"黑磁散粉\"\"小奶猫气垫\"\"安瓶粉底液\"等覆盖从学生党到轻奢客群</p>\n        </div>\n        <div style=\"flex:1; min-width:250px; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n          <div style=\"font-size:20px; color:#2196f3; margin-bottom:10px;\">💋 唇妆线</div>\n          <p style=\"font-size:15px; line-height:1.6; color:#666;\">主打\"低饱和显白\"与\"护唇精华\"概念，联名包装频繁</p>\n        </div>\n        <div style=\"flex:1; min-width:250px; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n          <div style=\"font-size:20px; color:#2196f3; margin-bottom:10px;\">👁️ 眼妆线</div>\n          <p style=\"font-size:15px; line-height:1.6; color:#666;\">大眼睛睫毛膏、吸睛眼影盘、零触感眼线笔</p>\n        </div>\n        <div style=\"flex:1; min-width:250px; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.05);\">\n          <div style=\"font-size:20px; color:#2196f3; margin-bottom:10px;\">🧴 护肤线</div>\n          <p style=\"font-size:15px; line-height:1.6; color:#666;\">2025年推出动能肽面霜、精华液，切入功效护肤赛道</p>\n        </div>\n      </div>\n    </div>\n    \n    <div>\n      <h3 style=\"font-size:22px; color:#333; margin-bottom:15px;\">4.2 产品创新范式</h3>\n      <div style=\"background:#fff; padding:20px; border-radius:8px;\">\n        <div style=\"display:flex; align-items:center; margin-bottom:15px;\">\n          <div style=\"width:40px; height:40px; background:#e3f2fd; border-radius:50%; display:flex; align-items:center; justify-content:center; margin-right:15px; color:#2196f3; font-size:20px;\">1</div>\n          <div style=\"flex:1;\">\n            <div style=\"font-weight:bold; margin-bottom:5px;\">\"彩妆+护肤\"</div>\n            <p style=\"font-size:15px; line-height:1.6; color:#666; margin:0;\">在粉底与气垫中加入胜肽、烟酰胺等成分，响应\"养肤底妆\"潮流</p>\n          </div>\n        </div>\n        <div style=\"display:flex; align-items:center; margin-bottom:15px;\">\n          <div style=\"width:40px; height:40px; background:#e3f2fd; border-radius:50%; display:flex; align-items:center; justify-content:center; margin-right:15px; color:#2196f3; font-size:20px;\">2</div>\n          <div style=\"flex:1;\">\n            <div style=\"font-weight:bold; margin-bottom:5px;\">小包装&多色号</div>\n            <p style=\"font-size:15px; line-height:1.6; color:#666; margin:0;\">适配\"混合猫\"气垫可拆卸芯，减少库存压力</p>\n          </div>\n        </div>\n        <div style=\"display:flex; align-items:center;\">\n          <div style=\"width:40px; height:40px; background:#e3f2fd; border-radius:50%; display:flex; align-items:center; justify-content:center; margin-right:15px; color:#2196f3; font-size:20px;\">3</div>\n          <div style=\"flex:1;\">\n            <div style=\"font-weight:bold; margin-bottom:5px;\">环保包装</div>\n            <p style=\"font-size:15px; line-height:1.6; color:#666; margin:0;\">部分新品已使用PCR材料；建议继续强化ESG叙事</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 财务表现与扩张策略 -->\n  <div style=\"background:#f3e5f5; padding:25px; border-radius:12px; margin-bottom:30px;\">\n    <h2 style=\"font-size:28px; color:#9c27b0; margin-bottom:20px; border-bottom:2px solid #9c27b0; padding-bottom:10px;\">五、财务表现与扩张策略</h2>\n    \n    <div style=\"margin-bottom:25px;\">\n      <h3 style=\"font-size:22px; color:#333; margin-bottom:15px;\">5.1 核心财务指标（公开口径）</h3>\n      <div style=\"overflow-x:auto;\">\n        <table style=\"width:100%; border-collapse:collapse; background:#fff; border-radius:8px;\">\n          <thead>\n            <tr style=\"background:#9c27b0; color:#fff;\">\n              <th style=\"padding:12px; text-align:left;\">指标</th>\n              <th style=\"padding:12px; text-align:right;\">2022年</th>\n              <th style=\"padding:12px; text-align:right;\">2023年</th>\n              <th style=\"padding:12px; text-align:center;\">同比</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr style=\"border-bottom:1px solid #eee;\">\n              <td style=\"padding:12px; font-weight:bold;\">线上销售额</td>\n              <td style=\"padding:12px; text-align:right;\">¥19.8亿</td>\n              <td style=\"padding:12px; text-align:right; font-weight:bold;\">¥21.79亿</td>\n              <td style=\"padding:12px; text-align:center; color:#4caf50;\">↑ 10.1%</td>\n            </tr>\n            <tr>\n              <td style=\"padding:12px; font-weight:bold;\">线上市场份额</td>\n              <td style=\"padding:12px; text-align:right;\">1.9%</td>\n              <td style=\"padding:12px; text-align:right; font-weight:bold;\">2.2%</td>\n              <td style=\"padding:12px; text-align:center; color:#4caf50;\">+0.3 ppts</td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n      <p style=\"font-size:14px; color:#666; margin-top:10px;\">数据来源：魔镜洞察 MAT（2022-12~2023-11）</p>\n    </div>\n    \n    <div>\n      <h3 style=\"font-size:22px; color:#333; margin-bottom:15px;\">5.2 资本 & 渠道扩张</h3>\n      <div style=\"background:#fff; padding:20px; border-radius:8px; position:relative;\">\n        <div style=\"position:absolute; left:0; top:0; bottom:0; width:4px; background:linear-gradient(to bottom, #9c27b0, #e91e63); border-radius:4px 0 0 4px;\"></div>\n        <div style=\"padding-left:15px;\">\n          <div style=\"display:flex; margin-bottom:20px; padding-bottom:20px; border-bottom:1px dashed #eee;\">\n            <div style=\"width:100px; font-weight:bold; color:#9c27b0;\">2011-2014</div>\n            <div style=\"flex:1;\">\n              <div style=\"font-weight:bold; margin-bottom:5px;\">全国\"百万大店\"计划</div>\n              <p style=\"font-size:15px; line-height:1.6; color:#666; margin:0;\">县域百货专柜+加盟店，下沉触达低线市场</p>\n            </div>\n          </div>\n          <div style=\"display:flex; margin-bottom:20px; padding-bottom:20px; border-bottom:1px dashed #eee;\">\n            <div style=\"width:100px; font-weight:bold; color:#9c27b0;\">2016-2020</div>\n            <div style=\"flex:1;\">\n              <div style=\"font-weight:bold; margin-bottom:5px;\">冠名卫视综艺+明星代言</div>\n              <p style=\"font-size:15px; line-height:1.6; color:#666; margin:0;\">蔡依林、郭碧婷等代言，品牌认知破圈</p>\n            </div>\n          </div>\n          <div style=\"display:flex; margin-bottom:20px; padding-bottom:20px; border-bottom:1px dashed #eee;\">\n            <div style=\"width:100px; font-weight:bold; color:#9c27b0;\">2021-2024</div>\n            <div style=\"flex:1;\">\n              <div style=\"font-weight:bold; margin-bottom:5px;\">抖音、快手自播 + KOL 联动</div>\n              <p style=\"font-size:15px; line-height:1.6; color:#666; margin:0;\">流量阵地转向短视频，天猫超级品牌日</p>\n            </div>\n          </div>\n          <div style=\"display:flex;\">\n            <div style=\"width:100px; font-weight:bold; color:#9c27b0;\">2024-2025</div>\n            <div style=\"flex:1;\">\n              <div style=\"font-weight:bold; margin-bottom:5px;\">护肤业务 \"动能肽\"上线</div>\n              <p style=\"font-size:15px; line-height:1.6; color:#666; margin:0;\">研发中心升级，开辟第二增长曲线</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 关键结论与战略建议 -->\n  <div style=\"background:#fff8e1; padding:25px; border-radius:12px; margin-bottom:30px;\">\n    <h2 style=\"font-size:28px; color:#ff9800; margin-bottom:20px; border-bottom:2px solid #ff9800; padding-bottom:10px;\">关键结论与战略建议</h2>\n    \n    <div style=\"display:flex; flex-wrap:wrap; gap:15px; margin-bottom:25px;\">\n      <div style=\"flex:1; min-width:250px; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.05); border-top:4px solid #ff9800;\">\n        <div style=\"font-size:20px; color:#ff9800; margin-bottom:10px;\">1️⃣ 巩固底妆护城河</div>\n        <p style=\"font-size:15px; line-height:1.6; color:#666;\">继续加码\"黑磁散粉\"IP，推出升级替换芯；同时布局男士油皮气垫，抢占新细分</p>\n      </div>\n      <div style=\"flex:1; min-width:250px; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.05); border-top:4px solid #ff9800;\">\n        <div style=\"font-size:20px; color:#ff9800; margin-bottom:10px;\">2️⃣ 用\"科研故事\"对冲同质化</div>\n        <p style=\"font-size:15px; line-height:1.6; color:#666;\">与上游原料龙头/院校共建\"胜肽联合实验室\"，形成专利壁垒</p>\n      </div>\n      <div style=\"flex:1; min-width:250px; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.05); border-top:4px solid #ff9800;\">\n        <div style=\"font-size:20px; color:#ff9800; margin-bottom:10px;\">3️⃣ 护肤业务三年倍增计划</div>\n        <p style=\"font-size:15px; line-height:1.6; color:#666;\">目标2026年护肤收入占比≥15%；开发\"修护+彩妆前打底\"全链路产品</p>\n      </div>\n      <div style=\"flex:1; min-width:250px; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.05); border-top:4px solid #ff9800;\">\n        <div style=\"font-size:20px; color:#ff9800; margin-bottom:10px;\">4️⃣ 全渠道矩阵重新平衡</div>\n        <p style=\"font-size:15px; line-height:1.6; color:#666;\">通过O2O\"线上下单、线下30min自提\"模式，盘活县域加盟店</p>\n      </div>\n      <div style=\"flex:1; min-width:250px; background:#fff; padding:20px; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.05); border-top:4px solid #ff9800;\">\n        <div style=\"font-size:20px; color:#ff9800; margin-bottom:10px;\">5️⃣ 强化ESG & 可持续</div>\n        <p style=\"font-size:15px; line-height:1.6; color:#666;\">2025年实现明星SKU 50%使用可回收包材；增设\"绿色彩妆\"标签</p>\n      </div>\n    </div>\n    \n    <div style=\"background:#fff; padding:20px; border-radius:8px; text-align:center;\">\n      <p style=\"font-size:18px; line-height:1.7; margin:0; font-weight:bold; color:#333;\">\n        \"卡姿兰已经凭借底妆技术和大众价位带在红海中占据战略高地，下一站是科研＋护肤双轮驱动，进一步夯实国货彩妆第一梯队的位置\"\n      </p>\n    </div>\n  </div>\n\n  <!-- 数据来源 -->\n  <div style=\"font-size:14px; color:#999; text-align:center; margin-top:40px; padding-top:20px; border-top:1px solid #eee;\">\n    <p>数据来源：魔镜洞察、欧睿国际、公开财报等 | 报告日期：2024年</p>\n  </div>\n</div>\n```"}, "industry_visualization": {"content": "```html\n<div style=\"width: 100%; max-width: 750px; margin: 0 auto; padding: 0 30px; box-sizing: border-box; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; color: #333; background: #fff;\">\n\n  <!-- 标题区域 -->\n  <div style=\"text-align: center; margin-bottom: 30px;\">\n    <h1 style=\"font-size: 32px; font-weight: 700; color: #d81e06; margin-bottom: 10px;\">彩妆专题研究报告</h1>\n    <p style=\"font-size: 16px; color: #666;\">2025年中国彩妆市场深度分析</p>\n  </div>\n\n  <!-- 行业概述 -->\n  <div style=\"margin-bottom: 40px; background: #f9f9f9; padding: 20px; border-radius: 8px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 15px;\">一、行业概述</h2>\n    <div style=\"display: flex; flex-wrap: wrap; gap: 20px;\">\n      <div style=\"flex: 1; min-width: 300px;\">\n        <p style=\"margin-bottom: 15px; line-height: 1.6;\">彩妆(Color Cosmetics)通常指用于面部、唇部、眼部、美甲等部位的上妆及修饰产品。</p>\n        <div style=\"background: #fff; padding: 15px; border-radius: 6px; margin-bottom: 15px;\">\n          <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n            <span style=\"font-size: 24px; margin-right: 10px;\">📊</span>\n            <span style=\"font-weight: 600;\">2023年中国彩妆市场规模</span>\n          </div>\n          <p style=\"font-size: 28px; color: #d81e06; font-weight: 700; margin: 0;\">618亿</p>\n          <p style=\"color: #666; margin-top: 5px;\">人民币，同比增长11%</p>\n        </div>\n      </div>\n      <div style=\"flex: 1; min-width: 300px;\">\n        <div style=\"background: #fff; padding: 15px; border-radius: 6px; margin-bottom: 15px;\">\n          <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n            <span style=\"font-size: 24px; margin-right: 10px;\">🌍</span>\n            <span style=\"font-weight: 600;\">全球市场规模</span>\n          </div>\n          <p style=\"font-size: 28px; color: #d81e06; font-weight: 700; margin: 0;\">790.6亿</p>\n          <p style=\"color: #666; margin-top: 5px;\">美元(2025年预测)</p>\n        </div>\n        <p style=\"margin-bottom: 10px; line-height: 1.6;\">预计2025-2034年复合增长率(CAGR)为7.23%，2034年可达1481.9亿美元。</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- 市场趋势与预测 -->\n  <div style=\"margin-bottom: 40px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 20px;\">二、市场趋势与预测</h2>\n    \n    <!-- 图表1 -->\n    <div style=\"background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n      <h3 style=\"font-size: 18px; color: #d81e06; margin-bottom: 15px;\">中国市场增长预测</h3>\n      <div style=\"height: 250px; position: relative;\">\n        <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 600 250\" style=\"overflow: visible;\">\n          <!-- X轴 -->\n          <line x1=\"50\" y1=\"200\" x2=\"550\" y2=\"200\" stroke=\"#999\" stroke-width=\"1.5\"/>\n          <!-- Y轴 -->\n          <line x1=\"50\" y1=\"200\" x2=\"50\" y2=\"50\" stroke=\"#999\" stroke-width=\"1.5\"/>\n          \n          <!-- 刻度线 -->\n          <line x1=\"50\" y1=\"180\" x2=\"550\" y2=\"180\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"160\" x2=\"550\" y2=\"160\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"140\" x2=\"550\" y2=\"140\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"120\" x2=\"550\" y2=\"120\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"100\" x2=\"550\" y2=\"100\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"80\" x2=\"550\" y2=\"80\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          <line x1=\"50\" y1=\"60\" x2=\"550\" y2=\"60\" stroke=\"#ddd\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n          \n          <!-- 数据点 -->\n          <circle cx=\"100\" cy=\"180\" r=\"5\" fill=\"#d81e06\"/>\n          <circle cx=\"200\" cy=\"160\" r=\"5\" fill=\"#d81e06\"/>\n          <circle cx=\"300\" cy=\"140\" r=\"5\" fill=\"#d81e06\"/>\n          <circle cx=\"400\" cy=\"120\" r=\"5\" fill=\"#d81e06\"/>\n          <circle cx=\"500\" cy=\"100\" r=\"5\" fill=\"#d81e06\"/>\n          \n          <!-- 连接线 -->\n          <path d=\"M100 180 L200 160 L300 140 L400 120 L500 100\" stroke=\"#d81e06\" stroke-width=\"2\" fill=\"none\"/>\n          \n          <!-- 标签 -->\n          <text x=\"100\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2023</text>\n          <text x=\"200\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2025</text>\n          <text x=\"300\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2027</text>\n          <text x=\"400\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2029</text>\n          <text x=\"500\" y=\"220\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">2030</text>\n          \n          <!-- 数值标签 -->\n          <text x=\"100\" y=\"170\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">618亿</text>\n          <text x=\"200\" y=\"150\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">720亿</text>\n          <text x=\"300\" y=\"130\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">800亿</text>\n          <text x=\"400\" y=\"110\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">880亿</text>\n          <text x=\"500\" y=\"90\" text-anchor=\"middle\" font-size=\"12\" fill=\"#d81e06\">950亿</text>\n          \n          <!-- 标题 -->\n          <text x=\"300\" y=\"30\" text-anchor=\"middle\" font-size=\"14\" font-weight=\"bold\">中国彩妆市场规模预测(人民币)</text>\n        </svg>\n      </div>\n      <p style=\"margin-top: 10px; font-size: 14px; color: #666; text-align: center;\">预计2025-2030年CAGR保持在8-9%，2030年市场规模有望突破950亿元人民币</p>\n    </div>\n    \n    <!-- 消费人群 -->\n    <div style=\"display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px;\">\n      <div style=\"flex: 1; min-width: 200px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n          <span style=\"font-size: 24px; margin-right: 10px;\">👩‍🎤</span>\n          <span style=\"font-weight: 600;\">Z世代</span>\n        </div>\n        <p style=\"margin: 0; line-height: 1.5; font-size: 14px;\">追求个性化妆容与即时分享，偏好遮瑕、高光等精细品类</p>\n      </div>\n      <div style=\"flex: 1; min-width: 200px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n          <span style=\"font-size: 24px; margin-right: 10px;\">👨‍🎤</span>\n          <span style=\"font-weight: 600;\">男性彩妆</span>\n        </div>\n        <p style=\"margin: 0; line-height: 1.5; font-size: 14px;\">18-25岁人群占男士彩妆消费者59.5%，护肤-彩妆一体化产品增长最快</p>\n      </div>\n      <div style=\"flex: 1; min-width: 200px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n          <span style=\"font-size: 24px; margin-right: 10px;\">📱</span>\n          <span style=\"font-weight: 600;\">直播电商</span>\n        </div>\n        <p style=\"margin: 0; line-height: 1.5; font-size: 14px;\">2024年上半年抖音彩妆月均播放量超400亿，直播月均商品点击破10亿</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- 技术发展与创新 -->\n  <div style=\"margin-bottom: 40px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 20px;\">三、技术发展与创新</h2>\n    \n    <div style=\"display: flex; flex-wrap: wrap; gap: 20px;\">\n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 20px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #d81e06; margin-bottom: 15px;\">数字化与AI赋能</h3>\n        <div style=\"display: flex; align-items: center; margin-bottom: 15px;\">\n          <span style=\"font-size: 40px; margin-right: 15px;\">🤖</span>\n          <div>\n            <p style=\"font-weight: 600; margin: 0;\">AI虚拟试妆</p>\n            <p style=\"margin: 5px 0 0; font-size: 14px; color: #666;\">2024年被列为影响美妆与时尚的五大AI趋势之一</p>\n          </div>\n        </div>\n        <p style=\"margin-bottom: 15px; line-height: 1.6;\">Florasis(花西子)在杭州落成6480㎡\"智能工厂\"，通过AI缺陷检测、机器人包装与实时质量监控，实现年产5000万件、能耗下降15%</p>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 20px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #d81e06; margin-bottom: 15px;\">可持续创新</h3>\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"margin-right: 15px;\">\n            <div style=\"width: 60px; height: 60px; background: #e8f5e9; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;\">\n              <span style=\"font-size: 24px;\">♻️</span>\n            </div>\n            <p style=\"text-align: center; font-size: 12px; margin: 0;\">配方</p>\n          </div>\n          <div style=\"margin-right: 15px;\">\n            <div style=\"width: 60px; height: 60px; background: #e8f5e9; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;\">\n              <span style=\"font-size: 24px;\">🔄</span>\n            </div>\n            <p style=\"text-align: center; font-size: 12px; margin: 0;\">包装</p>\n          </div>\n          <div>\n            <div style=\"width: 60px; height: 60px; background: #e8f5e9; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;\">\n              <span style=\"font-size: 24px;\">🌱</span>\n            </div>\n            <p style=\"text-align: center; font-size: 12px; margin: 0;\">认证</p>\n          </div>\n        </div>\n        <p style=\"margin-bottom: 0; line-height: 1.6;\">水基、无酒精配方；PCR(再生塑料)、可替换芯包装；合成云母或经RMI认证的天然云母以取代童工风险高的开采链</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- 卡姿兰案例 -->\n  <div style=\"margin-bottom: 40px; background: #f9f9f9; padding: 20px; border-radius: 8px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 20px;\">卡姿兰案例研究</h2>\n    \n    <div style=\"display: flex; flex-wrap: wrap; gap: 20px; align-items: center;\">\n      <div style=\"flex: 1; min-width: 300px;\">\n        <h3 style=\"font-size: 20px; color: #d81e06; margin-bottom: 15px;\">从渠道覆盖到底妆专家</h3>\n        <ul style=\"padding-left: 20px; margin-bottom: 15px;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">黑磁/光磁/安瓶/小猫系列覆盖从粉底液到散粉</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">\"一妆到底24H全场景持妆\"对接年轻消费者全天候需求</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">天猫+抖音双平台，加密县域百货专柜</li>\n          <li style=\"line-height: 1.5;\">自建研发中心＋与大学联合实验室，共享皮肤数据</li>\n        </ul>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px;\">\n        <div style=\"background: #fff; padding: 15px; border-radius: 8px; text-align: center;\">\n          <p style=\"font-weight: 600; margin-bottom: 10px;\">2023年底妆TOP10品牌线上占比</p>\n          <svg width=\"100%\" height=\"200\" viewBox=\"0 0 300 200\">\n            <circle cx=\"150\" cy=\"100\" r=\"80\" fill=\"none\" stroke=\"#eee\" stroke-width=\"15\"/>\n            <path d=\"M150 20 A80 80 0 0 1 150 180\" fill=\"none\" stroke=\"#d81e06\" stroke-width=\"15\" stroke-linecap=\"round\"/>\n            <text x=\"150\" y=\"100\" text-anchor=\"middle\" font-size=\"32\" font-weight=\"bold\" fill=\"#d81e06\">43.3%</text>\n            <text x=\"150\" y=\"130\" text-anchor=\"middle\" font-size=\"14\" fill=\"#666\">卡姿兰市场份额</text>\n          </svg>\n          <p style=\"font-size: 14px; color: #666; margin-top: 5px;\">被称\"大众底妆之王\"</p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- SWOT分析 -->\n  <div style=\"margin-bottom: 40px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 20px;\">SWOT分析</h2>\n    \n    <div style=\"display: flex; flex-wrap: wrap; gap: 15px;\">\n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #4caf50; margin-bottom: 15px; display: flex; align-items: center;\">\n          <span style=\"margin-right: 8px;\">✅</span>优势 (S)\n        </h3>\n        <ul style=\"padding-left: 20px; margin: 0;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">消费基数大、年轻化</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">数字渠道成本较低、转化快</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">政策支持国货与创新</li>\n          <li style=\"line-height: 1.5;\">技术升级(AI、智能工厂)提升效率</li>\n        </ul>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #f44336; margin-bottom: 15px; display: flex; align-items: center;\">\n          <span style=\"margin-right: 8px;\">❌</span>劣势 (W)\n        </h3>\n        <ul style=\"padding-left: 20px; margin: 0;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">市场集中度低，价格战激烈</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">产品同质化高、研发投入不足</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">原料依赖进口、汇率风险</li>\n          <li style=\"line-height: 1.5;\">品牌生命周期短，新锐易被复制</li>\n        </ul>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #2196f3; margin-bottom: 15px; display: flex; align-items: center;\">\n          <span style=\"margin-right: 8px;\">🔍</span>机会 (O)\n        </h3>\n        <ul style=\"padding-left: 20px; margin: 0;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">男性化妆、银发经济等蓝海</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">可持续消费与ESG驱动差异化</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">海外并购与跨境电商</li>\n          <li style=\"line-height: 1.5;\">科技+医美联动产品创新</li>\n        </ul>\n      </div>\n      \n      <div style=\"flex: 1; min-width: 300px; background: #f9f9f9; padding: 15px; border-radius: 8px;\">\n        <h3 style=\"font-size: 18px; color: #ff9800; margin-bottom: 15px; display: flex; align-items: center;\">\n          <span style=\"margin-right: 8px;\">⚠️</span>威胁 (T)\n        </h3>\n        <ul style=\"padding-left: 20px; margin: 0;\">\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">宏观经济放缓消费降级</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">监管趋严、CSAR罚款与召回风险</li>\n          <li style=\"margin-bottom: 8px; line-height: 1.5;\">供应链ESG负面事件引发舆情</li>\n          <li style=\"line-height: 1.5;\">新媒体流量红利见顶、获客成本上升</li>\n        </ul>\n      </div>\n    </div>\n  </div>\n\n  <!-- 结论 -->\n  <div style=\"background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 30px;\">\n    <h2 style=\"font-size: 24px; border-left: 4px solid #d81e06; padding-left: 10px; margin-bottom: 15px;\">结论</h2>\n    <p style=\"line-height: 1.8; margin-bottom: 15px;\">彩妆行业在<b>\"技术革新 × 消费升级 × 监管进步\"</b>的三重驱动下，正迈向高质量、可持续的新周期。</p>\n    <div style=\"display: flex; align-items: center; background: #fff; padding: 15px; border-radius: 8px;\">\n      <span style=\"font-size: 40px; margin-right: 15px;\">🚀</span>\n      <div>\n        <p style=\"margin: 0; font-weight: 600; color: #d81e06;\">1000亿级增量红利</p>\n        <p style=\"margin: 5px 0 0; font-size: 14px; color: #666;\">企业若能在研发创新、供应链透明、数字生态三大核心能力上形成闭环，将有望在2025-2030年的竞争浪潮中脱颖而出</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- 页脚 -->\n  <div style=\"text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #999; font-size: 14px;\">\n    <p>© 2025 卡姿兰市场研究中心 | 数据来源: 公开资料整理</p>\n  </div>\n</div>\n```"}}
import { memo } from 'react'
import { KeepAliveRouteCtx, KeepAliveRouteCtxVal } from './KeepAliveRouteCtx'

function _KeepAliveProvider({
  children,
}: KeepAliveProviderProps) {
  return <KeepAliveRouteCtx.Provider
    value={ KeepAliveRouteCtxVal }
  >
    {children}
  </KeepAliveRouteCtx.Provider>
}

export const KeepAliveProvider = memo<KeepAliveProviderProps>(_KeepAliveProvider)
KeepAliveProvider.displayName = 'KeepAliveProvider'

export type KeepAliveProviderProps = {
  children?: React.ReactNode
}
